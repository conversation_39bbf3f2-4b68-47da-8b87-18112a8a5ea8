<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b2cd344a-2f19-44b4-9adb-0fccee262d2f" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/MarsCodeWorkspaceAppSettings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/MarsCodeWorkspaceAppSettings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/compiler.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/logs/multi-datasource-dev.log" beforeDir="false" afterPath="$PROJECT_DIR$/logs/multi-datasource-dev.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/MultiDataSourceApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/MultiDataSourceApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/config/MyBatisDataSourceConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/config/MyBatisDataSourceConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/voyage/dto/MaritimeCrawlRequestDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/voyage/dto/MaritimeCrawlRequestDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/voyage/dto/MaritimeCrawlResultDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/voyage/dto/MaritimeCrawlResultDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-prod.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-prod.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/voyage/MaritimeInfoMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/voyage/MaritimeInfoMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/application-prod.yml" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/application-prod.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/MultiDataSourceApplication.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/MultiDataSourceApplication.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/common/util/JwtUtil$ClaimsResolver.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/common/util/JwtUtil$ClaimsResolver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/common/util/JwtUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/common/util/JwtUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/DataSourceProperties$HikariConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/DataSourceProperties$HikariConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/DataSourceProperties.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/DataSourceProperties.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/DynamicDataSource.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/DynamicDataSource.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/DynamicDataSourceManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/DynamicDataSourceManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/GlobalExceptionHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/GlobalExceptionHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/JwtAuthenticationFilter$JwtUserPrincipal.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/JwtAuthenticationFilter$JwtUserPrincipal.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/JwtAuthenticationFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/JwtAuthenticationFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/MyBatisDataSourceConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/MyBatisDataSourceConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/OpenApiConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/OpenApiConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/PasswordEncoderConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/PasswordEncoderConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/SecurityConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/SecurityConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/controller/DataSourceController.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/controller/DataSourceController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/controller/HealthController.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/controller/HealthController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/controller/PublicController.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/controller/PublicController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/controller/SqlExecutorController.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/controller/SqlExecutorController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/controller/CrewController.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/controller/CrewController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/controller/OilVoyageConsumptionController.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/controller/OilVoyageConsumptionController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/dto/VoyageConsumptionSummaryDTO$OilConsumptionDetailDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/dto/VoyageConsumptionSummaryDTO$OilConsumptionDetailDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/dto/VoyageConsumptionSummaryDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/dto/VoyageConsumptionSummaryDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/entity/CrewInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/entity/CrewInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/mapper/CrewMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/mapper/CrewMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/mapper/OilVoyageConsumptionMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/mapper/OilVoyageConsumptionMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/service/CrewService.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/service/CrewService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/service/OilVoyageConsumptionService.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/service/OilVoyageConsumptionService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/service/impl/CrewServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/service/impl/CrewServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/service/impl/OilVoyageConsumptionServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/crew/service/impl/OilVoyageConsumptionServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/model/ApiResponse.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/model/ApiResponse.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/model/DataSourceConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/model/DataSourceConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/model/SqlRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/model/SqlRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/model/SqlResponse.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/model/SqlResponse.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/service/SqlExecutorService.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/service/SqlExecutorService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/voyage/controller/VoyageController.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/voyage/controller/VoyageController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/voyage/entity/VoyageInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/voyage/entity/VoyageInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/voyage/mapper/VoyageMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/voyage/mapper/VoyageMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/voyage/service/VoyageService.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/voyage/service/VoyageService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/voyage/service/impl/VoyageServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/voyage/service/impl/VoyageServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="F:\learing\apache-maven-3.6.1\mavenRepository" />
        <option name="mavenHome" value="F:/learing/apache-maven-3.6.1/apache-maven-3.6.1" />
        <option name="useMavenConfig" value="true" />
        <option name="userSettingsFile" value="F:\learing\apache-maven-3.6.1\apache-maven-3.6.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="workspaceImportEnabled" value="true" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="ProjectId" id="31E3kCFBE8Oj1g5Q1DeLX6QRJbu" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="false" />
    <option name="showLibraryContents" value="true" />
    <option name="showScratchesAndConsoles" value="false" />
    <option name="showVisibilityIcons" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "last_opened_file_path": "D:/augmentSpace",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Global Libraries",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "configurable.group.build",
    "spring.configuration.checksum": "96e4b2703bc3d3c2b2f618cf5e956881",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="MultiDataSourceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="multi-datasource-api" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.multidatasource.MultiDataSourceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\augmentSpace" />
          <option name="myCopyRoot" value="D:\augmentSpace" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\augmentSpace" />
          <option name="myCopyRoot" value="D:\augmentSpace" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b2cd344a-2f19-44b4-9adb-0fccee262d2f" name="Changes" comment="" />
      <created>1755075550203</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755075550203</updated>
      <workItem from="1755075553439" duration="2606000" />
      <workItem from="1755078220646" duration="3159000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>