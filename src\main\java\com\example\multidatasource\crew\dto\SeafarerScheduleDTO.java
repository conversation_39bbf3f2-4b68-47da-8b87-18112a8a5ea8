package com.example.multidatasource.crew.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 船员查询汇总DTO
 */
@Data
@Schema(description = "船员查询汇总信息")
public class SeafarerScheduleDTO {
    
    @Schema(description = "姓名（支持模糊查询）")
    private String name;
    
    @Schema(description = "职位")
    private String position;
    
    @Schema(description = "状态")
    private String status;
    
    @Schema(description = "电话（支持模糊查询）")
    private String phone;
}
