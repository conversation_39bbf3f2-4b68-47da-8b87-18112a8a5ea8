<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e3713aad-0f6f-4249-8f2d-27d453ba6c40" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/crew/controller/SeafarerScheduleController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/crew/controller/SeafarerScheduleController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/crew/mapper/SeafarerScheduleMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/crew/mapper/SeafarerScheduleMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/crew/service/SeafarerScheduleService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/crew/service/SeafarerScheduleService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/crew/service/impl/SeafarerScheduleServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/crew/service/impl/SeafarerScheduleServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/crew/SeafarerScheduleMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/crew/SeafarerScheduleMapper.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHome" value="F:/learing/apache-maven-3.6.1/apache-maven-3.6.1" />
        <option name="useMavenConfig" value="true" />
        <option name="userSettingsFile" value="F:\learing\apache-maven-3.6.1\apache-maven-3.6.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectId" id="2yzQndRJxrZ03OWCyjudPjDAcqy" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="false" />
    <option name="showLibraryContents" value="true" />
    <option name="showScratchesAndConsoles" value="false" />
    <option name="showVisibilityIcons" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "last_opened_file_path": "D:/augmentSpace/src/main/resources/mapper/crew",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Modules",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "vcs.Subversion",
    "spring.configuration.checksum": "96e4b2703bc3d3c2b2f618cf5e956881",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\augmentSpace\src\main\resources\mapper\crew" />
      <recent name="D:\augmentSpace\src\main\java\com\example\multidatasource\crew\entity" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.example.multidatasource.crew.entity" />
      <recent name="com.example.multidatasource.crew.dto" />
      <recent name="com.example.multidatasource.crew.mapper" />
      <recent name="com.example.multidatasource.crew.service.impl" />
      <recent name="com.example.multidatasource.crew.service" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.MultiDataSourceApplication">
    <configuration name="PasswordTest.testPasswordMatching" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="multi-datasource-api" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.multidatasource.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.example.multidatasource" />
      <option name="MAIN_CLASS_NAME" value="com.example.multidatasource.PasswordTest" />
      <option name="METHOD_NAME" value="testPasswordMatching" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MultiDataSourceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="multi-datasource-api" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.multidatasource.MultiDataSourceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.PasswordTest.testPasswordMatching" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\augmentSpace" />
          <option name="myCopyRoot" value="D:\augmentSpace" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\augmentSpace" />
          <option name="myCopyRoot" value="D:\augmentSpace" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="e3713aad-0f6f-4249-8f2d-27d453ba6c40" name="Changes" comment="" />
      <created>1750835089151</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750835089151</updated>
      <workItem from="1750835094034" duration="10912000" />
      <workItem from="1752628014044" duration="45822000" />
      <workItem from="1752828790599" duration="6830000" />
    </task>
    <task id="LOCAL-00001" summary="测试提交">
      <created>1752828861894</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752828861894</updated>
    </task>
    <task id="LOCAL-00002" summary="测试提交">
      <created>1752828916703</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752828916703</updated>
    </task>
    <task id="LOCAL-00003" summary="测试提交">
      <created>1752829048148</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752829048148</updated>
    </task>
    <task id="LOCAL-00004" summary="测试提交">
      <created>1753081840382</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753081840382</updated>
    </task>
    <task id="LOCAL-00005" summary="编码船员列表后提交">
      <created>1753174375672</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753174375672</updated>
    </task>
    <task id="LOCAL-00006" summary="编码船员列表后提交">
      <created>1753176780535</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753176780535</updated>
    </task>
    <task id="LOCAL-00007" summary="编码船员列表后提交">
      <created>1753178329880</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753178329880</updated>
    </task>
    <option name="localTasksCounter" value="8" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="首次提交" />
    <MESSAGE value="测试提交" />
    <MESSAGE value="编码船员列表后提交" />
    <option name="LAST_COMMIT_MESSAGE" value="编码船员列表后提交" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>