package com.example.multidatasource.config;

import com.example.multidatasource.common.config.DataSourceContextHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

/**
 * 动态数据源路由
 * 根据当前线程的数据源标识来决定使用哪个数据源
 * 支持MyBatis多数据源切换
 */
public class DynamicDataSource extends AbstractRoutingDataSource {
    
    private static final Logger logger = LoggerFactory.getLogger(DynamicDataSource.class);
    
    @Override
    protected Object determineCurrentLookupKey() {
        String dataSourceType = DataSourceContextHolder.getDataSourceType();
        logger.debug("Current data source: " + dataSourceType);
        return dataSourceType;
    }
    
    @Override
    public void afterPropertiesSet() {
        super.afterPropertiesSet();
        logger.info("Dynamic DataSource initialized with target data sources: " + getResolvedDataSources().keySet());
    }
}
