package com.example.multidatasource.auth.service;

import com.example.multidatasource.common.dto.LoginRequest;
import com.example.multidatasource.common.dto.LoginResponse;
import com.example.multidatasource.common.dto.RegisterRequest;

/**
 * 认证服务接口
 */
public interface AuthService {

    /**
     * 用户登录
     */
    LoginResponse login(LoginRequest loginRequest, String clientIp);

    /**
     * 用户注册
     */
    boolean register(RegisterRequest registerRequest);

    /**
     * 用户登出
     */
    boolean logout(String token);

    /**
     * 刷新令牌
     */
    LoginResponse refreshToken(String token);

    /**
     * 验证令牌
     */
    boolean validateToken(String token);

    /**
     * 从令牌获取用户名
     */
    String getUsernameFromToken(String token);

    /**
     * 从令牌获取用户ID
     */
    Long getUserIdFromToken(String token);
}
