package com.example.multidatasource.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * SQL执行响应模型
 */
@Data
@Schema(description = "SQL执行响应")
public class SqlResponse {
    
    @Schema(description = "执行是否成功", example = "true")
    private Boolean success;
    
    @Schema(description = "响应消息", example = "查询成功")
    private String message;
    
    @Schema(description = "查询结果数据")
    private List<Map<String, Object>> data;
    
    @Schema(description = "影响的行数(用于INSERT/UPDATE/DELETE)", example = "1")
    private Integer affectedRows;
    
    @Schema(description = "总记录数(用于分页查询)", example = "100")
    private Long totalCount;
    
    @Schema(description = "当前页码", example = "1")
    private Integer currentPage;
    
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize;
    
    @Schema(description = "总页数", example = "10")
    private Integer totalPages;
    
    @Schema(description = "执行时间(毫秒)", example = "150")
    private Long executionTime;
    
    @Schema(description = "错误信息")
    private String error;
    
    /**
     * 创建成功响应
     */
    public static SqlResponse success(String message) {
        SqlResponse response = new SqlResponse();
        response.setSuccess(true);
        response.setMessage(message);
        return response;
    }
    
    /**
     * 创建查询成功响应
     */
    public static SqlResponse success(List<Map<String, Object>> data, String message) {
        SqlResponse response = success(message);
        response.setData(data);
        return response;
    }
    
    /**
     * 创建更新成功响应
     */
    public static SqlResponse success(int affectedRows, String message) {
        SqlResponse response = success(message);
        response.setAffectedRows(affectedRows);
        return response;
    }
    
    /**
     * 创建失败响应
     */
    public static SqlResponse error(String message, String error) {
        SqlResponse response = new SqlResponse();
        response.setSuccess(false);
        response.setMessage(message);
        response.setError(error);
        return response;
    }
}
