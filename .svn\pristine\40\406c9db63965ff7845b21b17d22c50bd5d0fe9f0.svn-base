package com.example.multidatasource.crew.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 证书等级匹配工具类
 */
@Slf4j
public class CertificateLevelMatcher {

    /**
     * 四大头职务
     */
    private static final Set<String> MAJOR_POSITIONS = new HashSet<>(Arrays.asList(
            "船长", "大副", "轮机长", "大管轮"
    ));

    /**
     * 证书类别优先级（从高到低）
     */
    private static final String[] CERTIFICATE_PRIORITY = {"甲", "乙", "丙", "丁"};

    /**
     * 甲类证书等级（按等级从高到低排序）
     */
    private static final String[] JIA_LEVELS = {"甲一", "甲二", "甲类"};

    /**
     * 乙类证书等级（按等级从高到低排序）
     */
    private static final String[] YI_LEVELS = {"乙一", "乙类"};

    /**
     * 丙类证书等级（按等级从高到低排序）
     */
    private static final String[] BING_LEVELS = {"丙一", "丙二", "丙类"};

    /**
     * 丁类证书等级（按等级从高到低排序）
     */
    private static final String[] DING_LEVELS = {"丁类", "其他"};

    /**
     * 判断船员证书等级是否满足船舶要求
     *
     * @param seafarerCertLevel 船员证书等级名称
     * @param applyDutyName     申请职务名称
     * @param vesselInfo        船舶信息
     * @return 是否匹配
     */
    public static boolean isLevelMatched(String seafarerCertLevel, String applyDutyName, Map<String, Object> vesselInfo) {
        if (!StringUtils.hasText(seafarerCertLevel) || !StringUtils.hasText(applyDutyName) || vesselInfo == null) {
            log.warn("参数不完整: seafarerCertLevel={}, applyDutyName={}, vesselInfo={}", 
                    seafarerCertLevel, applyDutyName, vesselInfo);
            return false;
        }

        try {
            // 1. 计算船舶要求的证书等级：如甲一、丙二等
            String requiredLevel = calculateRequiredCertificateLevel(applyDutyName, vesselInfo);
            if (!StringUtils.hasText(requiredLevel)) {
                log.warn("无法计算船舶要求的证书等级: applyDutyName={}, vesselInfo={}", applyDutyName, vesselInfo);
                return false;
            }

            // 2. 执行匹配逻辑
            boolean matched = checkCertificateCompatibility(seafarerCertLevel, requiredLevel, applyDutyName);
            
            log.debug("证书等级匹配结果: 船员证书={}, 船舶要求={}, 职务={}, 匹配结果={}", 
                    seafarerCertLevel, requiredLevel, applyDutyName, matched);
            
            return matched;

        } catch (Exception e) {
            log.error("证书等级匹配异常", e);
            return false;
        }
    }

    /**
     * 计算船舶要求的证书等级
     *
     * @param applyDutyName 申请职务名称
     * @param vesselInfo    船舶信息
     * @return 要求的证书等级
     */
    private static String calculateRequiredCertificateLevel(String applyDutyName, Map<String, Object> vesselInfo) {
        // 1. 确定航区类别
        Integer navigationArea = (Integer) vesselInfo.get("navigationArea");
        if (navigationArea == null) {
            return null;
        }
        String areaType = navigationArea == 1 ? "丙" : "甲";

        // 2. 判断是否为四大头职务
        if (!MAJOR_POSITIONS.contains(applyDutyName)) {
            // 非四大头职务，只需航区匹配
            return areaType + "类";
        }

        // 3. 四大头职务需要精确等级匹配
        String levelType = calculateVesselLevel(applyDutyName, vesselInfo);
        if (!StringUtils.hasText(levelType)) {
            return areaType + "类"; // 降级为类别匹配
        }

        return areaType + levelType; // 如: "甲一", "丙二"
    }

    /**
     * 计算船舶等级（一类/二类）
     *
     * @param applyDutyName 申请职务名称
     * @param vesselInfo    船舶信息
     * @return 船舶等级
     */
    private static String calculateVesselLevel(String applyDutyName, Map<String, Object> vesselInfo) {
        try {
            if ("船长".equals(applyDutyName) || "大副".equals(applyDutyName)) {
                // 船长、大副：基于总吨位判断
                Object grossTonageObj = vesselInfo.get("grossTonage");
                if (grossTonageObj != null) {
                    double grossTonage = Double.parseDouble(grossTonageObj.toString());
                    return grossTonage >= 3000 ? "一" : "二";
                }
            } else if ("轮机长".equals(applyDutyName) || "大管轮".equals(applyDutyName)) {
                // 轮机长、大管轮：基于主机功率判断
                Object mainEnginePowerObj = vesselInfo.get("mainEnginePowerKw");
                if (mainEnginePowerObj != null) {
                    double mainEnginePower = Double.parseDouble(mainEnginePowerObj.toString());
                    return mainEnginePower >= 3000 ? "一" : "二";
                }
            }
        } catch (NumberFormatException e) {
            log.warn("船舶参数格式错误: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 检查证书兼容性
     *
     * @param seafarerLevel 船员证书等级
     * @param requiredLevel 要求的证书等级
     * @param applyDutyName 申请职务名称
     * @return 是否兼容
     */
    private static boolean checkCertificateCompatibility(String seafarerLevel, String requiredLevel, String applyDutyName) {
        // 1. 完全匹配
        if (seafarerLevel.equals(requiredLevel)) {
            return true;
        }

        // 2. 判断是否为四大头职务
        if (!MAJOR_POSITIONS.contains(applyDutyName)) {
            // 非四大头职务：首字精确匹配
            return seafarerLevel.charAt(0) == requiredLevel.charAt(0);
        }

        // 3. 四大头职务：精确等级匹配（同时考虑类别优先级）
        return checkMajorPositionCompatibility(seafarerLevel, requiredLevel);
    }

    /**
     * 检查四大头职务的证书兼容性
     *
     * @param seafarerLevel 船员证书等级
     * @param requiredLevel 要求的证书等级
     * @return 是否兼容
     */
    private static boolean checkMajorPositionCompatibility(String seafarerLevel, String requiredLevel) {
        // 1. 确定证书类别
        char seafarerType = seafarerLevel.charAt(0);
        char requiredType = requiredLevel.charAt(0);

        // 2. 检查证书类别优先级兼容性
        if (!checkCertificatePriorityCompatibility(seafarerLevel, requiredLevel)) {
            return false;
        }

        // 3. 如果是同类别，检查等级兼容性
        if (seafarerType == requiredType) {
            return checkSameCategoryLevelCompatibility(seafarerLevel, requiredLevel);
        }

        // 4. 如果是不同类别但优先级兼容，则允许（高优先级可以上低优先级船舶）
        return true;
    }

    /**
     * 检查证书类别优先级兼容性
     *
     * @param seafarerLevel 船员证书等级
     * @param requiredLevel 要求的证书等级
     * @return 是否兼容
     */
    private static boolean checkCertificatePriorityCompatibility(String seafarerLevel, String requiredLevel) {
        char seafarerType = seafarerLevel.charAt(0);
        char requiredType = requiredLevel.charAt(0);

        // 获取证书类别在优先级数组中的位置
        int seafarerPriority = getCertificatePriority(seafarerType);
        int requiredPriority = getCertificatePriority(requiredType);

        // 如果找不到对应的优先级，返回false
        if (seafarerPriority == -1 || requiredPriority == -1) {
            return false;
        }

        // 船员证书优先级必须高于或等于要求的优先级（索引小的优先级高）
        return seafarerPriority <= requiredPriority;
    }

    /**
     * 获取证书类别的优先级索引
     *
     * @param certificateType 证书类别首字符
     * @return 优先级索引，-1表示未找到
     */
    private static int getCertificatePriority(char certificateType) {
        String typeStr = String.valueOf(certificateType);
        for (int i = 0; i < CERTIFICATE_PRIORITY.length; i++) {
            if (CERTIFICATE_PRIORITY[i].equals(typeStr)) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 检查同类别内的等级兼容性
     *
     * @param seafarerLevel 船员证书等级
     * @param requiredLevel 要求的证书等级
     * @return 是否兼容
     */
    private static boolean checkSameCategoryLevelCompatibility(String seafarerLevel, String requiredLevel) {
        char certificateType = seafarerLevel.charAt(0);

        String[] levelArray = null;
        if (certificateType == '甲') {
            levelArray = JIA_LEVELS;
        } else if (certificateType == '乙') {
            levelArray = YI_LEVELS;
        } else if (certificateType == '丙') {
            levelArray = BING_LEVELS;
        } else if (certificateType == '丁') {
            levelArray = DING_LEVELS;
        }

        if (levelArray == null) {
            return false;
        }

        return checkLevelCompatibility(seafarerLevel, requiredLevel, levelArray);
    }

    /**
     * 检查同类别内的等级兼容性
     *
     * @param seafarerLevel 船员证书等级
     * @param requiredLevel 要求的证书等级
     * @param levelArray    等级数组（按从高到低排序）
     * @return 是否兼容
     */
    private static boolean checkLevelCompatibility(String seafarerLevel, String requiredLevel, String[] levelArray) {
        int seafarerIndex = -1;
        int requiredIndex = -1;

        // 查找等级在数组中的位置
        for (int i = 0; i < levelArray.length; i++) {
            if (levelArray[i].equals(seafarerLevel)) {
                seafarerIndex = i;
            }
            if (levelArray[i].equals(requiredLevel)) {
                requiredIndex = i;
            }
        }

        // 如果找不到对应等级，返回false
        if (seafarerIndex == -1 || requiredIndex == -1) {
            return false;
        }

        // 船员等级索引小于等于要求等级索引（高等级可以兼容低等级）
        return seafarerIndex <= requiredIndex;
    }
}
