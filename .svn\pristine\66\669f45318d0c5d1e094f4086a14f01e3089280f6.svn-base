<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e3713aad-0f6f-4249-8f2d-27d453ba6c40" name="Changes" comment="添加候选人甲板相关字段后提交">
      <change afterPath="$PROJECT_DIR$/docs/在船船员引擎信息扩展功能测试指南.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/docs/服务资历扩展功能测试指南.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/voyage/mapper/ShipEngineMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/resources/mapper/voyage/ShipEngineMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/项目结构说明.md" beforeDir="false" afterPath="$PROJECT_DIR$/docs/项目结构说明.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/crew/dto/SeafarerMatchResultDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/crew/dto/SeafarerMatchResultDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/crew/service/impl/SeafarerMatchingServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/crew/service/impl/SeafarerMatchingServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/crew/SeafarerScheduleMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/crew/SeafarerScheduleMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHome" value="F:/learing/apache-maven-3.6.1/apache-maven-3.6.1" />
        <option name="useMavenConfig" value="true" />
        <option name="userSettingsFile" value="F:\learing\apache-maven-3.6.1\apache-maven-3.6.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectId" id="2yzQndRJxrZ03OWCyjudPjDAcqy" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="false" />
    <option name="showLibraryContents" value="true" />
    <option name="showScratchesAndConsoles" value="false" />
    <option name="showVisibilityIcons" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/augmentSpace/src/main/resources/mapper/crew&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;vcs.Subversion&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;96e4b2703bc3d3c2b2f618cf5e956881&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\augmentSpace\src\main\resources\mapper\crew" />
      <recent name="D:\augmentSpace\src\main\java\com\example\multidatasource\crew\entity" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.example.multidatasource.crew.entity" />
      <recent name="com.example.multidatasource.crew.dto" />
      <recent name="com.example.multidatasource.crew.mapper" />
      <recent name="com.example.multidatasource.crew.service.impl" />
      <recent name="com.example.multidatasource.crew.service" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.MultiDataSourceApplication">
    <configuration name="PasswordTest.testPasswordMatching" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="multi-datasource-api" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.multidatasource.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.example.multidatasource" />
      <option name="MAIN_CLASS_NAME" value="com.example.multidatasource.PasswordTest" />
      <option name="METHOD_NAME" value="testPasswordMatching" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MultiDataSourceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="multi-datasource-api" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.multidatasource.MultiDataSourceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.PasswordTest.testPasswordMatching" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\augmentSpace" />
          <option name="myCopyRoot" value="D:\augmentSpace" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\augmentSpace" />
          <option name="myCopyRoot" value="D:\augmentSpace" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="e3713aad-0f6f-4249-8f2d-27d453ba6c40" name="Changes" comment="" />
      <created>1750835089151</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750835089151</updated>
      <workItem from="1750835094034" duration="10912000" />
      <workItem from="1752628014044" duration="45822000" />
      <workItem from="1752828790599" duration="6830000" />
      <workItem from="1753684542769" duration="4189000" />
      <workItem from="1754471722152" duration="2054000" />
      <workItem from="1754535198109" duration="7773000" />
      <workItem from="1754873261589" duration="2545000" />
      <workItem from="1754878968103" duration="1941000" />
      <workItem from="1754881212333" duration="11605000" />
      <workItem from="1754957324234" duration="1415000" />
      <workItem from="1754961180187" duration="313000" />
      <workItem from="1754961640821" duration="1990000" />
      <workItem from="1754963998101" duration="2491000" />
      <workItem from="1754968080234" duration="1049000" />
      <workItem from="1754969149245" duration="2333000" />
      <workItem from="1754981129232" duration="412000" />
    </task>
    <task id="LOCAL-00001" summary="测试提交">
      <created>1752828861894</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752828861894</updated>
    </task>
    <task id="LOCAL-00002" summary="测试提交">
      <created>1752828916703</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752828916703</updated>
    </task>
    <task id="LOCAL-00003" summary="测试提交">
      <created>1752829048148</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752829048148</updated>
    </task>
    <task id="LOCAL-00004" summary="测试提交">
      <created>1753081840382</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753081840382</updated>
    </task>
    <task id="LOCAL-00005" summary="编码船员列表后提交">
      <created>1753174375672</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753174375672</updated>
    </task>
    <task id="LOCAL-00006" summary="编码船员列表后提交">
      <created>1753176780535</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753176780535</updated>
    </task>
    <task id="LOCAL-00007" summary="编码船员列表后提交">
      <created>1753178329880</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753178329880</updated>
    </task>
    <task id="LOCAL-00008" summary="编码船员列表后提交">
      <created>1753232784592</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753232784592</updated>
    </task>
    <task id="LOCAL-00009" summary="编码船员列表后提交">
      <created>1753233243033</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753233243033</updated>
    </task>
    <task id="LOCAL-00010" summary="编码船员列表后提交">
      <created>1753253104177</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753253104177</updated>
    </task>
    <task id="LOCAL-00011" summary="编码船员列表后提交">
      <created>1753260821083</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1753260821083</updated>
    </task>
    <task id="LOCAL-00012" summary="编码船员列表后提交">
      <created>1753321951701</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1753321951701</updated>
    </task>
    <task id="LOCAL-00013" summary="编码船员列表后提交">
      <created>1753321984273</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1753321984273</updated>
    </task>
    <task id="LOCAL-00014" summary="编码船员列表后提交">
      <created>1754536584726</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1754536584726</updated>
    </task>
    <task id="LOCAL-00015" summary="编码船员列表后提交">
      <created>1754536611973</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1754536611973</updated>
    </task>
    <task id="LOCAL-00016" summary="编码船员列表后提交">
      <created>1754536618487</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1754536618487</updated>
    </task>
    <task id="LOCAL-00017" summary="编码船员列表后提交">
      <created>1754906385869</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1754906385869</updated>
    </task>
    <task id="LOCAL-00018" summary="编码船员列表后提交">
      <created>1754964082628</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1754964082628</updated>
    </task>
    <task id="LOCAL-00019" summary="编码船员列表后提交">
      <created>1754967265836</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1754967265836</updated>
    </task>
    <task id="LOCAL-00020" summary="添加候选人甲板相关字段后提交">
      <created>1754970266862</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1754970266862</updated>
    </task>
    <option name="localTasksCounter" value="21" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="首次提交" />
    <MESSAGE value="测试提交" />
    <MESSAGE value="编码船员列表后提交" />
    <MESSAGE value="添加候选人甲板相关字段后提交" />
    <option name="LAST_COMMIT_MESSAGE" value="添加候选人甲板相关字段后提交" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>