package com.example.multidatasource.voyage;

import okhttp3.OkHttpClient;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.junit.jupiter.api.Test;

/**
 * 依赖测试类 - 验证OkHttp和Jsoup依赖是否正常
 */
public class DependencyTest {

    @Test
    public void testOkHttpDependency() {
        try {
            OkHttpClient client = new OkHttpClient();
            System.out.println("OkHttp依赖正常，版本: " + client.getClass().getPackage().getImplementationVersion());
        } catch (Exception e) {
            System.err.println("OkHttp依赖异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testJsoupDependency() {
        try {
            Document doc = Jsoup.parse("<html><body><h1>Test</h1></body></html>");
            String title = doc.select("h1").text();
            System.out.println("Jsoup依赖正常，解析结果: " + title);
        } catch (Exception e) {
            System.err.println("Jsoup依赖异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
