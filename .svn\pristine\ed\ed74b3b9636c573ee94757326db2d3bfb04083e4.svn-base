package com.example.multidatasource.crew.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 船员匹配请求DTO
 */
@Data
@Schema(description = "船员匹配请求参数")
@JsonInclude(JsonInclude.Include.ALWAYS)
public class SeafarerMatchRequestDTO {

    @Schema(description = "船舶ID")
    private String vesselId;

    @Schema(description = "船舶ID列表（逗号分隔）")
    private String vesselIdIn;

    @Schema(description = "申请职务ID")
    private String applyDutyId;

    @Schema(description = "申请职务ID列表（逗号分隔）")
    private String applyDutyIdIn;

    @Schema(description = "证书等级ID")
    private String crtLevelId;

    @Schema(description = "证书等级ID列表（逗号分隔）")
    private String crtLevelIdIn;

    @Schema(description = "是否有江海证书（0-无，1-有）")
    private String haveJiangCertificate;

    @Schema(description = "在船天数阈值", example = "180")
    private String onBoardDays = "180";

    @Schema(description = "下船天数起始值", example = "30")
    private String downBoardDaysSt = "30";

    @Schema(description = "下船天数结束值", example = "180")
    private String downBoardDaysEd = "180";

    @Schema(description = "证书到期天数", example = "240")
    private String certificateExpireDate = "240";


}
