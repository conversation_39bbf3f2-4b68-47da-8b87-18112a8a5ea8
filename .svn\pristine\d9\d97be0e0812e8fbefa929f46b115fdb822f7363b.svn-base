package com.example.multidatasource.controller;

import com.example.multidatasource.model.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 公开接口控制器
 * 这些接口无需身份验证即可访问
 */
@RestController
@RequestMapping("/public")
@Tag(name = "公开接口", description = "无需身份验证的公开API接口")
public class PublicController {

    @GetMapping("/info")
    @Operation(summary = "获取系统信息", description = "获取系统基本信息，无需认证")
    public ApiResponse<Map<String, Object>> getSystemInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("systemName", "多数据源管理系统");
        info.put("version", "1.0.0");
        info.put("description", "基于Spring Boot的多MySQL数据源管理系统");
        info.put("serverTime", LocalDateTime.now());
        info.put("status", "running");
        
        return ApiResponse.success(info);
    }

    @GetMapping("/version")
    @Operation(summary = "获取版本信息", description = "获取系统版本信息，无需认证")
    public ApiResponse<String> getVersion() {
        return ApiResponse.success("1.0.0");
    }

    @GetMapping("/ping")
    @Operation(summary = "系统连通性测试", description = "测试系统是否正常运行，无需认证")
    public ApiResponse<String> ping() {
        return ApiResponse.success("pong");
    }

    @GetMapping("/modules")
    @Operation(summary = "获取系统模块列表", description = "获取系统支持的业务模块列表，无需认证")
    public ApiResponse<Map<String, Object>> getModules() {
        Map<String, Object> modules = new HashMap<>();
        
        Map<String, String> crewModule = new HashMap<>();
        crewModule.put("name", "船管系统");
        crewModule.put("description", "船员信息管理");
        crewModule.put("status", "已实现");
        
        Map<String, String> voyageModule = new HashMap<>();
        voyageModule.put("name", "航次管理");
        voyageModule.put("description", "航次计划和状态管理");
        voyageModule.put("status", "已实现");
        
        Map<String, String> cargoModule = new HashMap<>();
        cargoModule.put("name", "货物管理");
        cargoModule.put("description", "货物信息管理");
        cargoModule.put("status", "待实现");
        
        Map<String, String> financeModule = new HashMap<>();
        financeModule.put("name", "财务管理");
        financeModule.put("description", "财务数据管理");
        financeModule.put("status", "待实现");
        
        modules.put("crew", crewModule);
        modules.put("voyage", voyageModule);
        modules.put("cargo", cargoModule);
        modules.put("finance", financeModule);
        
        return ApiResponse.success(modules);
    }
}
