package com.example.multidatasource.common.annotation;

import com.example.multidatasource.common.config.DataSourceContextHolder;

import java.lang.annotation.*;

/**
 * 数据源切换注解
 * 用于在Controller或Service方法上指定使用的数据源
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataSource {
    
    /**
     * 数据源类型
     * 可以使用字符串或枚举值
     */
    DataSourceContextHolder.DataSourceType value() default DataSourceContextHolder.DataSourceType.CREW;
    
    /**
     * 数据源名称（字符串形式）
     * 当value()不满足需求时可以使用此属性
     */
    String name() default "";
}
