package com.example.multidatasource.crew.service.impl;

import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.common.config.DataSourceContextHolder;
import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.crew.dto.SeafarerMatchRequestDTO;
import com.example.multidatasource.crew.dto.EvaluationInfoDTO;
import com.example.multidatasource.crew.dto.QualificationWithEvaluationDTO;
import com.example.multidatasource.crew.dto.HandoverPairDTO;
import com.example.multidatasource.crew.dto.SeafarerMatchResultDTO;
import java.time.LocalDate;
import com.example.multidatasource.crew.entity.CrewInfo;
import com.example.multidatasource.crew.mapper.SeafarerScheduleMapper;
import com.example.multidatasource.crew.service.SeafarerMatchingService;
import com.example.multidatasource.crew.service.SeafarerScheduleService;
import com.example.multidatasource.voyage.mapper.VoyageQualificationMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.io.ByteArrayOutputStream;
import java.time.format.DateTimeFormatter;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * 船员管理服务实现
 */
@Slf4j
@Service
@DataSource(DataSourceContextHolder.DataSourceType.CREW)
public class SeafarerScheduleServiceImpl implements SeafarerScheduleService {

    @Autowired
    private SeafarerScheduleMapper seafarerScheduleMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SeafarerMatchingService seafarerMatchingService;

    @Autowired
    private VoyageQualificationMapper voyageQualificationMapper;


    @Override
    public PageResult<CrewInfo> queryCrewsPage(Integer page, Integer size, String seafarerId, String seafarerName, String applyDutyId) {
        int offset = (page - 1) * size;
        List<CrewInfo> records = seafarerScheduleMapper.selectCrewList(offset, size, seafarerId, seafarerName, applyDutyId);
        int total = seafarerScheduleMapper.countCrew(seafarerId, seafarerName, applyDutyId);

        return PageResult.of(records, (long) total, page, size);
    }

    @Override
    public List<Map<String, Object>> getSeafarerCertificateInfo(String seafarerId) {
        log.info("查询船员证书到期列表, seafarerId: {}", seafarerId);
        try {
            List<Map<String, Object>> result = seafarerScheduleMapper.getSeafarerCertificateInfo(seafarerId);
            log.info("成功查询到{}条证书记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("查询船员证书信息失败, seafarerId: {}", seafarerId, e);
            throw new RuntimeException("查询船员证书信息失败: " + e.getMessage());
        }
    }

    @Override
    public QualificationWithEvaluationDTO getSeafarerQualificationInfo(String seafarerId, String applyDutyId) {
        log.info("查询船员服务资历及最新考评, seafarerId: {}, applyDutyId: {}", seafarerId, applyDutyId);
        try {
            // 1. 查询服务资历列表
            List<Map<String, Object>> qualifications = getQualificationInfoCore(seafarerId, applyDutyId, null, null);

            // 2. 查询最新的考评信息
            EvaluationInfoDTO latestEvaluation = getLatestEvaluation(seafarerId);

            // 3. 提取曾任职船舶列表
            List<String> vesselNames = qualifications.stream()
                    .map(q -> (String) q.get("vesselName"))
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            // 4. 组装并返回
            QualificationWithEvaluationDTO resultDto = QualificationWithEvaluationDTO.of(qualifications, latestEvaluation);
            resultDto.setPreviouslyServedVessels(vesselNames);
            return resultDto;
        } catch (Exception e) {
            log.error("查询船员服务资历及考评失败, seafarerId: {}", seafarerId, e);
            throw new RuntimeException("查询船员服务资历及考评失败", e);
        }
    }

    @Override
    public QualificationWithEvaluationDTO getSeafarerQualificationInfoPage(String seafarerId, String applyDutyId, Integer page, Integer size) {
        log.info("分页查询船员服务资历及最新考评, seafarerId: {}, page: {}, size: {}", seafarerId, page, size);
        try {
            // 1. 查询分页的服务资历
            Integer total = seafarerScheduleMapper.countSeafarerQualificationInfo(seafarerId, applyDutyId);
            List<Map<String, Object>> list = (total == null || total == 0)
                    ? Collections.emptyList()
                    : getQualificationInfoCore(seafarerId, applyDutyId, (page - 1) * size, size);
            PageResult<Map<String, Object>> pagedQualifications = PageResult.of(list, total != null ? total.longValue() : 0L, page, size);

            // 2. 查询最新的考评信息
            EvaluationInfoDTO latestEvaluation = getLatestEvaluation(seafarerId);

            // 3. 组装并返回
            QualificationWithEvaluationDTO resultDto = QualificationWithEvaluationDTO.of(pagedQualifications, latestEvaluation);

            // 4. 提取曾任职船舶列表
            if (pagedQualifications.getRecords() != null) {
                List<String> vesselNames = pagedQualifications.getRecords().stream()
                        .map(q -> (String) q.get("vesselName"))
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());
                resultDto.setPreviouslyServedVessels(vesselNames);
            }

            return resultDto;
        } catch (Exception e) {
            log.error("分页查询船员服务资历及考评失败, seafarerId: {}", seafarerId, e);
            throw new RuntimeException("分页查询船员服务资历及考评失败", e);
        }
    }

    /**
     * 获取单个船员的最新考评信息（私有辅助方法）
     */
    private EvaluationInfoDTO getLatestEvaluation(String seafarerId) {
        try {
            Map<String, Object> evalMap = seafarerScheduleMapper.getLatestEvaluationInfoBySeafarerId(seafarerId);
            if (evalMap == null || evalMap.isEmpty()) {
                return null;
            }

            EvaluationInfoDTO dto = new EvaluationInfoDTO();
            dto.setEvaluationType((String) evalMap.get("evaluationType"));
            Object evalDateObj = evalMap.get("evaluationDate");
            if (evalDateObj instanceof LocalDateTime) {
                dto.setEvaluationDate(((LocalDateTime) evalDateObj).toLocalDate());
            } else if (evalDateObj instanceof java.sql.Date) {
                dto.setEvaluationDate(((java.sql.Date) evalDateObj).toLocalDate());
            } else if (evalDateObj instanceof java.util.Date) { // Also handles java.sql.Timestamp
                dto.setEvaluationDate(new java.sql.Date(((java.util.Date) evalDateObj).getTime()).toLocalDate());
            }
            // 船长信息
            dto.setCaptainComment((String) evalMap.get("captainComment"));
            dto.setCaptainName((String) evalMap.get("captainName"));

            // 部门长信息
            dto.setDepartmentHeadName((String) evalMap.get("departmentHeadName"));
            dto.setDepartmentHeadDuty((String) evalMap.get("departmentHeadDuty"));

            // 船员部经理信息
            dto.setCrewDeptManagerEvaluation((String) evalMap.get("crewDeptManagerEvaluation"));
            dto.setCrewManagerName((String) evalMap.get("crewManagerName"));
            dto.setCrewManagerDuty((String) evalMap.get("crewManagerDuty"));

            // 从editData JSON中解析部门长意见
            String editDataJson = (String) evalMap.get("editData");
            if (StringUtils.hasText(editDataJson)) {
                try {
                    Map<String, Object> editDataMap = objectMapper.readValue(editDataJson, new TypeReference<Map<String, Object>>() {});
                    for (Map.Entry<String, Object> entry : editDataMap.entrySet()) {
                        if (entry.getKey().startsWith("textarea_")) {
                            dto.setDepartmentHeadOpinion(entry.getValue() != null ? entry.getValue().toString() : null);
                            break; // 找到第一个就跳出
                        }
                    }
                } catch (Exception e) {
                    log.error("解析部门长意见JSON失败, seafarerId: {}, json: {}", seafarerId, editDataJson, e);
                }
            }
            return dto;
        } catch (Exception e) {
            log.error("获取船员最新考评信息失败, seafarerId: {}", seafarerId, e);
            return null; // 即使考评查询失败，也不应影响主流程
        }
    }

    /**
     * 核心查询逻辑，支持分页和非分页
     * @param seafarerId 船员ID
     * @param applyDutyId 申请职务ID（可选）
     * @param offset 偏移量（可选，为null时不分页）
     * @param limit 限制数量（可选，为null时不分页）
     * @return 服务资历列表
     */
    private List<Map<String, Object>> getQualificationInfoCore(String seafarerId, String applyDutyId, Integer offset, Integer limit) {
        // 步骤1: 查询crew数据源，获取基础服务资历信息（包含船员姓名）
        List<Map<String, Object>> crewQualifications = seafarerScheduleMapper.getSeafarerQualificationInfo(
                seafarerId, applyDutyId, offset, limit);
        log.info("从crew数据源查询到{}条服务资历记录", crewQualifications.size());

        // 步骤2: 为每条服务资历记录添加航次相关信息
        for (Map<String, Object> qualification : crewQualifications) {
            enrichWithVoyageInfo(qualification);
        }

        log.info("成功完成{}条服务资历记录的航次信息扩展", crewQualifications.size());
        return crewQualifications;
    }

    @Override
    public List<Map<String, Object>> matchSeafarerForShiftChange(SeafarerMatchRequestDTO request) {
        log.info("使用优化后的匹配服务 - 匹配所有候选人");
        return seafarerMatchingService.matchSeafarerForShiftChange(request);
    }

    @Override
    public List<Map<String, Object>> matchSingleSeafarerForShiftChange(SeafarerMatchRequestDTO request) {
        log.info("使用优化后的匹配服务 - 单个最优匹配");
        return seafarerMatchingService.matchSingleSeafarerForShiftChange(request);
    }

    /**
     * 为服务资历记录添加航次相关信息
     *
     * 功能说明：
     * 1. 根据船舶MMSI码和上下船时间查询voyage数据源
     * 2. 通过MMSI码实现跨数据源船舶关联
     * 3. 获取航次号、装卸货品、码头、主机/辅机信息
     * 4. 将查询结果添加到原始记录中
     *
     * 注意：数据源切换通过VoyageQualificationMapper上的@DataSource注解自动处理
     *
     * @param qualification 服务资历记录（会被直接修改）
     */
    private void enrichWithVoyageInfo(Map<String, Object> qualification) {
        try {
            // 获取查询voyage数据源所需的参数
            String mmsiCode = (String) qualification.get("mmsiCode");
            String onBoardDate = convertToDateString(qualification.get("onBoardDate"));
            String downBoardDate = convertToDateString(qualification.get("downBoardDate"));

            // 参数验证
            if (mmsiCode == null || mmsiCode.trim().isEmpty()) {
                log.warn("船舶MMSI码为空，跳过航次信息查询: seafarerId={}, vesselName={}",
                        qualification.get("seafarerId"), qualification.get("vesselName"));
                setDefaultVoyageInfo(qualification);
                return;
            }

            if (onBoardDate == null || onBoardDate.trim().isEmpty()) {
                log.warn("上船日期为空，跳过航次信息查询: seafarerId={}, mmsiCode={}",
                        qualification.get("seafarerId"), mmsiCode);
                setDefaultVoyageInfo(qualification);
                return;
            }

            // 查询voyage数据源航次信息（通过MMSI码关联，数据源切换由@DataSource注解自动处理）
            Map<String, Object> voyageInfo = voyageQualificationMapper.getVoyageQualificationInfo(
                    mmsiCode, onBoardDate, downBoardDate);

            // 将航次信息添加到原始记录中
            if (voyageInfo != null) {
                qualification.put("voyageNumbers", cleanString((String) voyageInfo.get("voyageNumbers")));
                qualification.put("cargos", cleanString((String) voyageInfo.get("cargos")));
                qualification.put("terminals", cleanString((String) voyageInfo.get("terminals")));
                qualification.put("mainEngineModel", cleanString((String) voyageInfo.get("mainEngineModel")));
                qualification.put("mainEngineManufacturer", cleanString((String) voyageInfo.get("mainEngineManufacturer")));
                qualification.put("auxiliaryEngineModel", cleanString((String) voyageInfo.get("auxiliaryEngineModel")));
                qualification.put("auxiliaryEngineManufacturer", cleanString((String) voyageInfo.get("auxiliaryEngineManufacturer")));

                log.debug("成功为船员{}添加航次信息", qualification.get("seafarerId"));
            } else {
                log.warn("未查询到航次信息: mmsiCode={}, onBoardDate={}, downBoardDate={}",
                        mmsiCode, onBoardDate, downBoardDate);
                setDefaultVoyageInfo(qualification);
            }

        } catch (Exception e) {
            log.error("查询航次信息失败: {}", qualification.get("seafarerId"), e);
            setDefaultVoyageInfo(qualification);
        }
    }

    /**
     * 设置默认的航次信息（空值）
     */
    private void setDefaultVoyageInfo(Map<String, Object> qualification) {
        qualification.put("voyageNumbers", "");
        qualification.put("cargos", "");
        qualification.put("terminals", "");
        qualification.put("mainEngineModel", "");
        qualification.put("mainEngineManufacturer", "");
        qualification.put("auxiliaryEngineModel", "");
        qualification.put("auxiliaryEngineManufacturer", "");
    }

    /**
     * 清理字符串，去除空值和null值
     *
     * @param str 原始字符串（可能包含空值）
     * @return 清理后的字符串
     */
    private String cleanString(String str) {
        if (str == null || str.trim().isEmpty()) {
            return "";
        }

        // 分割字符串，去除空值和null值，然后重新拼接
        String[] parts = str.split(",");
        StringBuilder result = new StringBuilder();

        for (String part : parts) {
            if (part != null && !part.trim().isEmpty() && !"null".equalsIgnoreCase(part.trim())) {
                if (result.length() > 0) {
                    result.append(",");
                }
                result.append(part.trim());
            }
        }

        return result.toString();
    }

    /**
     * 将日期对象转换为字符串格式
     *
     * 支持多种日期类型的转换：
     * - LocalDateTime -> "yyyy-MM-dd HH:mm:ss"
     * - LocalDate -> "yyyy-MM-dd"
     * - String -> 直接返回
     * - null -> null
     *
     * @param dateObj 日期对象
     * @return 格式化后的日期字符串
     */
    private String convertToDateString(Object dateObj) {
        if (dateObj == null) {
            return null;
        }

        if (dateObj instanceof String) {
            return (String) dateObj;
        }

        if (dateObj instanceof java.time.LocalDateTime) {
            java.time.LocalDateTime localDateTime = (java.time.LocalDateTime) dateObj;
            return localDateTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        if (dateObj instanceof java.time.LocalDate) {
            java.time.LocalDate localDate = (java.time.LocalDate) dateObj;
            return localDate.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }

        if (dateObj instanceof java.util.Date) {
            java.util.Date date = (java.util.Date) dateObj;
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(date);
        }

        if (dateObj instanceof java.sql.Timestamp) {
            java.sql.Timestamp timestamp = (java.sql.Timestamp) dateObj;
            return timestamp.toLocalDateTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        // 如果是其他类型，尝试转换为字符串
        return dateObj.toString();
    }


    @Override
    public byte[] generateHandoverExcel(List<com.example.multidatasource.crew.dto.HandoverPairDTO> handoverPairs) throws Exception {
        if (handoverPairs == null || handoverPairs.isEmpty()) {
            return new byte[0];
        }

        try (XSSFWorkbook workbook = new XSSFWorkbook(); ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            XSSFSheet sheet = workbook.createSheet("交接班人员列表");

            // Header
            String[] headers = {"序号", "职务", "船名", "预交接日期", "交班人员姓名", "接班人员姓名", "交接原因", "接班人员曾任职船舶", "上个合同/新聘任职评价", "备注"};
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            int rowNum = 1;
            for (com.example.multidatasource.crew.dto.HandoverPairDTO pair : handoverPairs) {
                Row row = sheet.createRow(rowNum++);

                SeafarerMatchResultDTO.OnBoardSeafarerDTO onBoard = pair.getOnBoardSeafarer();
                SeafarerMatchResultDTO.CandidateSeafarerDTO candidate = pair.getCandidateSeafarer();

                // Fetch full details for the candidate
                QualificationWithEvaluationDTO candidateDetails = getSeafarerQualificationInfo(candidate.getSeafarerId(), candidate.getApplyDutyId());

                // 1. 在船交班人员信息
                row.createCell(0).setCellValue(rowNum - 1);
                row.createCell(1).setCellValue(onBoard.getApplyDutyName());
                row.createCell(2).setCellValue(onBoard.getVesselName());
                java.time.LocalDate handoverDate = onBoard.getOnBoardDate() != null ? onBoard.getOnBoardDate().plusDays(180) : null;
                row.createCell(3).setCellValue(handoverDate != null ? handoverDate.format(DateTimeFormatter.ISO_LOCAL_DATE) : "");
                row.createCell(4).setCellValue(onBoard.getSeafarerName());

                // 2. 换选接班人员信息
                row.createCell(5).setCellValue(candidate.getSeafarerName());
                row.createCell(6).setCellValue(""); // 交接原因 (放空)

                String vessels = "";
                if (candidateDetails.getPreviouslyServedVessels() != null) {
                    vessels = String.join(", ", candidateDetails.getPreviouslyServedVessels());
                }
                row.createCell(7).setCellValue(vessels);

                String evaluationText = formatEvaluation(candidateDetails.getLatestEvaluation());
                row.createCell(8).setCellValue(evaluationText);
                row.createCell(9).setCellValue(""); // 备注 (默认放空)
            }

            workbook.write(out);
            return out.toByteArray();
        }
    }

    private String formatEvaluation(EvaluationInfoDTO eval) {
        if (eval == null) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        if (StringUtils.hasText(eval.getCaptainName()) || StringUtils.hasText(eval.getCaptainComment())) {
            sb.append(String.format("船长 %s：%s\n", eval.getCaptainName() != null ? eval.getCaptainName() : "", eval.getCaptainComment() != null ? eval.getCaptainComment() : ""));
        }
        if (StringUtils.hasText(eval.getDepartmentHeadName()) || StringUtils.hasText(eval.getDepartmentHeadOpinion())) {
            sb.append(String.format("%s %s：%s\n", eval.getDepartmentHeadDuty() != null ? eval.getDepartmentHeadDuty() : "", eval.getDepartmentHeadName() != null ? eval.getDepartmentHeadName() : "", eval.getDepartmentHeadOpinion() != null ? eval.getDepartmentHeadOpinion() : ""));
        }
        if (StringUtils.hasText(eval.getCrewManagerName()) || StringUtils.hasText(eval.getCrewDeptManagerEvaluation())) {
            sb.append(String.format("%s %s：%s\n", eval.getCrewManagerDuty() != null ? eval.getCrewManagerDuty() : "", eval.getCrewManagerName() != null ? eval.getCrewManagerName() : "", eval.getCrewDeptManagerEvaluation() != null ? eval.getCrewDeptManagerEvaluation() : ""));
        }
        return sb.toString().trim();
    }

}
