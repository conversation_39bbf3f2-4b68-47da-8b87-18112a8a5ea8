package com.example.multidatasource.crew.controller;

import com.example.multidatasource.model.ApiResponse;
import com.example.multidatasource.crew.dto.VoyageConsumptionSummaryDTO;
import com.example.multidatasource.crew.service.OilVoyageConsumptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 航次油耗信息控制器    航次油耗管理
 */
@Slf4j
@RestController
@RequestMapping("/crew/oil-voyage-consumption")
@Tag(name = "航次油耗管理", description = "航次油耗信息管理相关接口")
public class OilVoyageConsumptionController {

    @Autowired
    private OilVoyageConsumptionService oilVoyageConsumptionService;

    @GetMapping("/latest-by-vessel")
    @Operation(summary = "获取每个船舶最新的航次油耗记录",
               description = "核心功能：主表取最新记录 + 子表关联查询（轻油和重油明细）")
    public ApiResponse<List<VoyageConsumptionSummaryDTO>> getLatestVoyageConsumptionByVessel(
            @Parameter(description = "船舶ID（可选）") @RequestParam(required = false) String vesselId,
            @Parameter(description = "船舶名称（可选，支持模糊查询）") @RequestParam(required = false) String vesselName,
            @Parameter(description = "MMSI（可选）") @RequestParam(required = false) String mmsiCode,
            @Parameter(description = "开始日期（可选，格式：yyyy-MM-dd）") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束日期（可选，格式：yyyy-MM-dd）") @RequestParam(required = false) String endDate) {
        try {
            List<VoyageConsumptionSummaryDTO> result = oilVoyageConsumptionService.getLatestVoyageConsumptionByVessel(
                    vesselId, vesselName, mmsiCode, startDate, endDate);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取船舶最新航次油耗记录失败", e);
            return ApiResponse.error("获取船舶最新航次油耗记录失败: " + e.getMessage());
        }
    }
}
