@echo off
echo ========================================
echo 多数据源API文档生成工具
echo ========================================

:: 检查应用是否运行
echo 正在检查应用状态...
curl -s http://localhost:54321/multi/source/api/health/status >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 应用未运行，请先启动应用
    echo 启动命令: mvn spring-boot:run
    pause
    exit /b 1
)

echo 应用运行正常，开始生成文档...

:: 创建文档目录
if not exist "docs\api" mkdir docs\api

:: 1. 下载OpenAPI JSON
echo 1. 生成OpenAPI JSON文档...
curl -s http://localhost:54321/multi/source/api/v3/api-docs > docs\api\openapi.json
if %errorlevel% equ 0 (
    echo    ✓ OpenAPI JSON已生成: docs\api\openapi.json
) else (
    echo    ✗ OpenAPI JSON生成失败
)

:: 2. 生成Postman Collection
echo 2. 生成Postman Collection...
curl -s http://localhost:54321/multi/source/api/v3/api-docs > docs\api\postman-collection.json
if %errorlevel% equ 0 (
    echo    ✓ Postman Collection已生成: docs\api\postman-collection.json
) else (
    echo    ✗ Postman Collection生成失败
)

:: 3. 创建API文档说明
echo 3. 生成API文档说明...
(
echo # API文档使用指南
echo.
echo ## 📋 文档类型
echo.
echo ### 1. 在线交互式文档
echo - **Swagger UI**: http://localhost:54321/multi/source/api/swagger-ui/index.html
echo - **特点**: 可以直接测试接口，支持JWT认证
echo - **使用方法**: 
echo   1. 通过 /auth/login 获取JWT令牌
echo   2. 点击右上角 Authorize 按钮配置认证
echo   3. 测试各种API接口
echo.
echo ### 2. OpenAPI规范文档
echo - **JSON格式**: http://localhost:54321/multi/source/api/v3/api-docs
echo - **本地文件**: docs/api/openapi.json
echo - **用途**: 前端代码生成、第三方工具集成
echo.
echo ### 3. Postman Collection
echo - **文件位置**: docs/api/postman-collection.json
echo - **使用方法**: 导入到Postman中进行接口测试
echo.
echo ## 🔐 认证说明
echo.
echo ### 默认用户账户
echo - **管理员**: admin / admin123
echo - **普通用户**: testuser / user123
echo.
echo ### JWT令牌使用
echo 1. 调用 POST /auth/login 获取令牌
echo 2. 在请求头中添加: Authorization: Bearer ^<token^>
echo 3. 令牌默认1小时过期
echo.
echo ## 📚 API分组说明
echo.
echo - **身份认证** ^(/auth/*^): 用户登录、注册、令牌管理
echo - **用户管理** ^(/users/*^): 用户信息管理^(需要认证^)
echo - **船管系统** ^(/crew/*^): 船员信息管理^(需要认证^)
echo - **航次管理** ^(/voyage/*^): 航次计划管理^(需要认证^)
echo - **通用SQL** ^(/sql/*^): 动态SQL执行^(需要认证^)
echo - **系统监控** ^(/health/*^): 系统状态检查^(公开^)
echo - **公开接口** ^(/public/*^): 无需认证的接口
echo.
echo ## 🚀 快速开始
echo.
echo 1. 启动应用: mvn spring-boot:run
echo 2. 访问Swagger UI: http://localhost:54321/multi/source/api/swagger-ui/index.html
echo 3. 使用默认账户登录获取令牌
echo 4. 配置认证后测试各种接口
echo.
echo 生成时间: %date% %time%
) > docs\api\README.md

echo    ✓ API文档说明已生成: docs\api\README.md

:: 4. 创建前端集成示例
echo 4. 生成前端集成示例...
(
echo // API调用示例 - JavaScript
echo.
echo // 1. 用户登录
echo async function login^(username, password^) {
echo   const response = await fetch^('/multi/source/api/auth/login', {
echo     method: 'POST',
echo     headers: { 'Content-Type': 'application/json' },
echo     body: JSON.stringify^({ username, password, rememberMe: false }^)
echo   }^);
echo   const result = await response.json^(^);
echo   if ^(result.code === 200^) {
echo     localStorage.setItem^('token', result.data.accessToken^);
echo     return result.data;
echo   }
echo   throw new Error^(result.message^);
echo }
echo.
echo // 2. 调用需要认证的API
echo async function apiCall^(url, options = {}^) {
echo   const token = localStorage.getItem^('token'^);
echo   const headers = {
echo     'Content-Type': 'application/json',
echo     ...^(token ^&^& { 'Authorization': `Bearer ${token}` }^),
echo     ...options.headers
echo   };
echo   
echo   const response = await fetch^(url, { ...options, headers }^);
echo   return await response.json^(^);
echo }
echo.
echo // 3. 使用示例
echo // 获取船员列表
echo const crewList = await apiCall^('/multi/source/api/crew/crew-list?page=1^&size=10'^);
echo.
echo // 获取航次详情
echo const voyageDetail = await apiCall^('/multi/source/api/voyage/voyage/1'^);
echo.
echo // 获取用户信息
echo const userInfo = await apiCall^('/multi/source/api/users/1'^);
) > docs\api\frontend-example.js

echo    ✓ 前端集成示例已生成: docs\api\frontend-example.js

echo.
echo ========================================
echo 文档生成完成！
echo ========================================
echo.
echo 📁 生成的文件:
echo   - docs\api\openapi.json          ^(OpenAPI规范文档^)
echo   - docs\api\postman-collection.json ^(Postman导入文件^)
echo   - docs\api\README.md             ^(API使用说明^)
echo   - docs\api\frontend-example.js   ^(前端集成示例^)
echo.
echo 🌐 在线文档:
echo   - Swagger UI: http://localhost:54321/multi/source/api/swagger-ui/index.html
echo   - OpenAPI JSON: http://localhost:54321/multi/source/api/v3/api-docs
echo.
pause
