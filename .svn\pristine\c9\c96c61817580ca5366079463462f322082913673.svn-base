2025-08-13 15:26:56 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication v1.0.0 using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 4092 (D:\augmentSpace\target\multi-datasource-api-1.0.0.jar started by <PERSON><PERSON><PERSON><PERSON> in D:\augmentSpace)
2025-08-13 15:26:56 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-13 15:26:56 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-08-13 15:26:58 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/auth/mapper/UserMapper.class]
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/crew/mapper/CrewMapper.class]
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/crew/mapper/OilVoyageConsumptionMapper.class]
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/crew/mapper/SeafarerBaseInfoMapper.class]
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/crew/mapper/SeafarerScheduleMapper.class]
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/voyage/mapper/ShipEngineMapper.class]
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/voyage/mapper/VoyageMapper.class]
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/voyage/mapper/VoyageQualificationMapper.class]
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerBaseInfoMapper' and 'com.example.multidatasource.crew.mapper.SeafarerBaseInfoMapper' mapperInterface
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerScheduleMapper' and 'com.example.multidatasource.crew.mapper.SeafarerScheduleMapper' mapperInterface
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'shipEngineMapper' and 'com.example.multidatasource.voyage.mapper.ShipEngineMapper' mapperInterface
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageQualificationMapper' and 'com.example.multidatasource.voyage.mapper.VoyageQualificationMapper' mapperInterface
2025-08-13 15:27:01 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-08-13 15:27:01 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-13 15:27:01 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-13 15:27:01 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-08-13 15:27:01 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4914 ms
2025-08-13 15:27:01 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 15:27:02 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-08-13 15:27:02 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/auth/UserMapper.xml]'
2025-08-13 15:27:02 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/crew/CrewMapper.xml]'
2025-08-13 15:27:02 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/crew/OilVoyageConsumptionMapper.xml]'
2025-08-13 15:27:02 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/crew/SeafarerBaseInfoMapper.xml]'
2025-08-13 15:27:03 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/crew/SeafarerScheduleMapper.xml]'
2025-08-13 15:27:03 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/voyage/ShipEngineMapper.xml]'
2025-08-13 15:27:03 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/voyage/VoyageMapper.xml]'
2025-08-13 15:27:03 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/voyage/VoyageQualificationMapper.xml]'
2025-08-13 15:27:04 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 63507bdf-558f-4792-b691-439ba4ae43ab

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-13 15:27:04 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@478db956, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6ca18a14, org.springframework.security.web.context.SecurityContextPersistenceFilter@7ac296f6, org.springframework.security.web.header.HeaderWriterFilter@2f01783a, org.springframework.web.filter.CorsFilter@c667f46, org.springframework.security.web.authentication.logout.LogoutFilter@17d919b6, com.example.multidatasource.config.JwtAuthenticationFilter@503d687a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4b86805d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4149c063, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@51bd8b5c, org.springframework.security.web.session.SessionManagementFilter@41488b16, org.springframework.security.web.access.ExceptionTranslationFilter@2bdd8394, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7905a0b8]
2025-08-13 15:27:07 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 54321 (http) with context path '/multi/source/api'
2025-08-13 15:27:07 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 11.48 seconds (JVM running for 12.165)
2025-08-13 16:49:00 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 23840 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-08-13 16:49:00 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-13 16:49:00 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-08-13 16:49:01 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerBaseInfoMapper.class]
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerScheduleMapper.class]
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\MaritimeBureauConfigMapper.class]
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\MaritimeInfoMapper.class]
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\ShipEngineMapper.class]
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageQualificationMapper.class]
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerBaseInfoMapper' and 'com.example.multidatasource.crew.mapper.SeafarerBaseInfoMapper' mapperInterface
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerScheduleMapper' and 'com.example.multidatasource.crew.mapper.SeafarerScheduleMapper' mapperInterface
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'maritimeBureauConfigMapper' and 'com.example.multidatasource.voyage.mapper.MaritimeBureauConfigMapper' mapperInterface
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'maritimeInfoMapper' and 'com.example.multidatasource.voyage.mapper.MaritimeInfoMapper' mapperInterface
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'shipEngineMapper' and 'com.example.multidatasource.voyage.mapper.ShipEngineMapper' mapperInterface
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-08-13 16:49:01 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageQualificationMapper' and 'com.example.multidatasource.voyage.mapper.VoyageQualificationMapper' mapperInterface
2025-08-13 16:49:03 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-08-13 16:49:03 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-13 16:49:03 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-13 16:49:03 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:49:03 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3624 ms
2025-08-13 16:49:04 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 16:49:04 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-08-13 16:49:04 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-08-13 16:49:04 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-08-13 16:49:04 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-08-13 16:49:04 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerBaseInfoMapper.xml]'
2025-08-13 16:49:04 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerScheduleMapper.xml]'
2025-08-13 16:49:04 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeBureauConfigMapper.xml]'
2025-08-13 16:49:04 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController': Unsatisfied dependency expressed through field 'authService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authServiceImpl': Unsatisfied dependency expressed through field 'userService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'userMapper' defined in file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]: Cannot resolve reference to bean 'sqlSessionFactory' while setting bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/example/multidatasource/config/MyBatisDataSourceConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeInfoMapper.xml]'
2025-08-13 16:49:04 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-13 16:49:04 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 16:49:04 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController': Unsatisfied dependency expressed through field 'authService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authServiceImpl': Unsatisfied dependency expressed through field 'userService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'userMapper' defined in file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]: Cannot resolve reference to bean 'sqlSessionFactory' while setting bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/example/multidatasource/config/MyBatisDataSourceConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeInfoMapper.xml]'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:713)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.example.multidatasource.MultiDataSourceApplication.main(MultiDataSourceApplication.java:17)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authServiceImpl': Unsatisfied dependency expressed through field 'userService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'userMapper' defined in file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]: Cannot resolve reference to bean 'sqlSessionFactory' while setting bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/example/multidatasource/config/MyBatisDataSourceConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeInfoMapper.xml]'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:713)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710)
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'userMapper' defined in file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]: Cannot resolve reference to bean 'sqlSessionFactory' while setting bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/example/multidatasource/config/MyBatisDataSourceConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeInfoMapper.xml]'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:713)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710)
	... 34 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'userMapper' defined in file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]: Cannot resolve reference to bean 'sqlSessionFactory' while setting bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/example/multidatasource/config/MyBatisDataSourceConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeInfoMapper.xml]'
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:342)
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveValueIfNecessary(BeanDefinitionValueResolver.java:113)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyPropertyValues(AbstractAutowireCapableBeanFactory.java:1707)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1452)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710)
	... 48 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/example/multidatasource/config/MyBatisDataSourceConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeInfoMapper.xml]'
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:633)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:330)
	... 61 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeInfoMapper.xml]'
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:648)
	... 71 common frames omitted
Caused by: java.io.IOException: Failed to parse mapping resource: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeInfoMapper.xml]'
	at org.mybatis.spring.SqlSessionFactoryBean.buildSqlSessionFactory(SqlSessionFactoryBean.java:700)
	at org.mybatis.spring.SqlSessionFactoryBean.afterPropertiesSet(SqlSessionFactoryBean.java:577)
	at org.mybatis.spring.SqlSessionFactoryBean.getObject(SqlSessionFactoryBean.java:720)
	at com.example.multidatasource.config.MyBatisDataSourceConfig.sqlSessionFactory(MyBatisDataSourceConfig.java:118)
	at com.example.multidatasource.config.MyBatisDataSourceConfig$$EnhancerBySpringCGLIB$$7678c27b.CGLIB$sqlSessionFactory$1(<generated>)
	at com.example.multidatasource.config.MyBatisDataSourceConfig$$EnhancerBySpringCGLIB$$7678c27b$$FastClassBySpringCGLIB$$e8b60c4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.example.multidatasource.config.MyBatisDataSourceConfig$$EnhancerBySpringCGLIB$$7678c27b.sqlSessionFactory(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 72 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error creating document instance.  Cause: org.xml.sax.SAXParseException; lineNumber: 37; columnNumber: 35; 元素内容必须由格式正确的字符数据或标记组成。
	at org.apache.ibatis.parsing.XPathParser.createDocument(XPathParser.java:262)
	at org.apache.ibatis.parsing.XPathParser.<init>(XPathParser.java:127)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.<init>(XMLMapperBuilder.java:85)
	at org.mybatis.spring.SqlSessionFactoryBean.buildSqlSessionFactory(SqlSessionFactoryBean.java:697)
	... 85 common frames omitted
Caused by: org.xml.sax.SAXParseException: 元素内容必须由格式正确的字符数据或标记组成。
	at com.sun.org.apache.xerces.internal.util.ErrorHandlerWrapper.createSAXParseException(ErrorHandlerWrapper.java:203)
	at com.sun.org.apache.xerces.internal.util.ErrorHandlerWrapper.fatalError(ErrorHandlerWrapper.java:177)
	at com.sun.org.apache.xerces.internal.impl.XMLErrorReporter.reportError(XMLErrorReporter.java:400)
	at com.sun.org.apache.xerces.internal.impl.XMLErrorReporter.reportError(XMLErrorReporter.java:327)
	at com.sun.org.apache.xerces.internal.impl.XMLScanner.reportFatalError(XMLScanner.java:1472)
	at com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl$FragmentContentDriver.startOfMarkup(XMLDocumentFragmentScannerImpl.java:2635)
	at com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl$FragmentContentDriver.next(XMLDocumentFragmentScannerImpl.java:2732)
	at com.sun.org.apache.xerces.internal.impl.XMLDocumentScannerImpl.next(XMLDocumentScannerImpl.java:602)
	at com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl.scanDocument(XMLDocumentFragmentScannerImpl.java:505)
	at com.sun.org.apache.xerces.internal.parsers.XML11Configuration.parse(XML11Configuration.java:842)
	at com.sun.org.apache.xerces.internal.parsers.XML11Configuration.parse(XML11Configuration.java:771)
	at com.sun.org.apache.xerces.internal.parsers.XMLParser.parse(XMLParser.java:141)
	at com.sun.org.apache.xerces.internal.parsers.DOMParser.parse(DOMParser.java:243)
	at com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderImpl.parse(DocumentBuilderImpl.java:339)
	at org.apache.ibatis.parsing.XPathParser.createDocument(XPathParser.java:260)
	... 88 common frames omitted
2025-08-13 17:07:16 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 34796 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-08-13 17:07:16 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-13 17:07:16 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-08-13 17:07:17 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerBaseInfoMapper.class]
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerScheduleMapper.class]
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\MaritimeBureauConfigMapper.class]
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\MaritimeInfoMapper.class]
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\ShipEngineMapper.class]
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageQualificationMapper.class]
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerBaseInfoMapper' and 'com.example.multidatasource.crew.mapper.SeafarerBaseInfoMapper' mapperInterface
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerScheduleMapper' and 'com.example.multidatasource.crew.mapper.SeafarerScheduleMapper' mapperInterface
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'maritimeBureauConfigMapper' and 'com.example.multidatasource.voyage.mapper.MaritimeBureauConfigMapper' mapperInterface
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'maritimeInfoMapper' and 'com.example.multidatasource.voyage.mapper.MaritimeInfoMapper' mapperInterface
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'shipEngineMapper' and 'com.example.multidatasource.voyage.mapper.ShipEngineMapper' mapperInterface
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-08-13 17:07:17 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageQualificationMapper' and 'com.example.multidatasource.voyage.mapper.VoyageQualificationMapper' mapperInterface
2025-08-13 17:07:18 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-08-13 17:07:18 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-13 17:07:18 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-13 17:07:19 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-08-13 17:07:19 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2784 ms
2025-08-13 17:07:19 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 17:07:19 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-08-13 17:07:19 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-08-13 17:07:19 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-08-13 17:07:19 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-08-13 17:07:19 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerBaseInfoMapper.xml]'
2025-08-13 17:07:19 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerScheduleMapper.xml]'
2025-08-13 17:07:19 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeBureauConfigMapper.xml]'
2025-08-13 17:07:19 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController': Unsatisfied dependency expressed through field 'authService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authServiceImpl': Unsatisfied dependency expressed through field 'userService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'userMapper' defined in file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]: Cannot resolve reference to bean 'sqlSessionFactory' while setting bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/example/multidatasource/config/MyBatisDataSourceConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeInfoMapper.xml]'
2025-08-13 17:07:19 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-13 17:07:19 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 17:07:19 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController': Unsatisfied dependency expressed through field 'authService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authServiceImpl': Unsatisfied dependency expressed through field 'userService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'userMapper' defined in file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]: Cannot resolve reference to bean 'sqlSessionFactory' while setting bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/example/multidatasource/config/MyBatisDataSourceConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeInfoMapper.xml]'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:713)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.example.multidatasource.MultiDataSourceApplication.main(MultiDataSourceApplication.java:17)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authServiceImpl': Unsatisfied dependency expressed through field 'userService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'userMapper' defined in file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]: Cannot resolve reference to bean 'sqlSessionFactory' while setting bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/example/multidatasource/config/MyBatisDataSourceConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeInfoMapper.xml]'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:713)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710)
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'userMapper' defined in file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]: Cannot resolve reference to bean 'sqlSessionFactory' while setting bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/example/multidatasource/config/MyBatisDataSourceConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeInfoMapper.xml]'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:713)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:693)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:408)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710)
	... 34 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'userMapper' defined in file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]: Cannot resolve reference to bean 'sqlSessionFactory' while setting bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/example/multidatasource/config/MyBatisDataSourceConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeInfoMapper.xml]'
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:342)
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveValueIfNecessary(BeanDefinitionValueResolver.java:113)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyPropertyValues(AbstractAutowireCapableBeanFactory.java:1707)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1452)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:710)
	... 48 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/example/multidatasource/config/MyBatisDataSourceConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeInfoMapper.xml]'
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:633)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:330)
	... 61 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.io.IOException: Failed to parse mapping resource: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeInfoMapper.xml]'
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:648)
	... 71 common frames omitted
Caused by: java.io.IOException: Failed to parse mapping resource: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeInfoMapper.xml]'
	at org.mybatis.spring.SqlSessionFactoryBean.buildSqlSessionFactory(SqlSessionFactoryBean.java:700)
	at org.mybatis.spring.SqlSessionFactoryBean.afterPropertiesSet(SqlSessionFactoryBean.java:577)
	at org.mybatis.spring.SqlSessionFactoryBean.getObject(SqlSessionFactoryBean.java:720)
	at com.example.multidatasource.config.MyBatisDataSourceConfig.sqlSessionFactory(MyBatisDataSourceConfig.java:118)
	at com.example.multidatasource.config.MyBatisDataSourceConfig$$EnhancerBySpringCGLIB$$27646946.CGLIB$sqlSessionFactory$1(<generated>)
	at com.example.multidatasource.config.MyBatisDataSourceConfig$$EnhancerBySpringCGLIB$$27646946$$FastClassBySpringCGLIB$$3a0a2a12.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.example.multidatasource.config.MyBatisDataSourceConfig$$EnhancerBySpringCGLIB$$27646946.sqlSessionFactory(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 72 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error creating document instance.  Cause: org.xml.sax.SAXParseException; lineNumber: 37; columnNumber: 35; 元素内容必须由格式正确的字符数据或标记组成。
	at org.apache.ibatis.parsing.XPathParser.createDocument(XPathParser.java:262)
	at org.apache.ibatis.parsing.XPathParser.<init>(XPathParser.java:127)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.<init>(XMLMapperBuilder.java:85)
	at org.mybatis.spring.SqlSessionFactoryBean.buildSqlSessionFactory(SqlSessionFactoryBean.java:697)
	... 85 common frames omitted
Caused by: org.xml.sax.SAXParseException: 元素内容必须由格式正确的字符数据或标记组成。
	at com.sun.org.apache.xerces.internal.util.ErrorHandlerWrapper.createSAXParseException(ErrorHandlerWrapper.java:203)
	at com.sun.org.apache.xerces.internal.util.ErrorHandlerWrapper.fatalError(ErrorHandlerWrapper.java:177)
	at com.sun.org.apache.xerces.internal.impl.XMLErrorReporter.reportError(XMLErrorReporter.java:400)
	at com.sun.org.apache.xerces.internal.impl.XMLErrorReporter.reportError(XMLErrorReporter.java:327)
	at com.sun.org.apache.xerces.internal.impl.XMLScanner.reportFatalError(XMLScanner.java:1472)
	at com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl$FragmentContentDriver.startOfMarkup(XMLDocumentFragmentScannerImpl.java:2635)
	at com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl$FragmentContentDriver.next(XMLDocumentFragmentScannerImpl.java:2732)
	at com.sun.org.apache.xerces.internal.impl.XMLDocumentScannerImpl.next(XMLDocumentScannerImpl.java:602)
	at com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl.scanDocument(XMLDocumentFragmentScannerImpl.java:505)
	at com.sun.org.apache.xerces.internal.parsers.XML11Configuration.parse(XML11Configuration.java:842)
	at com.sun.org.apache.xerces.internal.parsers.XML11Configuration.parse(XML11Configuration.java:771)
	at com.sun.org.apache.xerces.internal.parsers.XMLParser.parse(XMLParser.java:141)
	at com.sun.org.apache.xerces.internal.parsers.DOMParser.parse(DOMParser.java:243)
	at com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderImpl.parse(DocumentBuilderImpl.java:339)
	at org.apache.ibatis.parsing.XPathParser.createDocument(XPathParser.java:260)
	... 88 common frames omitted
2025-08-13 17:17:13 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 14528 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-08-13 17:17:13 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-13 17:17:13 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-08-13 17:17:14 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerBaseInfoMapper.class]
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerScheduleMapper.class]
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\MaritimeBureauConfigMapper.class]
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\MaritimeInfoMapper.class]
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\ShipEngineMapper.class]
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageQualificationMapper.class]
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerBaseInfoMapper' and 'com.example.multidatasource.crew.mapper.SeafarerBaseInfoMapper' mapperInterface
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerScheduleMapper' and 'com.example.multidatasource.crew.mapper.SeafarerScheduleMapper' mapperInterface
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'maritimeBureauConfigMapper' and 'com.example.multidatasource.voyage.mapper.MaritimeBureauConfigMapper' mapperInterface
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'maritimeInfoMapper' and 'com.example.multidatasource.voyage.mapper.MaritimeInfoMapper' mapperInterface
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'shipEngineMapper' and 'com.example.multidatasource.voyage.mapper.ShipEngineMapper' mapperInterface
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-08-13 17:17:14 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageQualificationMapper' and 'com.example.multidatasource.voyage.mapper.VoyageQualificationMapper' mapperInterface
2025-08-13 17:17:15 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-08-13 17:17:15 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-13 17:17:15 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-13 17:17:16 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-08-13 17:17:16 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2638 ms
2025-08-13 17:17:16 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 17:17:16 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-08-13 17:17:16 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-08-13 17:17:16 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-08-13 17:17:16 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-08-13 17:17:16 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerBaseInfoMapper.xml]'
2025-08-13 17:17:16 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerScheduleMapper.xml]'
2025-08-13 17:17:16 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\MaritimeBureauConfigMapper.xml]'
2025-08-13 17:17:16 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\ShipEngineMapper.xml]'
2025-08-13 17:17:16 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-08-13 17:17:16 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageQualificationMapper.xml]'
2025-08-13 17:17:17 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 42062e8f-275d-4aa5-a1cf-1598eb6ad793

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-13 17:17:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4656fcc5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2c16677c, org.springframework.security.web.context.SecurityContextPersistenceFilter@49889154, org.springframework.security.web.header.HeaderWriterFilter@2342f1ff, org.springframework.web.filter.CorsFilter@27d73d22, org.springframework.security.web.authentication.logout.LogoutFilter@3d033453, com.example.multidatasource.config.JwtAuthenticationFilter@64b0d1fa, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@368424db, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6e239337, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7fd751de, org.springframework.security.web.session.SessionManagementFilter@59b447a4, org.springframework.security.web.access.ExceptionTranslationFilter@2a19a0fe, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@171aa66]
2025-08-13 17:17:18 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 54321 (http) with context path '/multi/source/api'
2025-08-13 17:17:18 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 6.197 seconds (JVM running for 7.801)
2025-08-13 17:28:33 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication v1.0.0 using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 32028 (D:\augmentSpace\target\multi-datasource-api-1.0.0.jar started by chiqiyun in D:\augmentSpace)
2025-08-13 17:28:33 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-13 17:28:33 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-08-13 17:28:34 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/auth/mapper/UserMapper.class]
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/crew/mapper/CrewMapper.class]
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/crew/mapper/OilVoyageConsumptionMapper.class]
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/crew/mapper/SeafarerBaseInfoMapper.class]
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/crew/mapper/SeafarerScheduleMapper.class]
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/voyage/mapper/MaritimeBureauConfigMapper.class]
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/voyage/mapper/MaritimeInfoMapper.class]
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/voyage/mapper/ShipEngineMapper.class]
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/voyage/mapper/VoyageMapper.class]
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/voyage/mapper/VoyageQualificationMapper.class]
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'crewMapper'.
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'oilVoyageConsumptionMapper'.
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerBaseInfoMapper' and 'com.example.multidatasource.crew.mapper.SeafarerBaseInfoMapper' mapperInterface
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'seafarerBaseInfoMapper'.
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerScheduleMapper' and 'com.example.multidatasource.crew.mapper.SeafarerScheduleMapper' mapperInterface
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'seafarerScheduleMapper'.
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'maritimeBureauConfigMapper' and 'com.example.multidatasource.voyage.mapper.MaritimeBureauConfigMapper' mapperInterface
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'maritimeBureauConfigMapper'.
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'maritimeInfoMapper' and 'com.example.multidatasource.voyage.mapper.MaritimeInfoMapper' mapperInterface
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'maritimeInfoMapper'.
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'shipEngineMapper' and 'com.example.multidatasource.voyage.mapper.ShipEngineMapper' mapperInterface
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'shipEngineMapper'.
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'voyageMapper'.
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageQualificationMapper' and 'com.example.multidatasource.voyage.mapper.VoyageQualificationMapper' mapperInterface
2025-08-13 17:28:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'voyageQualificationMapper'.
2025-08-13 17:28:36 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-08-13 17:28:36 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-13 17:28:36 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-13 17:28:37 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-08-13 17:28:37 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4116 ms
2025-08-13 17:28:37 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 17:28:37 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-08-13 17:28:38 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/auth/UserMapper.xml]'
2025-08-13 17:28:38 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/crew/CrewMapper.xml]'
2025-08-13 17:28:38 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/crew/OilVoyageConsumptionMapper.xml]'
2025-08-13 17:28:38 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/crew/SeafarerBaseInfoMapper.xml]'
2025-08-13 17:28:38 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/crew/SeafarerScheduleMapper.xml]'
2025-08-13 17:28:38 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/voyage/MaritimeBureauConfigMapper.xml]'
2025-08-13 17:28:38 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/voyage/ShipEngineMapper.xml]'
2025-08-13 17:28:38 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/voyage/VoyageMapper.xml]'
2025-08-13 17:28:38 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/voyage/VoyageQualificationMapper.xml]'
2025-08-13 17:28:40 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 8b2667d2-b867-4e88-8df7-28ed035eaa3f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-13 17:28:40 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@37fb0bed, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@a82c5f1, org.springframework.security.web.context.SecurityContextPersistenceFilter@3fa247d1, org.springframework.security.web.header.HeaderWriterFilter@4d6025c5, org.springframework.web.filter.CorsFilter@7b7fdc8, org.springframework.security.web.authentication.logout.LogoutFilter@5f77d0f9, com.example.multidatasource.config.JwtAuthenticationFilter@68c72235, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4e928fbf, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3224a577, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@51c693d, org.springframework.security.web.session.SessionManagementFilter@407a7f2a, org.springframework.security.web.access.ExceptionTranslationFilter@7b993c65, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5d99c6b5]
2025-08-13 17:28:42 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 54321 (http) with context path '/multi/source/api'
2025-08-13 17:28:42 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 10.66 seconds (JVM running for 11.479)
