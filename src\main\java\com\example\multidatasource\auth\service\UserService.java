package com.example.multidatasource.auth.service;

import com.example.multidatasource.auth.entity.User;
import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.common.dto.RegisterRequest;
import com.example.multidatasource.common.dto.UserQueryDTO;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 根据用户名查询用户
     */
    User getUserByUsername(String username);

    /**
     * 根据ID查询用户
     */
    User getUserById(Long id);

    /**
     * 创建用户
     */
    boolean createUser(RegisterRequest registerRequest);

    /**
     * 更新用户信息
     */
    boolean updateUser(User user);

    /**
     * 删除用户
     */
    boolean deleteUser(Long id);

    /**
     * 更新用户状态
     */
    boolean updateUserStatus(Long id, String status);

    /**
     * 更新用户密码
     */
    boolean updateUserPassword(Long id, String oldPassword, String newPassword);

    /**
     * 重置用户密码（管理员操作）
     */
    boolean resetUserPassword(Long id, String newPassword);

    /**
     * 更新最后登录信息
     */
    boolean updateLastLoginInfo(Long id, String ip);

    /**
     * 分页查询用户列表
     */
    PageResult<User> getUserList(Integer page, Integer size);

    /**
     * 多条件查询用户（分页）
     */
    PageResult<User> searchUsers(UserQueryDTO queryDTO);

    /**
     * 根据角色查询用户
     */
    List<User> getUsersByRole(String role);

    /**
     * 根据状态查询用户
     */
    List<User> getUsersByStatus(String status);

    /**
     * 检查用户名是否存在
     */
    boolean isUsernameExists(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean isEmailExists(String email);

    /**
     * 验证密码
     */
    boolean validatePassword(String rawPassword, String encodedPassword);

    /**
     * 加密密码
     */
    String encodePassword(String rawPassword);

    /**
     * 统计用户总数
     */
    int getUserCount();
}
