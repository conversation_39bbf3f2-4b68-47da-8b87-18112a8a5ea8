package com.example.multidatasource.voyage.service;

import com.example.multidatasource.voyage.entity.MaritimeInfo;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * 海事局爬虫服务
 */
@Slf4j
@Service
public class MaritimeCrawlerService {

    private static final String BASE_URL = "https://www.msa.gov.cn";
    private static final String API_URL = "https://www.msa.gov.cn/page/openInfo/articleList.do";
    
    private final OkHttpClient httpClient;

    public MaritimeCrawlerService() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build();
    }

    /**
     * 爬取海事信息
     */
    public List<MaritimeInfo> crawlMaritimeInfo(String bureau, String channelId, String infoType, int days) throws IOException {
        List<MaritimeInfo> result = new ArrayList<>();
        
        // 构建请求URL
        String url = API_URL + "?channelId=" + channelId + "&pageSize=20&pageNo=1&isParent=0";
        
        // 创建HTTP请求
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Cache-Control", "max-age=0")
                .addHeader("Connection", "keep-alive")
                .addHeader("Referer", "https://www.bing.com/")
                .addHeader("Sec-Fetch-Dest", "document")
                .addHeader("Sec-Fetch-Mode", "navigate")
                .addHeader("Sec-Fetch-Site", "cross-site")
                .addHeader("Sec-Fetch-User", "?1")
                .addHeader("Upgrade-Insecure-Requests", "1")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
                .addHeader("sec-ch-ua", "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"")
                .addHeader("sec-ch-ua-mobile", "?0")
                .addHeader("sec-ch-ua-platform", "\"Windows\"")
                .build();

        // 执行请求
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("HTTP请求失败: " + response.code());
            }
            
            String html = response.body().string();
            log.info("获取到HTML内容，长度: {}", html.length());
            
            // 解析HTML
            result = parseMaritimeList(html, bureau, infoType, days);
        }
        
        return result;
    }

    /**
     * 解析海事信息列表页面
     */
    private List<MaritimeInfo> parseMaritimeList(String html, String bureau, String infoType, int days) {
        List<MaritimeInfo> result = new ArrayList<>();
        
        try {
            Document doc = Jsoup.parse(html);
            
            // 查找主要的ul列表容器
            Elements ulElements = doc.select("ul.main_list_ul");
            if (ulElements.isEmpty()) {
                log.warn("未找到主列表容器");
                return result;
            }
            
            Element ulElement = ulElements.first();
            Elements liElements = ulElement.select("li.main_list_li");
            
            log.info("找到 {} 个列表项", liElements.size());
            
            for (Element li : liElements) {
                try {
                    MaritimeInfo item = parseListItem(li, bureau, infoType, days);
                    if (item != null) {
                        result.add(item);
                    }
                } catch (Exception e) {
                    log.error("解析列表项失败: {}", e.getMessage());
                }
            }
            
        } catch (Exception e) {
            log.error("解析HTML失败: {}", e.getMessage(), e);
        }
        
        return result;
    }

    /**
     * 解析单个列表项
     */
    private MaritimeInfo parseListItem(Element li, String bureau, String infoType, int days) {
        try {
            // 提取链接
            Elements linkElements = li.select("a[href]");
            if (linkElements.isEmpty()) {
                return null;
            }
            
            Element linkElement = linkElements.first();
            String href = linkElement.attr("href");
            
            // 构建完整URL
            String fullUrl;
            if (href.startsWith("/")) {
                fullUrl = BASE_URL + href;
            } else {
                fullUrl = href;
            }
            
            // 提取标题和日期
            Elements spanElements = linkElement.select("span");
            if (spanElements.size() < 2) {
                return null;
            }
            
            String title = cleanText(spanElements.get(0).text());
            String dateText = cleanText(spanElements.get(1).text());
            
            // 清理日期格式
            String cleanDate = dateText.replaceAll("[\\[\\]\\s]", "");
            
            // 检查日期是否在指定天数内
            if (!isWithinDays(cleanDate, days)) {
                return null;
            }
            
            // 提取文章ID
            String articleId = extractArticleId(fullUrl);
            if (articleId == null) {
                log.warn("无法提取文章ID: {}", fullUrl);
                return null;
            }
            
            // 获取详细内容
            String detailContent = fetchArticleDetail(fullUrl);
            
            // 构建结果对象
            MaritimeInfo item = new MaritimeInfo();
            item.setArticleId(articleId);
            item.setBureauName(bureau);
            item.setTitle(title);
            item.setInfoType(infoType);
            item.setPublishDate(LocalDate.parse(cleanDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            item.setUrl(fullUrl);
            item.setDetailContent(detailContent);
            
            return item;
            
        } catch (Exception e) {
            log.error("解析列表项失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 提取文章ID
     */
    private String extractArticleId(String url) {
        try {
            // 从URL中提取articleId参数
            Pattern pattern = Pattern.compile("articleId=([^&]+)");
            java.util.regex.Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return matcher.group(1);
            }
        } catch (Exception e) {
            log.error("提取文章ID失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取文章详细内容
     */
    private String fetchArticleDetail(String url) {
        try {
            log.debug("正在获取详情: {}", url);
            
            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    return "获取详情失败: HTTP " + response.code();
                }
                
                String html = response.body().string();
                return extractContentFromHtml(html);
            }
            
        } catch (Exception e) {
            log.error("获取详情失败: {}", e.getMessage());
            return "获取详情失败: " + e.getMessage();
        }
    }

    /**
     * 从HTML中提取内容
     */
    private String extractContentFromHtml(String html) {
        try {
            Document doc = Jsoup.parse(html);
            
            // 尝试多种内容选择器
            String[] contentSelectors = {
                "#ch_p",
                ".content",
                "#content", 
                ".main-content"
            };
            
            for (String selector : contentSelectors) {
                Elements elements = doc.select(selector);
                if (!elements.isEmpty()) {
                    String content = elements.first().text();
                    return cleanText(content);
                }
            }
            
            return "无法提取详细内容";
            
        } catch (Exception e) {
            return "内容解析失败: " + e.getMessage();
        }
    }

    /**
     * 清理文本内容
     */
    private String cleanText(String text) {
        if (text == null) {
            return "";
        }
        
        // 去除不需要的功能按钮文本
        String[] unwantedTexts = {"收藏", "打印本页", "关闭窗口", "下载PDF文件"};
        for (String unwanted : unwantedTexts) {
            text = text.replace(unwanted, "");
        }
        
        // 清理多余的空白字符
        text = text.replaceAll("\\s+", " ");
        
        return text.trim();
    }

    /**
     * 检查日期是否在指定天数内
     */
    private boolean isWithinDays(String dateStr, int days) {
        try {
            if (dateStr.length() != 10 || !dateStr.contains("-")) {
                return false;
            }
            
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDate today = LocalDate.now();
            LocalDate cutoffDate = today.minusDays(days - 1);
            
            return !date.isBefore(cutoffDate);
            
        } catch (DateTimeParseException e) {
            log.error("日期解析失败: {}", dateStr);
            return false;
        }
    }
}
