<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e3713aad-0f6f-4249-8f2d-27d453ba6c40" name="Changes" comment="测试提交">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/environment-variables.md" beforeDir="false" afterPath="$PROJECT_DIR$/docs/environment-variables.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/common/dto/LoginResponse.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/common/dto/LoginResponse.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/common/util/JwtUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/common/util/JwtUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/config/OpenApiConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/config/OpenApiConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/config/SecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/config/SecurityConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-prod.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-prod.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/application-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/application-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/application-prod.yml" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/application-prod.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/OpenApiConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/multidatasource/config/OpenApiConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/surefire-reports/TEST-com.example.multidatasource.PasswordTest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/target/surefire-reports/TEST-com.example.multidatasource.PasswordTest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/surefire-reports/com.example.multidatasource.PasswordTest.txt" beforeDir="false" afterPath="$PROJECT_DIR$/target/surefire-reports/com.example.multidatasource.PasswordTest.txt" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHome" value="F:/learing/apache-maven-3.6.1/apache-maven-3.6.1" />
        <option name="useMavenConfig" value="true" />
        <option name="userSettingsFile" value="F:\learing\apache-maven-3.6.1\apache-maven-3.6.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectId" id="2yzQndRJxrZ03OWCyjudPjDAcqy" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="false" />
    <option name="showLibraryContents" value="true" />
    <option name="showScratchesAndConsoles" value="false" />
    <option name="showVisibilityIcons" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/augmentSpace&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;vcs.Subversion&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;96e4b2703bc3d3c2b2f618cf5e956881&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\augmentSpace\src\main\java\com\example\multidatasource\crew\entity" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.example.multidatasource.crew.controller" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.MultiDataSourceApplication">
    <configuration name="PasswordTest.testPasswordMatching" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="multi-datasource-api" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.multidatasource.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.example.multidatasource" />
      <option name="MAIN_CLASS_NAME" value="com.example.multidatasource.PasswordTest" />
      <option name="METHOD_NAME" value="testPasswordMatching" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MultiDataSourceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="multi-datasource-api" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.multidatasource.MultiDataSourceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.PasswordTest.testPasswordMatching" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\augmentSpace" />
          <option name="myCopyRoot" value="D:\augmentSpace" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\augmentSpace" />
          <option name="myCopyRoot" value="D:\augmentSpace" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="e3713aad-0f6f-4249-8f2d-27d453ba6c40" name="Changes" comment="" />
      <created>1750835089151</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750835089151</updated>
      <workItem from="1750835094034" duration="10912000" />
      <workItem from="1752628014044" duration="45822000" />
      <workItem from="1752828790599" duration="6830000" />
    </task>
    <task id="LOCAL-00001" summary="测试提交">
      <created>1752828861894</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752828861894</updated>
    </task>
    <task id="LOCAL-00002" summary="测试提交">
      <created>1752828916703</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752828916703</updated>
    </task>
    <task id="LOCAL-00003" summary="测试提交">
      <created>1752829048148</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752829048148</updated>
    </task>
    <option name="localTasksCounter" value="4" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="首次提交" />
    <MESSAGE value="测试提交" />
    <option name="LAST_COMMIT_MESSAGE" value="测试提交" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>