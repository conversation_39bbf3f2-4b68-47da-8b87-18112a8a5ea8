package com.example.multidatasource.common.aspect;

import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.common.config.DataSourceContextHolder;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 数据源切换AOP切面
 * 拦截带有@DataSource注解的方法，自动切换数据源
 */
@Aspect
@Component
@Order(1) // 确保在事务切面之前执行
public class DataSourceAspect {
    
    private static final Logger logger = LoggerFactory.getLogger(DataSourceAspect.class);
    
    /**
     * 切点：拦截所有带有@DataSource注解的方法
     */
    @Pointcut("@annotation(com.example.multidatasource.common.annotation.DataSource) || @within(com.example.multidatasource.common.annotation.DataSource)")
    public void dataSourcePointcut() {}
    
    /**
     * 环绕通知：在方法执行前切换数据源，执行后恢复
     */
    @Around("dataSourcePointcut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        
        // 获取方法上的@DataSource注解
        DataSource dataSource = AnnotationUtils.findAnnotation(method, DataSource.class);
        
        // 如果方法上没有注解，尝试获取类上的注解
        if (dataSource == null) {
            dataSource = AnnotationUtils.findAnnotation(method.getDeclaringClass(), DataSource.class);
        }
        
        String targetDataSource = null;
        String originalDataSource = DataSourceContextHolder.getDataSourceType();
        
        try {
            if (dataSource != null) {
                // 优先使用name属性，如果为空则使用value属性
                if (!dataSource.name().isEmpty()) {
                    targetDataSource = dataSource.name();
                } else {
                    targetDataSource = dataSource.value().getCode();
                }
                
                // 验证数据源是否有效
                if (DataSourceContextHolder.isValidDataSourceType(targetDataSource)) {
                    DataSourceContextHolder.setDataSourceType(targetDataSource);
                    logger.info("Switched to data source: {} for method: {}.{}", 
                               targetDataSource, 
                               method.getDeclaringClass().getSimpleName(), 
                               method.getName());
                } else {
                    logger.warn("Invalid data source type: {}, using default", targetDataSource);
                }
            }
            
            // 执行目标方法
            return point.proceed();
            
        } catch (Exception e) {
            logger.error("Error occurred while switching data source", e);
            throw e;
        } finally {
            // 恢复原始数据源或清除上下文
            if (targetDataSource != null) {
                if (originalDataSource != null) {
                    DataSourceContextHolder.setDataSourceType(originalDataSource);
                    logger.debug("Restored data source to: {}", originalDataSource);
                } else {
                    DataSourceContextHolder.clearDataSourceType();
                    logger.debug("Cleared data source context");
                }
            }
        }
    }
}
