<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b2cd344a-2f19-44b4-9adb-0fccee262d2f" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/logs/multi-datasource-dev.log" beforeDir="false" afterPath="$PROJECT_DIR$/logs/multi-datasource-dev.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/voyage/controller/MaritimeScheduleController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/voyage/controller/MaritimeScheduleController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/createdFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/createdFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/test-classes/com/example/multidatasource/PasswordTest.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/test-classes/com/example/multidatasource/PasswordTest.class" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="F:\learing\apache-maven-3.6.1\mavenRepository" />
        <option name="mavenHome" value="F:/learing/apache-maven-3.6.1/apache-maven-3.6.1" />
        <option name="useMavenConfig" value="true" />
        <option name="userSettingsFile" value="F:\learing\apache-maven-3.6.1\apache-maven-3.6.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="workspaceImportEnabled" value="true" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="ProjectId" id="31E3kCFBE8Oj1g5Q1DeLX6QRJbu" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="false" />
    <option name="showLibraryContents" value="true" />
    <option name="showScratchesAndConsoles" value="false" />
    <option name="showVisibilityIcons" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "last_opened_file_path": "D:/augmentSpace",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Global Libraries",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "configurable.group.build",
    "spring.configuration.checksum": "96e4b2703bc3d3c2b2f618cf5e956881",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="MultiDataSourceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="multi-datasource-api" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.multidatasource.MultiDataSourceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\augmentSpace" />
          <option name="myCopyRoot" value="D:\augmentSpace" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\augmentSpace" />
          <option name="myCopyRoot" value="D:\augmentSpace" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b2cd344a-2f19-44b4-9adb-0fccee262d2f" name="Changes" comment="" />
      <created>1755075550203</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755075550203</updated>
      <workItem from="1755075553439" duration="2606000" />
      <workItem from="1755078220646" duration="8032000" />
    </task>
    <task id="LOCAL-00001" summary="海事局定时任务完成后！">
      <created>1755131150123</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1755131150123</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="海事局定时任务完成后！" />
    <option name="LAST_COMMIT_MESSAGE" value="海事局定时任务完成后！" />
  </component>
</project>