2025-07-18 15:17:53 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 22932 (D:\augmentSpace\target\classes started by <PERSON><PERSON><PERSON><PERSON> in D:\augmentSpace)
2025-07-18 15:17:53 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-18 15:17:53 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-07-18 15:17:54 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-18 15:17:54 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-07-18 15:17:54 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-07-18 15:17:54 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-07-18 15:17:54 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-07-18 15:17:54 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-07-18 15:17:54 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-07-18 15:17:54 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-07-18 15:17:54 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-07-18 15:17:55 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-18 15:17:55 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 15:17:55 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 15:17:55 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 15:17:55 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2429 ms
2025-07-18 15:17:55 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-18 15:17:55 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-07-18 15:17:56 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-07-18 15:17:56 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-07-18 15:17:56 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-07-18 15:17:56 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-07-18 15:17:56 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 0e2c7111-7215-4311-8284-ff1e82614423

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-18 15:17:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@51c8f62c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@41a374be, org.springframework.security.web.context.SecurityContextPersistenceFilter@22c75c01, org.springframework.security.web.header.HeaderWriterFilter@7bac686b, org.springframework.web.filter.CorsFilter@11f9535b, org.springframework.security.web.authentication.logout.LogoutFilter@2f4ba1ae, com.example.multidatasource.config.JwtAuthenticationFilter@205bed61, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5b275811, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@c017175, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@8cc8cdb, org.springframework.security.web.session.SessionManagementFilter@49f40c00, org.springframework.security.web.access.ExceptionTranslationFilter@37d28f02, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@69f0b0f4]
2025-07-18 15:17:57 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/multi/source/api'
2025-07-18 15:17:57 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 4.584 seconds (JVM running for 5.582)
2025-07-18 15:23:29 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 20520 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-07-18 15:23:29 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-18 15:23:29 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-07-18 15:23:30 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-18 15:23:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-07-18 15:23:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-07-18 15:23:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-07-18 15:23:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-07-18 15:23:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-07-18 15:23:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-07-18 15:23:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-07-18 15:23:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-07-18 15:23:30 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-18 15:23:30 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 15:23:30 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 15:23:31 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 15:23:31 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1787 ms
2025-07-18 15:23:31 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-18 15:23:31 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-07-18 15:23:31 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-07-18 15:23:31 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-07-18 15:23:31 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-07-18 15:23:31 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-07-18 15:23:31 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 0ad93b6d-8fdf-498b-948b-f498bd9ccf9c

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-18 15:23:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6ca8fcf3, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@69f0b0f4, org.springframework.security.web.context.SecurityContextPersistenceFilter@52d6d273, org.springframework.security.web.header.HeaderWriterFilter@6056232d, org.springframework.web.filter.CorsFilter@66933239, org.springframework.security.web.authentication.logout.LogoutFilter@63d5874f, com.example.multidatasource.config.JwtAuthenticationFilter@6dfcffb5, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@46e64760, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2f0ed952, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2f7efd0b, org.springframework.security.web.session.SessionManagementFilter@1806bc4c, org.springframework.security.web.access.ExceptionTranslationFilter@6af91cc8, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2629d5dc]
2025-07-18 15:23:32 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/multi/source/api'
2025-07-18 15:23:32 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 3.848 seconds (JVM running for 5.06)
2025-07-18 15:25:09 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 21324 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-07-18 15:25:09 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-18 15:25:09 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-07-18 15:25:11 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-18 15:25:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-07-18 15:25:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-07-18 15:25:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-07-18 15:25:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-07-18 15:25:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-07-18 15:25:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-07-18 15:25:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-07-18 15:25:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-07-18 15:25:11 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-07-18 15:25:11 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 15:25:11 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 15:25:12 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 15:25:12 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2069 ms
2025-07-18 15:25:12 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-18 15:25:12 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-07-18 15:25:12 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-07-18 15:25:12 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-07-18 15:25:12 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-07-18 15:25:12 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-07-18 15:25:12 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 39c71793-9431-40e7-b744-3655815a0825

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-18 15:25:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6abdec0e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@28f4f300, org.springframework.security.web.context.SecurityContextPersistenceFilter@462e1e64, org.springframework.security.web.header.HeaderWriterFilter@6232ffdb, org.springframework.web.filter.CorsFilter@2b5c4f17, org.springframework.security.web.authentication.logout.LogoutFilter@38f77cd9, com.example.multidatasource.config.JwtAuthenticationFilter@4096aa05, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@52d6d273, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@46e64760, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6ca8fcf3, org.springframework.security.web.session.SessionManagementFilter@61a91c9b, org.springframework.security.web.access.ExceptionTranslationFilter@6c1cfa53, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@73ae0257]
2025-07-18 15:25:13 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 54321 (http) with context path '/multi/source/api'
2025-07-18 15:25:13 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 4.658 seconds (JVM running for 5.846)
2025-07-18 15:25:21 [http-nio-54321-exec-1] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 15:25:21 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 15:25:21 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-18 15:25:23 [http-nio-54321-exec-9] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 884 ms
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 15:25:46 [http-nio-54321-exec-10] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.getUserByUsername
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@615beb9e] was not registered for synchronization because synchronization is not active
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-18 15:25:46 [http-nio-54321-exec-10] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 15:25:46 [http-nio-54321-exec-10] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@702283632 wrapping com.mysql.cj.jdbc.ConnectionImpl@738c4748] will not be managed by Spring
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.selectUserByUsername - ==>  Preparing: SELECT * FROM t_ds_users WHERE username = ?
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.selectUserByUsername - ==> Parameters: admin(String)
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.selectUserByUsername - <==      Total: 1
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@615beb9e]
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 15:25:46 [http-nio-54321-exec-10] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.validatePassword
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 15:25:46 [http-nio-54321-exec-10] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.updateLastLoginInfo
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@48803610] was not registered for synchronization because synchronization is not active
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1703772916 wrapping com.mysql.cj.jdbc.ConnectionImpl@738c4748] will not be managed by Spring
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==>  Preparing: UPDATE t_ds_users SET last_login_time = ?, last_login_ip = ?, updated_time = NOW() WHERE id = ?
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==> Parameters: 2025-07-18T15:25:46.842(LocalDateTime), 0:0:0:0:0:0:0:1(String), 3(Long)
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.updateLastLoginInfo - <==    Updates: 1
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@48803610]
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:25:46 [http-nio-54321-exec-10] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:25:47 [http-nio-54321-exec-10] INFO  c.e.m.a.service.impl.AuthServiceImpl - User admin logged in successfully from IP: 0:0:0:0:0:0:0:1
2025-07-18 15:26:12 [http-nio-54321-exec-1] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-18 15:26:12 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 15:26:12 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:26:12 [http-nio-54321-exec-1] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: crew for method: OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel
2025-07-18 15:26:12 [http-nio-54321-exec-1] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取每个船舶最新的航次油耗记录（包含轻油和重油明细）- vesselId: null, vesselName: null, startDate: null, endDate: null
2025-07-18 15:26:12 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 15:26:12 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51d2d5fa] was not registered for synchronization because synchronization is not active
2025-07-18 15:26:12 [http-nio-54321-exec-1] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:26:12 [http-nio-54321-exec-1] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-18 15:26:12 [http-nio-54321-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-07-18 15:26:17 [http-nio-54321-exec-1] ERROR com.zaxxer.hikari.pool.HikariPool - HikariPool-2 - Exception during pool initialization.
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource.getConnection(AbstractRoutingDataSource.java:194)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:348)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:89)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:90)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	at com.sun.proxy.$Proxy87.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy95.getLatestVoyageConsumptionByVessel(Unknown Source)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionServiceImpl.java:31)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$FastClassBySpringCGLIB$$4f22a582.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$EnhancerBySpringCGLIB$$dfaeaba4.getLatestVoyageConsumptionByVessel(<generated>)
	at com.example.multidatasource.crew.controller.OilVoyageConsumptionController.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionController.java:36)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.multidatasource.config.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 158 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 161 common frames omitted
2025-07-18 15:26:17 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51d2d5fa]
2025-07-18 15:26:17 [http-nio-54321-exec-1] ERROR c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取船舶最新航次油耗记录失败
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]
### The error may involve com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper.getLatestVoyageConsumptionByVessel
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy87.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy95.getLatestVoyageConsumptionByVessel(Unknown Source)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionServiceImpl.java:31)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$FastClassBySpringCGLIB$$4f22a582.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$EnhancerBySpringCGLIB$$dfaeaba4.getLatestVoyageConsumptionByVessel(<generated>)
	at com.example.multidatasource.crew.controller.OilVoyageConsumptionController.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionController.java:36)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.multidatasource.config.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]
### The error may involve com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper.getLatestVoyageConsumptionByVessel
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:156)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 127 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:84)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:348)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:89)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:90)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	... 134 common frames omitted
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource.getConnection(AbstractRoutingDataSource.java:194)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	... 144 common frames omitted
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815)
	... 158 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 161 common frames omitted
2025-07-18 15:26:17 [http-nio-54321-exec-1] ERROR c.e.m.common.aspect.DataSourceAspect - Error occurred while switching data source
java.lang.RuntimeException: 获取船舶最新航次油耗记录失败: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]
### The error may involve com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper.getLatestVoyageConsumptionByVessel
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionServiceImpl.java:37)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$FastClassBySpringCGLIB$$4f22a582.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$EnhancerBySpringCGLIB$$dfaeaba4.getLatestVoyageConsumptionByVessel(<generated>)
	at com.example.multidatasource.crew.controller.OilVoyageConsumptionController.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionController.java:36)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.multidatasource.config.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-18 15:26:17 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:26:17 [http-nio-54321-exec-1] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:26:17 [http-nio-54321-exec-1] ERROR c.e.m.c.c.OilVoyageConsumptionController - 获取船舶最新航次油耗记录失败
java.lang.RuntimeException: 获取船舶最新航次油耗记录失败: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
### The error may exist in file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]
### The error may involve com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper.getLatestVoyageConsumptionByVessel
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionServiceImpl.java:37)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$FastClassBySpringCGLIB$$4f22a582.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$EnhancerBySpringCGLIB$$dfaeaba4.getLatestVoyageConsumptionByVessel(<generated>)
	at com.example.multidatasource.crew.controller.OilVoyageConsumptionController.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionController.java:36)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.multidatasource.config.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-18 15:30:21 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 9604 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-07-18 15:30:21 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-18 15:30:21 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-07-18 15:30:22 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-18 15:30:22 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-07-18 15:30:22 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-07-18 15:30:22 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-07-18 15:30:22 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-07-18 15:30:22 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-07-18 15:30:22 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-07-18 15:30:22 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-07-18 15:30:22 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-07-18 15:30:23 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-07-18 15:30:23 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 15:30:23 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 15:30:23 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 15:30:23 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2360 ms
2025-07-18 15:30:23 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-18 15:30:23 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-07-18 15:30:24 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-07-18 15:30:24 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-07-18 15:30:24 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-07-18 15:30:24 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-07-18 15:30:24 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: a26f0bba-c604-4f74-9950-7c34b5ec33ee

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-18 15:30:24 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4f327096, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1d61a348, org.springframework.security.web.context.SecurityContextPersistenceFilter@5b275811, org.springframework.security.web.header.HeaderWriterFilter@58b91d57, org.springframework.web.filter.CorsFilter@78a515e4, org.springframework.security.web.authentication.logout.LogoutFilter@204abeff, com.example.multidatasource.config.JwtAuthenticationFilter@46d63dbb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@c017175, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4832f03b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@d2291de, org.springframework.security.web.session.SessionManagementFilter@2f9a4401, org.springframework.security.web.access.ExceptionTranslationFilter@107bfcb2, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6abdec0e]
2025-07-18 15:30:25 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 54321 (http) with context path '/multi/source/api'
2025-07-18 15:30:25 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 5.055 seconds (JVM running for 6.176)
2025-07-18 15:30:39 [http-nio-54321-exec-1] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 15:30:39 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 15:30:39 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-18 15:30:40 [http-nio-54321-exec-1] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-18 15:30:40 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 15:30:40 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:30:40 [http-nio-54321-exec-1] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: crew for method: OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel
2025-07-18 15:30:40 [http-nio-54321-exec-1] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取每个船舶最新的航次油耗记录（包含轻油和重油明细）- vesselId: null, vesselName: null, startDate: null, endDate: null
2025-07-18 15:30:40 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 15:30:40 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4649240d] was not registered for synchronization because synchronization is not active
2025-07-18 15:30:40 [http-nio-54321-exec-1] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:30:40 [http-nio-54321-exec-1] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-18 15:30:40 [http-nio-54321-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 15:30:42 [http-nio-54321-exec-1] ERROR com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Exception during pool initialization.
java.sql.SQLSyntaxErrorException: Unknown database 'erp'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource.getConnection(AbstractRoutingDataSource.java:194)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:348)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:89)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:90)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	at com.sun.proxy.$Proxy87.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy95.getLatestVoyageConsumptionByVessel(Unknown Source)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionServiceImpl.java:31)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$FastClassBySpringCGLIB$$4f22a582.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$EnhancerBySpringCGLIB$$41b7cd34.getLatestVoyageConsumptionByVessel(<generated>)
	at com.example.multidatasource.crew.controller.OilVoyageConsumptionController.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionController.java:36)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.multidatasource.config.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-18 15:30:42 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4649240d]
2025-07-18 15:30:42 [http-nio-54321-exec-1] ERROR c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取船舶最新航次油耗记录失败
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLSyntaxErrorException: Unknown database 'erp'
### The error may exist in file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]
### The error may involve com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper.getLatestVoyageConsumptionByVessel
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLSyntaxErrorException: Unknown database 'erp'
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy87.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy95.getLatestVoyageConsumptionByVessel(Unknown Source)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionServiceImpl.java:31)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$FastClassBySpringCGLIB$$4f22a582.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$EnhancerBySpringCGLIB$$41b7cd34.getLatestVoyageConsumptionByVessel(<generated>)
	at com.example.multidatasource.crew.controller.OilVoyageConsumptionController.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionController.java:36)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.multidatasource.config.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLSyntaxErrorException: Unknown database 'erp'
### The error may exist in file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]
### The error may involve com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper.getLatestVoyageConsumptionByVessel
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLSyntaxErrorException: Unknown database 'erp'
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:156)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 127 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLSyntaxErrorException: Unknown database 'erp'
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:84)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:348)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:89)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:90)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	... 134 common frames omitted
Caused by: java.sql.SQLSyntaxErrorException: Unknown database 'erp'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource.getConnection(AbstractRoutingDataSource.java:194)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	... 144 common frames omitted
2025-07-18 15:30:42 [http-nio-54321-exec-1] ERROR c.e.m.common.aspect.DataSourceAspect - Error occurred while switching data source
java.lang.RuntimeException: 获取船舶最新航次油耗记录失败: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLSyntaxErrorException: Unknown database 'erp'
### The error may exist in file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]
### The error may involve com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper.getLatestVoyageConsumptionByVessel
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLSyntaxErrorException: Unknown database 'erp'
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionServiceImpl.java:37)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$FastClassBySpringCGLIB$$4f22a582.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$EnhancerBySpringCGLIB$$41b7cd34.getLatestVoyageConsumptionByVessel(<generated>)
	at com.example.multidatasource.crew.controller.OilVoyageConsumptionController.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionController.java:36)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.multidatasource.config.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-18 15:30:42 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:30:42 [http-nio-54321-exec-1] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:30:42 [http-nio-54321-exec-1] ERROR c.e.m.c.c.OilVoyageConsumptionController - 获取船舶最新航次油耗记录失败
java.lang.RuntimeException: 获取船舶最新航次油耗记录失败: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLSyntaxErrorException: Unknown database 'erp'
### The error may exist in file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]
### The error may involve com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper.getLatestVoyageConsumptionByVessel
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is java.sql.SQLSyntaxErrorException: Unknown database 'erp'
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionServiceImpl.java:37)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$FastClassBySpringCGLIB$$4f22a582.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$EnhancerBySpringCGLIB$$41b7cd34.getLatestVoyageConsumptionByVessel(<generated>)
	at com.example.multidatasource.crew.controller.OilVoyageConsumptionController.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionController.java:36)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.multidatasource.config.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-18 15:37:40 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 24204 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-07-18 15:37:40 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-18 15:37:40 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-07-18 15:37:41 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-18 15:37:41 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-07-18 15:37:41 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-07-18 15:37:41 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-07-18 15:37:41 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-07-18 15:37:41 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-07-18 15:37:41 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-07-18 15:37:41 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-07-18 15:37:41 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-07-18 15:37:41 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-07-18 15:37:41 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 15:37:41 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 15:37:42 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 15:37:42 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1654 ms
2025-07-18 15:37:42 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-18 15:37:42 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-07-18 15:37:42 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-07-18 15:37:42 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-07-18 15:37:42 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-07-18 15:37:42 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-07-18 15:37:42 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 420bb5d7-186d-4d6d-8dbf-93ea4c060e7e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-18 15:37:42 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2f7efd0b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@41ad373, org.springframework.security.web.context.SecurityContextPersistenceFilter@22c75c01, org.springframework.security.web.header.HeaderWriterFilter@7bac686b, org.springframework.web.filter.CorsFilter@6801b414, org.springframework.security.web.authentication.logout.LogoutFilter@6c0905f6, com.example.multidatasource.config.JwtAuthenticationFilter@12ffd1de, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5b275811, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@c017175, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@177c41d7, org.springframework.security.web.session.SessionManagementFilter@49f40c00, org.springframework.security.web.access.ExceptionTranslationFilter@37d28f02, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6aa3bfc]
2025-07-18 15:37:43 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 54321 (http) with context path '/multi/source/api'
2025-07-18 15:37:43 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 3.803 seconds (JVM running for 4.787)
2025-07-18 15:37:56 [http-nio-54321-exec-1] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 15:37:56 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 15:37:56 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-07-18 15:37:57 [http-nio-54321-exec-9] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1004 ms
2025-07-18 15:38:13 [http-nio-54321-exec-10] WARN  c.e.m.config.SecurityConfig - Unauthorized access attempt: Full authentication is required to access this resource
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 15:38:36 [http-nio-54321-exec-1] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.getUserByUsername
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@672829bc] was not registered for synchronization because synchronization is not active
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-18 15:38:36 [http-nio-54321-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 15:38:36 [http-nio-54321-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@553974183 wrapping com.mysql.cj.jdbc.ConnectionImpl@3d121ffe] will not be managed by Spring
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.a.m.U.selectUserByUsername - ==>  Preparing: SELECT * FROM t_ds_users WHERE username = ?
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.a.m.U.selectUserByUsername - ==> Parameters: admin(String)
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.a.m.U.selectUserByUsername - <==      Total: 1
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@672829bc]
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 15:38:36 [http-nio-54321-exec-1] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.validatePassword
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 15:38:36 [http-nio-54321-exec-1] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.updateLastLoginInfo
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3cd14bab] was not registered for synchronization because synchronization is not active
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@2033649171 wrapping com.mysql.cj.jdbc.ConnectionImpl@3d121ffe] will not be managed by Spring
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==>  Preparing: UPDATE t_ds_users SET last_login_time = ?, last_login_ip = ?, updated_time = NOW() WHERE id = ?
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==> Parameters: 2025-07-18T15:38:36.431(LocalDateTime), 0:0:0:0:0:0:0:1(String), 3(Long)
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.a.m.U.updateLastLoginInfo - <==    Updates: 1
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3cd14bab]
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:38:36 [http-nio-54321-exec-1] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:38:37 [http-nio-54321-exec-1] INFO  c.e.m.a.service.impl.AuthServiceImpl - User admin logged in successfully from IP: 0:0:0:0:0:0:0:1
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 15:38:53 [http-nio-54321-exec-8] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: VoyageController.getVoyageById
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 15:38:53 [http-nio-54321-exec-8] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: VoyageServiceImpl.getVoyageById
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299b518b] was not registered for synchronization because synchronization is not active
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@599993831 wrapping com.mysql.cj.jdbc.ConnectionImpl@3d121ffe] will not be managed by Spring
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG c.e.m.v.m.V.selectVoyageById - ==>  Preparing: SELECT * FROM voyage_info WHERE id = ?
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG c.e.m.v.m.V.selectVoyageById - ==> Parameters: 00228424835C44029E61DD0DE47760A3(String)
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299b518b]
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG o.s.j.support.SQLErrorCodesFactory - Looking up default SQLErrorCodes for DataSource [com.example.multidatasource.config.DynamicDataSource@329d0adf]
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG o.s.j.support.SQLErrorCodesFactory - SQL error codes for 'MySQL' found
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG o.s.j.support.SQLErrorCodesFactory - Caching SQL error codes for DataSource [com.example.multidatasource.config.DynamicDataSource@329d0adf]: database product name is 'MySQL'
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator - Translating SQLException with SQL state '42S02', error code '1146', message [Table 'hzx_master.voyage_info' doesn't exist] for task [
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'hzx_master.voyage_info' doesn't exist
### The error may exist in file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT * FROM voyage_info WHERE id = ?
### Cause: java.sql.SQLSyntaxErrorException: Table 'hzx_master.voyage_info' doesn't exist
]
2025-07-18 15:38:53 [http-nio-54321-exec-8] ERROR c.e.m.common.aspect.DataSourceAspect - Error occurred while switching data source
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'hzx_master.voyage_info' doesn't exist
### The error may exist in file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT * FROM voyage_info WHERE id = ?
### Cause: java.sql.SQLSyntaxErrorException: Table 'hzx_master.voyage_info' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'hzx_master.voyage_info' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:236)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy87.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:87)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy96.selectVoyageById(Unknown Source)
	at com.example.multidatasource.voyage.service.impl.VoyageServiceImpl.getVoyageById(VoyageServiceImpl.java:38)
	at com.example.multidatasource.voyage.service.impl.VoyageServiceImpl$$FastClassBySpringCGLIB$$acbc68f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.voyage.service.impl.VoyageServiceImpl$$EnhancerBySpringCGLIB$$9b6f2a05.getVoyageById(<generated>)
	at com.example.multidatasource.voyage.controller.VoyageController.getVoyageById(VoyageController.java:69)
	at com.example.multidatasource.voyage.controller.VoyageController$$FastClassBySpringCGLIB$$5de99d3f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.voyage.controller.VoyageController$$EnhancerBySpringCGLIB$$6e0c5c15.getVoyageById(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.multidatasource.config.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLSyntaxErrorException: Table 'hzx_master.voyage_info' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy144.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:90)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:75)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 147 common frames omitted
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: voyage
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:38:53 [http-nio-54321-exec-8] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:40:48 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 13108 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-07-18 15:40:48 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-18 15:40:48 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-07-18 15:40:49 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-18 15:40:49 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-07-18 15:40:49 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-07-18 15:40:49 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-07-18 15:40:49 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-07-18 15:40:49 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-07-18 15:40:49 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-07-18 15:40:49 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-07-18 15:40:49 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-07-18 15:40:49 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-07-18 15:40:49 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 15:40:49 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 15:40:50 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 15:40:50 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2196 ms
2025-07-18 15:40:50 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-18 15:40:50 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-07-18 15:40:50 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-07-18 15:40:50 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-07-18 15:40:50 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-07-18 15:40:50 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-07-18 15:40:50 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: d7a54d28-4d60-41fe-83d0-5eaaff8570a4

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-18 15:40:51 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6735f210, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4567fb2b, org.springframework.security.web.context.SecurityContextPersistenceFilter@64a1116a, org.springframework.security.web.header.HeaderWriterFilter@35ac9ebd, org.springframework.web.filter.CorsFilter@372954e1, org.springframework.security.web.authentication.logout.LogoutFilter@44dd0d38, com.example.multidatasource.config.JwtAuthenticationFilter@566e142, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@ac4915e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@d84b3a2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5fd8dd66, org.springframework.security.web.session.SessionManagementFilter@30159886, org.springframework.security.web.access.ExceptionTranslationFilter@76d0ecd7, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@aca3c85]
2025-07-18 15:40:51 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 54321 (http) with context path '/multi/source/api'
2025-07-18 15:40:52 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 4.654 seconds (JVM running for 5.883)
2025-07-18 15:40:56 [http-nio-54321-exec-1] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 15:40:56 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 15:40:56 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-18 15:40:57 [http-nio-54321-exec-9] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1080 ms
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 15:41:18 [http-nio-54321-exec-10] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.getUserByUsername
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@30423e74] was not registered for synchronization because synchronization is not active
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-18 15:41:18 [http-nio-54321-exec-10] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 15:41:18 [http-nio-54321-exec-10] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@896814970 wrapping com.mysql.cj.jdbc.ConnectionImpl@49b26bd0] will not be managed by Spring
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.selectUserByUsername - ==>  Preparing: SELECT * FROM t_ds_users WHERE username = ?
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.selectUserByUsername - ==> Parameters: admin(String)
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.selectUserByUsername - <==      Total: 1
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@30423e74]
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 15:41:18 [http-nio-54321-exec-10] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.validatePassword
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 15:41:18 [http-nio-54321-exec-10] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.updateLastLoginInfo
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3711841a] was not registered for synchronization because synchronization is not active
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@655052177 wrapping com.mysql.cj.jdbc.ConnectionImpl@49b26bd0] will not be managed by Spring
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==>  Preparing: UPDATE t_ds_users SET last_login_time = ?, last_login_ip = ?, updated_time = NOW() WHERE id = ?
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==> Parameters: 2025-07-18T15:41:18.711(LocalDateTime), 0:0:0:0:0:0:0:1(String), 3(Long)
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.updateLastLoginInfo - <==    Updates: 1
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3711841a]
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:41:18 [http-nio-54321-exec-10] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:41:19 [http-nio-54321-exec-10] INFO  c.e.m.a.service.impl.AuthServiceImpl - User admin logged in successfully from IP: 0:0:0:0:0:0:0:1
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:41:48 [http-nio-54321-exec-1] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: crew for method: CrewController.getCrewById
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:41:48 [http-nio-54321-exec-1] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: crew for method: CrewServiceImpl.getCrewById
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2370c590] was not registered for synchronization because synchronization is not active
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-18 15:41:48 [http-nio-54321-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-07-18 15:41:48 [http-nio-54321-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@2133024001 wrapping com.mysql.cj.jdbc.ConnectionImpl@c6ae1b0] will not be managed by Spring
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG c.e.m.c.m.CrewMapper.selectCrewById - ==>  Preparing: SELECT * FROM crew_info WHERE id = ?
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG c.e.m.c.m.CrewMapper.selectCrewById - ==> Parameters: 030ce5885b364ec7a35162cd1bc5b9e9(String)
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2370c590]
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG o.s.j.support.SQLErrorCodesFactory - Looking up default SQLErrorCodes for DataSource [com.example.multidatasource.config.DynamicDataSource@369880bf]
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG o.s.j.support.SQLErrorCodesFactory - SQL error codes for 'MySQL' found
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG o.s.j.support.SQLErrorCodesFactory - Caching SQL error codes for DataSource [com.example.multidatasource.config.DynamicDataSource@369880bf]: database product name is 'MySQL'
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator - Translating SQLException with SQL state '42S02', error code '1146', message [Table 'xingtong_prod.crew_info' doesn't exist] for task [
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'xingtong_prod.crew_info' doesn't exist
### The error may exist in file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT * FROM crew_info WHERE id = ?
### Cause: java.sql.SQLSyntaxErrorException: Table 'xingtong_prod.crew_info' doesn't exist
]
2025-07-18 15:41:48 [http-nio-54321-exec-1] ERROR c.e.m.common.aspect.DataSourceAspect - Error occurred while switching data source
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'xingtong_prod.crew_info' doesn't exist
### The error may exist in file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT * FROM crew_info WHERE id = ?
### Cause: java.sql.SQLSyntaxErrorException: Table 'xingtong_prod.crew_info' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'xingtong_prod.crew_info' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:236)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy87.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:87)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy94.selectCrewById(Unknown Source)
	at com.example.multidatasource.crew.service.impl.CrewServiceImpl.getCrewById(CrewServiceImpl.java:38)
	at com.example.multidatasource.crew.service.impl.CrewServiceImpl$$FastClassBySpringCGLIB$$868e840b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.crew.service.impl.CrewServiceImpl$$EnhancerBySpringCGLIB$$798d7d9b.getCrewById(<generated>)
	at com.example.multidatasource.crew.controller.CrewController.getCrewById(CrewController.java:72)
	at com.example.multidatasource.crew.controller.CrewController$$FastClassBySpringCGLIB$$4fbe1403.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.crew.controller.CrewController$$EnhancerBySpringCGLIB$$6a79e883.getCrewById(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.multidatasource.config.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLSyntaxErrorException: Table 'xingtong_prod.crew_info' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy144.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:90)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:75)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 147 common frames omitted
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:41:48 [http-nio-54321-exec-1] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:46:35 [http-nio-54321-exec-8] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-18 15:46:35 [http-nio-54321-exec-8] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 15:46:35 [http-nio-54321-exec-8] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:46:35 [http-nio-54321-exec-8] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: crew for method: OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel
2025-07-18 15:46:35 [http-nio-54321-exec-8] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取每个船舶最新的航次油耗记录（包含轻油和重油明细）- vesselId: null, vesselName: null, startDate: null, endDate: null
2025-07-18 15:46:35 [http-nio-54321-exec-8] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 15:46:35 [http-nio-54321-exec-8] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@85f7aa6] was not registered for synchronization because synchronization is not active
2025-07-18 15:46:35 [http-nio-54321-exec-8] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:46:35 [http-nio-54321-exec-8] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-18 15:46:35 [http-nio-54321-exec-8] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1016743071 wrapping com.mysql.cj.jdbc.ConnectionImpl@c6ae1b0] will not be managed by Spring
2025-07-18 15:46:35 [http-nio-54321-exec-8] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==>  Preparing: SELECT latest.vessel_id, latest.vessel_name, latest.fill_date, latest.consum_id, detail.oil_id, detail.oil_name_cn, detail.oil_name_en, detail.oil_mark, detail.oil_type_id, detail.fuel_type_id, detail.oil_unit_id, detail.oil_unit_name, detail.last_voyage_inventory FROM ( SELECT t1.vessel_id, t1.vessel_name, t1.fill_date, t1.consum_id FROM oil_voyage_consumption_info t1 INNER JOIN ( SELECT vessel_id, MAX(fill_date) AS max_fill_date FROM oil_voyage_consumption_info WHERE delete_flag = '0' AND status_flag != '99' GROUP BY vessel_id ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date WHERE t1.delete_flag = '0' AND t1.status_flag != '99' ) latest INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id WHERE detail.delete_flag = '0' AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油') ORDER BY latest.vessel_id, detail.oil_name_cn
2025-07-18 15:46:35 [http-nio-54321-exec-8] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==> Parameters: 
2025-07-18 15:46:36 [http-nio-54321-exec-8] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - <==      Total: 63
2025-07-18 15:46:36 [http-nio-54321-exec-8] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@85f7aa6]
2025-07-18 15:46:36 [http-nio-54321-exec-8] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 成功获取29条船舶最新航次油耗记录
2025-07-18 15:46:36 [http-nio-54321-exec-8] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:46:36 [http-nio-54321-exec-8] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:52:44 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 19836 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-07-18 15:52:44 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-18 15:52:44 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-07-18 15:52:45 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-18 15:52:45 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-07-18 15:52:45 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-07-18 15:52:45 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-07-18 15:52:45 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-07-18 15:52:45 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-07-18 15:52:45 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-07-18 15:52:45 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-07-18 15:52:45 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-07-18 15:52:46 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-07-18 15:52:46 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 15:52:46 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 15:52:46 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 15:52:46 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2234 ms
2025-07-18 15:52:47 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-18 15:52:47 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-07-18 15:52:47 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-07-18 15:52:47 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-07-18 15:52:47 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-07-18 15:52:47 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-07-18 15:52:47 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 897b080b-d96a-43b0-98e4-77a221198e3f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-18 15:52:47 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@26874f2c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5f3b84bd, org.springframework.security.web.context.SecurityContextPersistenceFilter@421ead7e, org.springframework.security.web.header.HeaderWriterFilter@3dc39459, org.springframework.web.filter.CorsFilter@1e734eee, org.springframework.security.web.authentication.logout.LogoutFilter@3a38e4dc, com.example.multidatasource.config.JwtAuthenticationFilter@5d318e91, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6826b70f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3a4e524, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1e191150, org.springframework.security.web.session.SessionManagementFilter@57c69937, org.springframework.security.web.access.ExceptionTranslationFilter@179ee36b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@13ca16bf]
2025-07-18 15:52:48 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 54321 (http) with context path '/multi/source/api'
2025-07-18 15:52:48 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 5.029 seconds (JVM running for 6.207)
2025-07-18 15:53:11 [http-nio-54321-exec-1] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 15:53:11 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 15:53:11 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-18 15:53:13 [http-nio-54321-exec-8] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 959 ms
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 15:53:39 [http-nio-54321-exec-9] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.getUserByUsername
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@33294cac] was not registered for synchronization because synchronization is not active
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-18 15:53:39 [http-nio-54321-exec-9] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 15:53:39 [http-nio-54321-exec-9] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@226327003 wrapping com.mysql.cj.jdbc.ConnectionImpl@627251c1] will not be managed by Spring
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.a.m.U.selectUserByUsername - ==>  Preparing: SELECT * FROM t_ds_users WHERE username = ?
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.a.m.U.selectUserByUsername - ==> Parameters: admin(String)
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.a.m.U.selectUserByUsername - <==      Total: 1
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@33294cac]
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 15:53:39 [http-nio-54321-exec-9] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.validatePassword
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 15:53:39 [http-nio-54321-exec-9] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.updateLastLoginInfo
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3e04153] was not registered for synchronization because synchronization is not active
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@2109844537 wrapping com.mysql.cj.jdbc.ConnectionImpl@627251c1] will not be managed by Spring
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==>  Preparing: UPDATE t_ds_users SET last_login_time = ?, last_login_ip = ?, updated_time = NOW() WHERE id = ?
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==> Parameters: 2025-07-18T15:53:39.818(LocalDateTime), 0:0:0:0:0:0:0:1(String), 3(Long)
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.a.m.U.updateLastLoginInfo - <==    Updates: 1
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3e04153]
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:53:39 [http-nio-54321-exec-9] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:53:40 [http-nio-54321-exec-9] INFO  c.e.m.a.service.impl.AuthServiceImpl - User admin logged in successfully from IP: 0:0:0:0:0:0:0:1
2025-07-18 15:54:05 [http-nio-54321-exec-10] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-18 15:54:05 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 15:54:05 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:54:05 [http-nio-54321-exec-10] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: crew for method: OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel
2025-07-18 15:54:05 [http-nio-54321-exec-10] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取每个船舶最新的航次油耗记录（包含轻油和重油明细）- vesselId: null, vesselName: null, startDate: null, endDate: null
2025-07-18 15:54:05 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 15:54:05 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@67d96d42] was not registered for synchronization because synchronization is not active
2025-07-18 15:54:05 [http-nio-54321-exec-10] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:54:05 [http-nio-54321-exec-10] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-18 15:54:05 [http-nio-54321-exec-10] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-07-18 15:54:05 [http-nio-54321-exec-10] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-07-18 15:54:05 [http-nio-54321-exec-10] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1052887742 wrapping com.mysql.cj.jdbc.ConnectionImpl@5c956fbd] will not be managed by Spring
2025-07-18 15:54:05 [http-nio-54321-exec-10] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==>  Preparing: SELECT latest.vessel_id, latest.vessel_name, latest.fill_date, latest.consum_id, detail.oil_id, detail.oil_name_cn, detail.oil_name_en, detail.oil_mark, detail.oil_type_id, detail.fuel_type_id, detail.oil_unit_id, detail.oil_unit_name, detail.this_voyage_inventory FROM ( SELECT t1.vessel_id, t1.vessel_name, t1.fill_date, t1.consum_id FROM oil_voyage_consumption_info t1 INNER JOIN ( SELECT vessel_id, MAX(fill_date) AS max_fill_date FROM oil_voyage_consumption_info WHERE delete_flag = '0' AND status_flag != '99' GROUP BY vessel_id ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date WHERE t1.delete_flag = '0' AND t1.status_flag != '99' ) latest INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id WHERE detail.delete_flag = '0' AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油') ORDER BY latest.vessel_id, detail.oil_name_cn
2025-07-18 15:54:05 [http-nio-54321-exec-10] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==> Parameters: 
2025-07-18 15:54:05 [http-nio-54321-exec-10] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - <==      Total: 63
2025-07-18 15:54:05 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@67d96d42]
2025-07-18 15:54:05 [http-nio-54321-exec-10] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 成功获取29条船舶最新航次油耗记录
2025-07-18 15:54:05 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:54:05 [http-nio-54321-exec-10] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:55:05 [http-nio-54321-exec-5] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-18 15:55:05 [http-nio-54321-exec-5] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 15:55:05 [http-nio-54321-exec-5] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:55:05 [http-nio-54321-exec-5] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: crew for method: OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel
2025-07-18 15:55:05 [http-nio-54321-exec-5] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取每个船舶最新的航次油耗记录（包含轻油和重油明细）- vesselId: null, vesselName: 兴通56, startDate: null, endDate: null
2025-07-18 15:55:05 [http-nio-54321-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 15:55:05 [http-nio-54321-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5d17832e] was not registered for synchronization because synchronization is not active
2025-07-18 15:55:05 [http-nio-54321-exec-5] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:55:05 [http-nio-54321-exec-5] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-18 15:55:05 [http-nio-54321-exec-5] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@129115069 wrapping com.mysql.cj.jdbc.ConnectionImpl@5c956fbd] will not be managed by Spring
2025-07-18 15:55:05 [http-nio-54321-exec-5] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==>  Preparing: SELECT latest.vessel_id, latest.vessel_name, latest.fill_date, latest.consum_id, detail.oil_id, detail.oil_name_cn, detail.oil_name_en, detail.oil_mark, detail.oil_type_id, detail.fuel_type_id, detail.oil_unit_id, detail.oil_unit_name, detail.this_voyage_inventory FROM ( SELECT t1.vessel_id, t1.vessel_name, t1.fill_date, t1.consum_id FROM oil_voyage_consumption_info t1 INNER JOIN ( SELECT vessel_id, MAX(fill_date) AS max_fill_date FROM oil_voyage_consumption_info WHERE delete_flag = '0' AND status_flag != '99' AND vessel_name LIKE CONCAT('%', ?, '%') GROUP BY vessel_id ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date WHERE t1.delete_flag = '0' AND t1.status_flag != '99' AND t1.vessel_name LIKE CONCAT('%', ?, '%') ) latest INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id WHERE detail.delete_flag = '0' AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油') ORDER BY latest.vessel_id, detail.oil_name_cn
2025-07-18 15:55:05 [http-nio-54321-exec-5] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==> Parameters: 兴通56(String), 兴通56(String)
2025-07-18 15:55:05 [http-nio-54321-exec-5] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - <==      Total: 2
2025-07-18 15:55:05 [http-nio-54321-exec-5] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5d17832e]
2025-07-18 15:55:05 [http-nio-54321-exec-5] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 成功获取1条船舶最新航次油耗记录
2025-07-18 15:55:05 [http-nio-54321-exec-5] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:55:05 [http-nio-54321-exec-5] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 15:56:59 [http-nio-54321-exec-7] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-18 15:56:59 [http-nio-54321-exec-7] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 15:56:59 [http-nio-54321-exec-7] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:56:59 [http-nio-54321-exec-7] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: crew for method: OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel
2025-07-18 15:56:59 [http-nio-54321-exec-7] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取每个船舶最新的航次油耗记录（包含轻油和重油明细）- vesselId: null, vesselName: null, startDate: null, endDate: null
2025-07-18 15:56:59 [http-nio-54321-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 15:56:59 [http-nio-54321-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3144d398] was not registered for synchronization because synchronization is not active
2025-07-18 15:56:59 [http-nio-54321-exec-7] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 15:56:59 [http-nio-54321-exec-7] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-18 15:56:59 [http-nio-54321-exec-7] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1059097622 wrapping com.mysql.cj.jdbc.ConnectionImpl@5c956fbd] will not be managed by Spring
2025-07-18 15:56:59 [http-nio-54321-exec-7] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==>  Preparing: SELECT latest.vessel_id, latest.vessel_name, latest.fill_date, latest.consum_id, detail.oil_id, detail.oil_name_cn, detail.oil_name_en, detail.oil_mark, detail.oil_type_id, detail.fuel_type_id, detail.oil_unit_id, detail.oil_unit_name, detail.this_voyage_inventory FROM ( SELECT t1.vessel_id, t1.vessel_name, t1.fill_date, t1.consum_id FROM oil_voyage_consumption_info t1 INNER JOIN ( SELECT vessel_id, MAX(fill_date) AS max_fill_date FROM oil_voyage_consumption_info WHERE delete_flag = '0' AND status_flag != '99' GROUP BY vessel_id ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date WHERE t1.delete_flag = '0' AND t1.status_flag != '99' ) latest INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id WHERE detail.delete_flag = '0' AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油') ORDER BY latest.vessel_id, detail.oil_name_cn
2025-07-18 15:56:59 [http-nio-54321-exec-7] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==> Parameters: 
2025-07-18 15:57:00 [http-nio-54321-exec-7] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - <==      Total: 63
2025-07-18 15:57:00 [http-nio-54321-exec-7] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3144d398]
2025-07-18 15:57:00 [http-nio-54321-exec-7] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 成功获取29条船舶最新航次油耗记录
2025-07-18 15:57:00 [http-nio-54321-exec-7] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 15:57:00 [http-nio-54321-exec-7] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 16:10:42 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 17200 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-07-18 16:10:42 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-18 16:10:42 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-07-18 16:10:43 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-18 16:10:43 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-07-18 16:10:43 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-07-18 16:10:43 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-07-18 16:10:43 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-07-18 16:10:43 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-07-18 16:10:43 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-07-18 16:10:43 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-07-18 16:10:43 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-07-18 16:10:43 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-07-18 16:10:43 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 16:10:43 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 16:10:43 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 16:10:43 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1762 ms
2025-07-18 16:10:44 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-18 16:10:44 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-07-18 16:10:44 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-07-18 16:10:44 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-07-18 16:10:44 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-07-18 16:10:44 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-07-18 16:10:44 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: c275d53c-2ad2-4d2e-8de3-a0e9e34b3143

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-18 16:10:44 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@78a515e4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@d2291de, org.springframework.security.web.context.SecurityContextPersistenceFilter@22c75c01, org.springframework.security.web.header.HeaderWriterFilter@6af91cc8, org.springframework.web.filter.CorsFilter@51c8f62c, org.springframework.security.web.authentication.logout.LogoutFilter@1f67761b, com.example.multidatasource.config.JwtAuthenticationFilter@1e530163, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5b275811, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@c017175, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@41a374be, org.springframework.security.web.session.SessionManagementFilter@58b91d57, org.springframework.security.web.access.ExceptionTranslationFilter@696b4a95, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2b5c4f17]
2025-07-18 16:10:45 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 54321 (http) with context path '/multi/source/api'
2025-07-18 16:10:45 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 3.892 seconds (JVM running for 4.935)
2025-07-18 16:10:53 [http-nio-54321-exec-2] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 16:10:53 [http-nio-54321-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 16:10:53 [http-nio-54321-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-18 16:10:53 [http-nio-54321-exec-2] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-18 16:10:53 [http-nio-54321-exec-2] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 16:10:53 [http-nio-54321-exec-2] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 16:10:53 [http-nio-54321-exec-2] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.getUserByUsername
2025-07-18 16:10:53 [http-nio-54321-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 16:10:53 [http-nio-54321-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@592f83e5] was not registered for synchronization because synchronization is not active
2025-07-18 16:10:53 [http-nio-54321-exec-2] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 16:10:53 [http-nio-54321-exec-2] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-18 16:10:53 [http-nio-54321-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 16:10:54 [http-nio-54321-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@2078633056 wrapping com.mysql.cj.jdbc.ConnectionImpl@64b57a54] will not be managed by Spring
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG c.e.m.a.m.U.selectUserByUsername - ==>  Preparing: SELECT * FROM t_ds_users WHERE username = ?
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG c.e.m.a.m.U.selectUserByUsername - ==> Parameters: admin(String)
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG c.e.m.a.m.U.selectUserByUsername - <==      Total: 1
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@592f83e5]
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 16:10:54 [http-nio-54321-exec-2] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.validatePassword
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 16:10:54 [http-nio-54321-exec-2] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.updateLastLoginInfo
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@68e77a6f] was not registered for synchronization because synchronization is not active
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@745222771 wrapping com.mysql.cj.jdbc.ConnectionImpl@64b57a54] will not be managed by Spring
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==>  Preparing: UPDATE t_ds_users SET last_login_time = ?, last_login_ip = ?, updated_time = NOW() WHERE id = ?
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==> Parameters: 2025-07-18T16:10:54.217(LocalDateTime), 0:0:0:0:0:0:0:1(String), 3(Long)
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG c.e.m.a.m.U.updateLastLoginInfo - <==    Updates: 1
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@68e77a6f]
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 16:10:54 [http-nio-54321-exec-2] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 16:10:54 [http-nio-54321-exec-2] INFO  c.e.m.a.service.impl.AuthServiceImpl - User admin logged in successfully from IP: 0:0:0:0:0:0:0:1
2025-07-18 16:11:09 [http-nio-54321-exec-10] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 805 ms
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 16:11:29 [http-nio-54321-exec-1] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: crew for method: OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel
2025-07-18 16:11:29 [http-nio-54321-exec-1] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取每个船舶最新的航次油耗记录（包含轻油和重油明细）- vesselId: null, vesselName: null, startDate: null, endDate: null
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@626837b7] was not registered for synchronization because synchronization is not active
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-18 16:11:29 [http-nio-54321-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-07-18 16:11:29 [http-nio-54321-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@431928812 wrapping com.mysql.cj.jdbc.ConnectionImpl@aff4301] will not be managed by Spring
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==>  Preparing: SELECT latest.vessel_id, latest.vessel_name, latest.fill_date, latest.consum_id, detail.oil_id, detail.oil_name_cn, detail.oil_name_en, detail.oil_mark, detail.oil_type_id, detail.fuel_type_id, detail.oil_unit_id, detail.oil_unit_name, detail.this_voyage_inventory FROM ( SELECT t1.vessel_id, t1.vessel_name, t1.fill_date, t1.consum_id, sh.mmsi_code FROM oil_voyage_consumption_info t1 INNER JOIN common_vessel sh ON t1.vessel_id = sh.vessel_id INNER JOIN ( SELECT vessel_id, MAX(fill_date) AS max_fill_date FROM oil_voyage_consumption_info ci INNER JOIN common_vessel vs ON vs.vessel_id = ci.vessel_id WHERE delete_flag = '0' AND status_flag != '99' GROUP BY vessel_id ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date WHERE t1.delete_flag = '0' AND t1.status_flag != '99' ) latest INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id WHERE detail.delete_flag = '0' AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油') ORDER BY latest.vessel_id, detail.oil_name_cn
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==> Parameters: 
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@626837b7]
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG o.s.j.support.SQLErrorCodesFactory - Looking up default SQLErrorCodes for DataSource [com.example.multidatasource.config.DynamicDataSource@6b0f9c8e]
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG o.s.j.support.SQLErrorCodesFactory - SQL error codes for 'MySQL' found
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG o.s.j.support.SQLErrorCodesFactory - Caching SQL error codes for DataSource [com.example.multidatasource.config.DynamicDataSource@6b0f9c8e]: database product name is 'MySQL'
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator - Unable to translate SQLException with Error code '1052', will now try the fallback translator
2025-07-18 16:11:29 [http-nio-54321-exec-1] ERROR c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取船舶最新航次油耗记录失败
org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'vessel_id' in field list is ambiguous
### The error may exist in file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT             latest.vessel_id,             latest.vessel_name,             latest.fill_date,             latest.consum_id,             detail.oil_id,             detail.oil_name_cn,             detail.oil_name_en,             detail.oil_mark,             detail.oil_type_id,             detail.fuel_type_id,             detail.oil_unit_id,             detail.oil_unit_name,             detail.this_voyage_inventory         FROM (             SELECT                 t1.vessel_id,                 t1.vessel_name,                 t1.fill_date,                 t1.consum_id,                 sh.mmsi_code             FROM oil_voyage_consumption_info t1             INNER JOIN common_vessel sh ON t1.vessel_id = sh.vessel_id             INNER JOIN (                 SELECT                     vessel_id,                     MAX(fill_date) AS max_fill_date                 FROM oil_voyage_consumption_info ci                 INNER JOIN common_vessel vs ON vs.vessel_id = ci.vessel_id                 WHERE delete_flag = '0' AND status_flag != '99'                                                                                                           GROUP BY vessel_id             ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date             WHERE t1.delete_flag = '0' AND t1.status_flag != '99'                                                                               ) latest         INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id         WHERE detail.delete_flag = '0'         AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油')         ORDER BY latest.vessel_id, detail.oil_name_cn
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'vessel_id' in field list is ambiguous
; Column 'vessel_id' in field list is ambiguous; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'vessel_id' in field list is ambiguous
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:87)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy87.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy95.getLatestVoyageConsumptionByVessel(Unknown Source)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionServiceImpl.java:31)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$FastClassBySpringCGLIB$$4f22a582.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$EnhancerBySpringCGLIB$$f222ef22.getLatestVoyageConsumptionByVessel(<generated>)
	at com.example.multidatasource.crew.controller.OilVoyageConsumptionController.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionController.java:37)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.multidatasource.config.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'vessel_id' in field list is ambiguous
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy122.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:90)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 127 common frames omitted
2025-07-18 16:11:29 [http-nio-54321-exec-1] ERROR c.e.m.common.aspect.DataSourceAspect - Error occurred while switching data source
java.lang.RuntimeException: 获取船舶最新航次油耗记录失败: 
### Error querying database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'vessel_id' in field list is ambiguous
### The error may exist in file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT             latest.vessel_id,             latest.vessel_name,             latest.fill_date,             latest.consum_id,             detail.oil_id,             detail.oil_name_cn,             detail.oil_name_en,             detail.oil_mark,             detail.oil_type_id,             detail.fuel_type_id,             detail.oil_unit_id,             detail.oil_unit_name,             detail.this_voyage_inventory         FROM (             SELECT                 t1.vessel_id,                 t1.vessel_name,                 t1.fill_date,                 t1.consum_id,                 sh.mmsi_code             FROM oil_voyage_consumption_info t1             INNER JOIN common_vessel sh ON t1.vessel_id = sh.vessel_id             INNER JOIN (                 SELECT                     vessel_id,                     MAX(fill_date) AS max_fill_date                 FROM oil_voyage_consumption_info ci                 INNER JOIN common_vessel vs ON vs.vessel_id = ci.vessel_id                 WHERE delete_flag = '0' AND status_flag != '99'                                                                                                           GROUP BY vessel_id             ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date             WHERE t1.delete_flag = '0' AND t1.status_flag != '99'                                                                               ) latest         INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id         WHERE detail.delete_flag = '0'         AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油')         ORDER BY latest.vessel_id, detail.oil_name_cn
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'vessel_id' in field list is ambiguous
; Column 'vessel_id' in field list is ambiguous; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'vessel_id' in field list is ambiguous
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionServiceImpl.java:37)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$FastClassBySpringCGLIB$$4f22a582.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$EnhancerBySpringCGLIB$$f222ef22.getLatestVoyageConsumptionByVessel(<generated>)
	at com.example.multidatasource.crew.controller.OilVoyageConsumptionController.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionController.java:37)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.multidatasource.config.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 16:11:29 [http-nio-54321-exec-1] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 16:11:29 [http-nio-54321-exec-1] ERROR c.e.m.c.c.OilVoyageConsumptionController - 获取船舶最新航次油耗记录失败
java.lang.RuntimeException: 获取船舶最新航次油耗记录失败: 
### Error querying database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'vessel_id' in field list is ambiguous
### The error may exist in file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT             latest.vessel_id,             latest.vessel_name,             latest.fill_date,             latest.consum_id,             detail.oil_id,             detail.oil_name_cn,             detail.oil_name_en,             detail.oil_mark,             detail.oil_type_id,             detail.fuel_type_id,             detail.oil_unit_id,             detail.oil_unit_name,             detail.this_voyage_inventory         FROM (             SELECT                 t1.vessel_id,                 t1.vessel_name,                 t1.fill_date,                 t1.consum_id,                 sh.mmsi_code             FROM oil_voyage_consumption_info t1             INNER JOIN common_vessel sh ON t1.vessel_id = sh.vessel_id             INNER JOIN (                 SELECT                     vessel_id,                     MAX(fill_date) AS max_fill_date                 FROM oil_voyage_consumption_info ci                 INNER JOIN common_vessel vs ON vs.vessel_id = ci.vessel_id                 WHERE delete_flag = '0' AND status_flag != '99'                                                                                                           GROUP BY vessel_id             ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date             WHERE t1.delete_flag = '0' AND t1.status_flag != '99'                                                                               ) latest         INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id         WHERE detail.delete_flag = '0'         AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油')         ORDER BY latest.vessel_id, detail.oil_name_cn
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'vessel_id' in field list is ambiguous
; Column 'vessel_id' in field list is ambiguous; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'vessel_id' in field list is ambiguous
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionServiceImpl.java:37)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$FastClassBySpringCGLIB$$4f22a582.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$EnhancerBySpringCGLIB$$f222ef22.getLatestVoyageConsumptionByVessel(<generated>)
	at com.example.multidatasource.crew.controller.OilVoyageConsumptionController.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionController.java:37)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.multidatasource.config.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-18 16:16:52 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 15300 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-07-18 16:16:52 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-18 16:16:52 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-07-18 16:16:53 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-18 16:16:53 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-07-18 16:16:53 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-07-18 16:16:53 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-07-18 16:16:53 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-07-18 16:16:53 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-07-18 16:16:53 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-07-18 16:16:53 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-07-18 16:16:53 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-07-18 16:16:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-07-18 16:16:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 16:16:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 16:16:54 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 16:16:54 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1681 ms
2025-07-18 16:16:54 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-18 16:16:54 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-07-18 16:16:54 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-07-18 16:16:54 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-07-18 16:16:54 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-07-18 16:16:54 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-07-18 16:16:55 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 8493c970-0a84-4157-bceb-433ba697f1c7

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-18 16:16:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@78a515e4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@d2291de, org.springframework.security.web.context.SecurityContextPersistenceFilter@22c75c01, org.springframework.security.web.header.HeaderWriterFilter@6af91cc8, org.springframework.web.filter.CorsFilter@51c8f62c, org.springframework.security.web.authentication.logout.LogoutFilter@1f67761b, com.example.multidatasource.config.JwtAuthenticationFilter@1e530163, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5b275811, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@c017175, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@41a374be, org.springframework.security.web.session.SessionManagementFilter@58b91d57, org.springframework.security.web.access.ExceptionTranslationFilter@696b4a95, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2b5c4f17]
2025-07-18 16:16:56 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 54321 (http) with context path '/multi/source/api'
2025-07-18 16:16:56 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 3.782 seconds (JVM running for 4.85)
2025-07-18 16:17:00 [http-nio-54321-exec-1] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 16:17:00 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 16:17:00 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-18 16:17:01 [http-nio-54321-exec-7] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 869 ms
2025-07-18 16:17:24 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 16:17:24 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 16:17:24 [http-nio-54321-exec-10] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.getUserByUsername
2025-07-18 16:17:24 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 16:17:24 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@69e7bd34] was not registered for synchronization because synchronization is not active
2025-07-18 16:17:24 [http-nio-54321-exec-10] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 16:17:24 [http-nio-54321-exec-10] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-18 16:17:24 [http-nio-54321-exec-10] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 16:17:24 [http-nio-54321-exec-10] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 16:17:24 [http-nio-54321-exec-10] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1661066591 wrapping com.mysql.cj.jdbc.ConnectionImpl@7e6f4ab] will not be managed by Spring
2025-07-18 16:17:24 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.selectUserByUsername - ==>  Preparing: SELECT * FROM t_ds_users WHERE username = ?
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.selectUserByUsername - ==> Parameters: admin(String)
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.selectUserByUsername - <==      Total: 1
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@69e7bd34]
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 16:17:25 [http-nio-54321-exec-10] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.validatePassword
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: voyage
2025-07-18 16:17:25 [http-nio-54321-exec-10] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: voyage for method: UserServiceImpl.updateLastLoginInfo
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@bc49161] was not registered for synchronization because synchronization is not active
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG c.e.m.config.DynamicDataSource - Current data source: voyage
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@851965015 wrapping com.mysql.cj.jdbc.ConnectionImpl@7e6f4ab] will not be managed by Spring
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==>  Preparing: UPDATE t_ds_users SET last_login_time = ?, last_login_ip = ?, updated_time = NOW() WHERE id = ?
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.updateLastLoginInfo - ==> Parameters: 2025-07-18T16:17:25.161(LocalDateTime), 0:0:0:0:0:0:0:1(String), 3(Long)
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG c.e.m.a.m.U.updateLastLoginInfo - <==    Updates: 1
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@bc49161]
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 16:17:25 [http-nio-54321-exec-10] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 16:17:25 [http-nio-54321-exec-10] INFO  c.e.m.a.service.impl.AuthServiceImpl - User admin logged in successfully from IP: 0:0:0:0:0:0:0:1
2025-07-18 16:17:49 [http-nio-54321-exec-1] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-18 16:17:49 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 16:17:49 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 16:17:49 [http-nio-54321-exec-1] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: crew for method: OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel
2025-07-18 16:17:49 [http-nio-54321-exec-1] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取每个船舶最新的航次油耗记录（包含轻油和重油明细）- vesselId: null, vesselName: null, startDate: null, endDate: null
2025-07-18 16:17:49 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 16:17:49 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a052f] was not registered for synchronization because synchronization is not active
2025-07-18 16:17:49 [http-nio-54321-exec-1] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 16:17:49 [http-nio-54321-exec-1] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-18 16:17:49 [http-nio-54321-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-07-18 16:17:50 [http-nio-54321-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-07-18 16:17:50 [http-nio-54321-exec-1] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@768656585 wrapping com.mysql.cj.jdbc.ConnectionImpl@4df89442] will not be managed by Spring
2025-07-18 16:17:50 [http-nio-54321-exec-1] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==>  Preparing: SELECT latest.vessel_id, latest.vessel_name, latest.fill_date, latest.consum_id, detail.oil_id, detail.oil_name_cn, detail.oil_name_en, detail.oil_mark, detail.oil_type_id, detail.fuel_type_id, detail.oil_unit_id, detail.oil_unit_name, detail.this_voyage_inventory FROM ( SELECT t1.vessel_id, t1.vessel_name, t1.fill_date, t1.consum_id, sh.mmsi_code FROM oil_voyage_consumption_info t1 INNER JOIN common_vessel sh ON t1.vessel_id = sh.vessel_id INNER JOIN ( SELECT ci.vessel_id, MAX(ci.fill_date) AS max_fill_date FROM oil_voyage_consumption_info ci INNER JOIN common_vessel vs ON vs.vessel_id = ci.vessel_id WHERE delete_flag = '0' AND status_flag != '99' GROUP BY ci.vessel_id ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date WHERE t1.delete_flag = '0' AND t1.status_flag != '99' ) latest INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id WHERE detail.delete_flag = '0' AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油') ORDER BY latest.vessel_id, detail.oil_name_cn
2025-07-18 16:17:50 [http-nio-54321-exec-1] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==> Parameters: 
2025-07-18 16:17:50 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a052f]
2025-07-18 16:17:50 [http-nio-54321-exec-1] DEBUG o.s.j.support.SQLErrorCodesFactory - Looking up default SQLErrorCodes for DataSource [com.example.multidatasource.config.DynamicDataSource@3fae82f1]
2025-07-18 16:17:50 [http-nio-54321-exec-1] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 16:17:50 [http-nio-54321-exec-1] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-18 16:17:50 [http-nio-54321-exec-1] DEBUG o.s.j.support.SQLErrorCodesFactory - SQL error codes for 'MySQL' found
2025-07-18 16:17:50 [http-nio-54321-exec-1] DEBUG o.s.j.support.SQLErrorCodesFactory - Caching SQL error codes for DataSource [com.example.multidatasource.config.DynamicDataSource@3fae82f1]: database product name is 'MySQL'
2025-07-18 16:17:50 [http-nio-54321-exec-1] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator - Unable to translate SQLException with Error code '1052', will now try the fallback translator
2025-07-18 16:17:50 [http-nio-54321-exec-1] ERROR c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取船舶最新航次油耗记录失败
org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'delete_flag' in where clause is ambiguous
### The error may exist in file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT             latest.vessel_id,             latest.vessel_name,             latest.fill_date,             latest.consum_id,             detail.oil_id,             detail.oil_name_cn,             detail.oil_name_en,             detail.oil_mark,             detail.oil_type_id,             detail.fuel_type_id,             detail.oil_unit_id,             detail.oil_unit_name,             detail.this_voyage_inventory         FROM (             SELECT                 t1.vessel_id,                 t1.vessel_name,                 t1.fill_date,                 t1.consum_id,                 sh.mmsi_code             FROM oil_voyage_consumption_info t1             INNER JOIN common_vessel sh ON t1.vessel_id = sh.vessel_id             INNER JOIN (                 SELECT                     ci.vessel_id,                     MAX(ci.fill_date) AS max_fill_date                 FROM oil_voyage_consumption_info ci                 INNER JOIN common_vessel vs ON vs.vessel_id = ci.vessel_id                 WHERE delete_flag = '0' AND status_flag != '99'                                                                                                           GROUP BY ci.vessel_id             ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date             WHERE t1.delete_flag = '0' AND t1.status_flag != '99'                                                                               ) latest         INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id         WHERE detail.delete_flag = '0'         AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油')         ORDER BY latest.vessel_id, detail.oil_name_cn
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'delete_flag' in where clause is ambiguous
; Column 'delete_flag' in where clause is ambiguous; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'delete_flag' in where clause is ambiguous
	at org.springframework.jdbc.support.SQLExceptionSubclassTranslator.doTranslate(SQLExceptionSubclassTranslator.java:87)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy87.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:147)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:80)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy95.getLatestVoyageConsumptionByVessel(Unknown Source)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionServiceImpl.java:31)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$FastClassBySpringCGLIB$$4f22a582.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$EnhancerBySpringCGLIB$$f222ef22.getLatestVoyageConsumptionByVessel(<generated>)
	at com.example.multidatasource.crew.controller.OilVoyageConsumptionController.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionController.java:37)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.multidatasource.config.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'delete_flag' in where clause is ambiguous
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at com.sun.proxy.$Proxy144.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:90)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 127 common frames omitted
2025-07-18 16:17:50 [http-nio-54321-exec-1] ERROR c.e.m.common.aspect.DataSourceAspect - Error occurred while switching data source
java.lang.RuntimeException: 获取船舶最新航次油耗记录失败: 
### Error querying database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'delete_flag' in where clause is ambiguous
### The error may exist in file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT             latest.vessel_id,             latest.vessel_name,             latest.fill_date,             latest.consum_id,             detail.oil_id,             detail.oil_name_cn,             detail.oil_name_en,             detail.oil_mark,             detail.oil_type_id,             detail.fuel_type_id,             detail.oil_unit_id,             detail.oil_unit_name,             detail.this_voyage_inventory         FROM (             SELECT                 t1.vessel_id,                 t1.vessel_name,                 t1.fill_date,                 t1.consum_id,                 sh.mmsi_code             FROM oil_voyage_consumption_info t1             INNER JOIN common_vessel sh ON t1.vessel_id = sh.vessel_id             INNER JOIN (                 SELECT                     ci.vessel_id,                     MAX(ci.fill_date) AS max_fill_date                 FROM oil_voyage_consumption_info ci                 INNER JOIN common_vessel vs ON vs.vessel_id = ci.vessel_id                 WHERE delete_flag = '0' AND status_flag != '99'                                                                                                           GROUP BY ci.vessel_id             ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date             WHERE t1.delete_flag = '0' AND t1.status_flag != '99'                                                                               ) latest         INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id         WHERE detail.delete_flag = '0'         AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油')         ORDER BY latest.vessel_id, detail.oil_name_cn
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'delete_flag' in where clause is ambiguous
; Column 'delete_flag' in where clause is ambiguous; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'delete_flag' in where clause is ambiguous
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionServiceImpl.java:37)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$FastClassBySpringCGLIB$$4f22a582.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$EnhancerBySpringCGLIB$$f222ef22.getLatestVoyageConsumptionByVessel(<generated>)
	at com.example.multidatasource.crew.controller.OilVoyageConsumptionController.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionController.java:37)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.multidatasource.config.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-18 16:17:50 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 16:17:50 [http-nio-54321-exec-1] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 16:17:50 [http-nio-54321-exec-1] ERROR c.e.m.c.c.OilVoyageConsumptionController - 获取船舶最新航次油耗记录失败
java.lang.RuntimeException: 获取船舶最新航次油耗记录失败: 
### Error querying database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'delete_flag' in where clause is ambiguous
### The error may exist in file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT             latest.vessel_id,             latest.vessel_name,             latest.fill_date,             latest.consum_id,             detail.oil_id,             detail.oil_name_cn,             detail.oil_name_en,             detail.oil_mark,             detail.oil_type_id,             detail.fuel_type_id,             detail.oil_unit_id,             detail.oil_unit_name,             detail.this_voyage_inventory         FROM (             SELECT                 t1.vessel_id,                 t1.vessel_name,                 t1.fill_date,                 t1.consum_id,                 sh.mmsi_code             FROM oil_voyage_consumption_info t1             INNER JOIN common_vessel sh ON t1.vessel_id = sh.vessel_id             INNER JOIN (                 SELECT                     ci.vessel_id,                     MAX(ci.fill_date) AS max_fill_date                 FROM oil_voyage_consumption_info ci                 INNER JOIN common_vessel vs ON vs.vessel_id = ci.vessel_id                 WHERE delete_flag = '0' AND status_flag != '99'                                                                                                           GROUP BY ci.vessel_id             ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date             WHERE t1.delete_flag = '0' AND t1.status_flag != '99'                                                                               ) latest         INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id         WHERE detail.delete_flag = '0'         AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油')         ORDER BY latest.vessel_id, detail.oil_name_cn
### Cause: java.sql.SQLIntegrityConstraintViolationException: Column 'delete_flag' in where clause is ambiguous
; Column 'delete_flag' in where clause is ambiguous; nested exception is java.sql.SQLIntegrityConstraintViolationException: Column 'delete_flag' in where clause is ambiguous
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionServiceImpl.java:37)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$FastClassBySpringCGLIB$$4f22a582.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.example.multidatasource.common.aspect.DataSourceAspect.around(DataSourceAspect.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.example.multidatasource.crew.service.impl.OilVoyageConsumptionServiceImpl$$EnhancerBySpringCGLIB$$f222ef22.getLatestVoyageConsumptionByVessel(<generated>)
	at com.example.multidatasource.crew.controller.OilVoyageConsumptionController.getLatestVoyageConsumptionByVessel(OilVoyageConsumptionController.java:37)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.example.multidatasource.config.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-18 16:18:38 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 8000 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-07-18 16:18:38 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-18 16:18:38 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-07-18 16:18:39 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-18 16:18:39 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-07-18 16:18:39 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-07-18 16:18:39 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-07-18 16:18:39 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-07-18 16:18:39 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-07-18 16:18:39 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-07-18 16:18:39 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-07-18 16:18:39 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-07-18 16:18:40 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-07-18 16:18:40 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 16:18:40 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 16:18:40 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 16:18:40 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1672 ms
2025-07-18 16:18:40 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-18 16:18:40 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-07-18 16:18:40 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-07-18 16:18:40 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-07-18 16:18:40 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-07-18 16:18:40 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-07-18 16:18:40 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 7c2b7689-d13f-42e9-aafc-96aa85690841

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-18 16:18:41 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@41ad373, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6801b414, org.springframework.security.web.context.SecurityContextPersistenceFilter@46e64760, org.springframework.security.web.header.HeaderWriterFilter@2ab26378, org.springframework.web.filter.CorsFilter@177c41d7, org.springframework.security.web.authentication.logout.LogoutFilter@23a918c7, com.example.multidatasource.config.JwtAuthenticationFilter@14d8444b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2f0ed952, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@559cedee, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4f327096, org.springframework.security.web.session.SessionManagementFilter@643d2dae, org.springframework.security.web.access.ExceptionTranslationFilter@15efda6c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6e4599c0]
2025-07-18 16:18:41 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 54321 (http) with context path '/multi/source/api'
2025-07-18 16:18:41 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 3.764 seconds (JVM running for 4.781)
2025-07-18 16:19:35 [http-nio-54321-exec-1] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 16:19:35 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 16:19:35 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-18 16:19:36 [http-nio-54321-exec-1] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-18 16:19:36 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 16:19:36 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 16:19:36 [http-nio-54321-exec-1] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: crew for method: OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel
2025-07-18 16:19:36 [http-nio-54321-exec-1] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取每个船舶最新的航次油耗记录（包含轻油和重油明细）- vesselId: null, vesselName: null, startDate: null, endDate: null
2025-07-18 16:19:36 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 16:19:36 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ce383c] was not registered for synchronization because synchronization is not active
2025-07-18 16:19:36 [http-nio-54321-exec-1] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 16:19:36 [http-nio-54321-exec-1] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-18 16:19:36 [http-nio-54321-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 16:19:36 [http-nio-54321-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 16:19:36 [http-nio-54321-exec-1] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@18289050 wrapping com.mysql.cj.jdbc.ConnectionImpl@d5a4544] will not be managed by Spring
2025-07-18 16:19:36 [http-nio-54321-exec-1] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==>  Preparing: SELECT latest.vessel_id, latest.vessel_name, latest.fill_date, latest.consum_id, detail.oil_id, detail.oil_name_cn, detail.oil_name_en, detail.oil_mark, detail.oil_type_id, detail.fuel_type_id, detail.oil_unit_id, detail.oil_unit_name, detail.this_voyage_inventory FROM ( SELECT t1.vessel_id, t1.vessel_name, t1.fill_date, t1.consum_id, sh.mmsi_code FROM oil_voyage_consumption_info t1 INNER JOIN common_vessel sh ON t1.vessel_id = sh.vessel_id INNER JOIN ( SELECT ci.vessel_id, MAX(ci.fill_date) AS max_fill_date FROM oil_voyage_consumption_info ci INNER JOIN common_vessel vs ON vs.vessel_id = ci.vessel_id WHERE ci.delete_flag = '0' AND ci.status_flag != '99' GROUP BY ci.vessel_id ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date WHERE t1.delete_flag = '0' AND t1.status_flag != '99' ) latest INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id WHERE detail.delete_flag = '0' AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油') ORDER BY latest.vessel_id, detail.oil_name_cn
2025-07-18 16:19:36 [http-nio-54321-exec-1] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==> Parameters: 
2025-07-18 16:19:38 [http-nio-54321-exec-1] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - <==      Total: 63
2025-07-18 16:19:38 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ce383c]
2025-07-18 16:19:38 [http-nio-54321-exec-1] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 成功获取29条船舶最新航次油耗记录
2025-07-18 16:19:38 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 16:19:38 [http-nio-54321-exec-1] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 16:20:49 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 5360 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-07-18 16:20:49 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-18 16:20:49 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-07-18 16:20:50 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-18 16:20:50 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-07-18 16:20:50 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-07-18 16:20:50 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-07-18 16:20:50 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-07-18 16:20:50 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-07-18 16:20:50 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-07-18 16:20:50 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-07-18 16:20:50 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-07-18 16:20:51 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-07-18 16:20:51 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 16:20:51 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 16:20:51 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-07-18 16:20:51 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1696 ms
2025-07-18 16:20:51 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-18 16:20:51 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-07-18 16:20:51 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-07-18 16:20:51 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-07-18 16:20:51 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-07-18 16:20:51 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-07-18 16:20:52 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 893d6b99-010e-403d-9afd-75d951534c21

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-18 16:20:52 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@78a515e4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@d2291de, org.springframework.security.web.context.SecurityContextPersistenceFilter@22c75c01, org.springframework.security.web.header.HeaderWriterFilter@6af91cc8, org.springframework.web.filter.CorsFilter@51c8f62c, org.springframework.security.web.authentication.logout.LogoutFilter@1f67761b, com.example.multidatasource.config.JwtAuthenticationFilter@1e530163, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5b275811, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@c017175, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@41a374be, org.springframework.security.web.session.SessionManagementFilter@58b91d57, org.springframework.security.web.access.ExceptionTranslationFilter@696b4a95, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2b5c4f17]
2025-07-18 16:20:52 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 54321 (http) with context path '/multi/source/api'
2025-07-18 16:20:52 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 3.742 seconds (JVM running for 4.786)
2025-07-18 16:21:28 [http-nio-54321-exec-1] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 16:21:28 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-18 16:21:28 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-18 16:21:28 [http-nio-54321-exec-1] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-18 16:21:28 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 16:21:29 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 16:21:29 [http-nio-54321-exec-1] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: crew for method: OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel
2025-07-18 16:21:29 [http-nio-54321-exec-1] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取每个船舶最新的航次油耗记录（包含轻油和重油明细）- vesselId: null, vesselName: null, startDate: null, endDate: null
2025-07-18 16:21:29 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 16:21:29 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7426ef21] was not registered for synchronization because synchronization is not active
2025-07-18 16:21:29 [http-nio-54321-exec-1] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 16:21:29 [http-nio-54321-exec-1] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-18 16:21:29 [http-nio-54321-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 16:21:29 [http-nio-54321-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 16:21:29 [http-nio-54321-exec-1] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1742138242 wrapping com.mysql.cj.jdbc.ConnectionImpl@26f9c3b4] will not be managed by Spring
2025-07-18 16:21:29 [http-nio-54321-exec-1] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==>  Preparing: SELECT latest.vessel_id, latest.vessel_name, latest.fill_date, latest.consum_id, latest.mmsi_code, detail.oil_id, detail.oil_name_cn, detail.oil_name_en, detail.oil_mark, detail.oil_type_id, detail.fuel_type_id, detail.oil_unit_id, detail.oil_unit_name, detail.this_voyage_inventory FROM ( SELECT t1.vessel_id, t1.vessel_name, t1.fill_date, t1.consum_id, sh.mmsi_code FROM oil_voyage_consumption_info t1 INNER JOIN common_vessel sh ON t1.vessel_id = sh.vessel_id INNER JOIN ( SELECT ci.vessel_id, MAX(ci.fill_date) AS max_fill_date FROM oil_voyage_consumption_info ci INNER JOIN common_vessel vs ON vs.vessel_id = ci.vessel_id WHERE ci.delete_flag = '0' AND ci.status_flag != '99' GROUP BY ci.vessel_id ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date WHERE t1.delete_flag = '0' AND t1.status_flag != '99' ) latest INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id WHERE detail.delete_flag = '0' AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油') ORDER BY latest.vessel_id, detail.oil_name_cn
2025-07-18 16:21:29 [http-nio-54321-exec-1] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==> Parameters: 
2025-07-18 16:21:29 [http-nio-54321-exec-1] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - <==      Total: 63
2025-07-18 16:21:29 [http-nio-54321-exec-1] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7426ef21]
2025-07-18 16:21:29 [http-nio-54321-exec-1] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 成功获取29条船舶最新航次油耗记录
2025-07-18 16:21:29 [http-nio-54321-exec-1] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 16:21:29 [http-nio-54321-exec-1] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
2025-07-18 16:21:42 [http-nio-54321-exec-2] DEBUG c.e.m.config.JwtAuthenticationFilter - JWT authentication successful for user: admin
2025-07-18 16:21:42 [http-nio-54321-exec-2] DEBUG c.e.m.c.c.DataSourceContextHolder - No data source specified, using default: crew
2025-07-18 16:21:42 [http-nio-54321-exec-2] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 16:21:42 [http-nio-54321-exec-2] INFO  c.e.m.common.aspect.DataSourceAspect - Switched to data source: crew for method: OilVoyageConsumptionServiceImpl.getLatestVoyageConsumptionByVessel
2025-07-18 16:21:42 [http-nio-54321-exec-2] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 获取每个船舶最新的航次油耗记录（包含轻油和重油明细）- vesselId: null, vesselName: null, startDate: null, endDate: null
2025-07-18 16:21:42 [http-nio-54321-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Creating a new SqlSession
2025-07-18 16:21:42 [http-nio-54321-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7ba90b88] was not registered for synchronization because synchronization is not active
2025-07-18 16:21:42 [http-nio-54321-exec-2] DEBUG o.s.jdbc.datasource.DataSourceUtils - Fetching JDBC Connection from DataSource
2025-07-18 16:21:42 [http-nio-54321-exec-2] DEBUG c.e.m.config.DynamicDataSource - Current data source: crew
2025-07-18 16:21:42 [http-nio-54321-exec-2] DEBUG o.m.s.t.SpringManagedTransaction - JDBC Connection [HikariProxyConnection@1305879708 wrapping com.mysql.cj.jdbc.ConnectionImpl@26f9c3b4] will not be managed by Spring
2025-07-18 16:21:42 [http-nio-54321-exec-2] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==>  Preparing: SELECT latest.vessel_id, latest.vessel_name, latest.fill_date, latest.consum_id, latest.mmsi_code, detail.oil_id, detail.oil_name_cn, detail.oil_name_en, detail.oil_mark, detail.oil_type_id, detail.fuel_type_id, detail.oil_unit_id, detail.oil_unit_name, detail.this_voyage_inventory FROM ( SELECT t1.vessel_id, t1.vessel_name, t1.fill_date, t1.consum_id, sh.mmsi_code FROM oil_voyage_consumption_info t1 INNER JOIN common_vessel sh ON t1.vessel_id = sh.vessel_id INNER JOIN ( SELECT ci.vessel_id, MAX(ci.fill_date) AS max_fill_date FROM oil_voyage_consumption_info ci INNER JOIN common_vessel vs ON vs.vessel_id = ci.vessel_id WHERE ci.delete_flag = '0' AND ci.status_flag != '99' AND vs.mmsi_code LIKE CONCAT('%', ?, '%') GROUP BY ci.vessel_id ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date WHERE t1.delete_flag = '0' AND t1.status_flag != '99' AND sh.mmsi_code LIKE CONCAT('%', ?, '%') ) latest INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id WHERE detail.delete_flag = '0' AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油') ORDER BY latest.vessel_id, detail.oil_name_cn
2025-07-18 16:21:42 [http-nio-54321-exec-2] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - ==> Parameters: 413219350(String), 413219350(String)
2025-07-18 16:21:42 [http-nio-54321-exec-2] DEBUG c.e.m.c.m.O.getLatestVoyageConsumptionByVessel - <==      Total: 2
2025-07-18 16:21:42 [http-nio-54321-exec-2] DEBUG org.mybatis.spring.SqlSessionUtils - Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7ba90b88]
2025-07-18 16:21:42 [http-nio-54321-exec-2] INFO  c.e.m.c.s.i.OilVoyageConsumptionServiceImpl - 成功获取1条船舶最新航次油耗记录
2025-07-18 16:21:42 [http-nio-54321-exec-2] DEBUG c.e.m.c.c.DataSourceContextHolder - Switching to data source: crew
2025-07-18 16:21:42 [http-nio-54321-exec-2] DEBUG c.e.m.common.aspect.DataSourceAspect - Restored data source to: crew
