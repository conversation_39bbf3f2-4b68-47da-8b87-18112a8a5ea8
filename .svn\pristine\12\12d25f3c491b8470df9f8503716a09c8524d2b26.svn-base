package com.example.multidatasource.config;

import com.example.multidatasource.auth.service.AuthService;
import com.example.multidatasource.common.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * Spring Security配置
 */
@Slf4j
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Autowired
    private JwtUtil jwtUtil;



    /**
     * JWT认证过滤器
     */
    @Bean
    public JwtAuthenticationFilter jwtAuthenticationFilter() {
        return new JwtAuthenticationFilter(jwtUtil);
    }

    /**
     * CORS配置
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(604800L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    /**
     * Security过滤器链配置
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF
            .csrf().disable()

            // 启用CORS
            .cors().configurationSource(corsConfigurationSource())

            .and()

            // 配置会话管理为无状态
            .sessionManagement()
            .sessionCreationPolicy(SessionCreationPolicy.STATELESS)

            .and()

            // 配置授权规则
            .authorizeRequests()
                // 公开访问的端点（无需认证）
                .antMatchers(
                    // 认证相关接口
                    "/auth/login",
                    "/auth/register",
                    "/auth/validate",

                    // 系统监控接口
                    "/health/**",
                    "/actuator/**",

                    // Swagger文档接口
                    "/swagger-ui/**",
                    "/swagger-ui.html",
                    "/v3/api-docs/**",
                    "/swagger-resources/**",
                    "/webjars/**",

                    // 自定义公开接口（示例）
                    "/public/**",           // 所有/public/开头的接口
                    "/crew/public/**",      // crew模块的公开接口
                    "/voyage/public/**"     // voyage模块的公开接口

                    // 添加更多公开接口...
                ).permitAll()

                // 需要认证的端点
                .antMatchers("/auth/logout", "/auth/refresh", "/auth/user-info").authenticated()
                .antMatchers("/users/**").authenticated()
                .antMatchers("/crew/**", "/voyage/**", "/cargo/**", "/finance/**").authenticated()
                .antMatchers("/sql/**", "/datasource/**").authenticated()

                // 其他所有请求都需要认证
                .anyRequest().authenticated()

            .and()

            // 添加JWT过滤器
            .addFilterBefore(jwtAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class)

            // 配置异常处理
            .exceptionHandling()
            .authenticationEntryPoint((request, response, authException) -> {
                log.warn("Unauthorized access attempt: {}", authException.getMessage());
                response.setStatus(401);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":401,\"message\":\"未授权访问，请先登录\",\"data\":null}");
            })
            .accessDeniedHandler((request, response, accessDeniedException) -> {
                log.warn("Access denied: {}", accessDeniedException.getMessage());
                response.setStatus(403);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":403,\"message\":\"权限不足，拒绝访问\",\"data\":null}");
            });

        return http.build();
    }
}
