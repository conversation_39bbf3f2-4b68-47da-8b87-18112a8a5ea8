-- 海事信息管理模块数据库脚本
-- 兼容 MySQL 5.5+ 版本
-- 简化方案：去掉默认值，在应用层处理时间

-- 海事局配置表
CREATE TABLE `maritime_bureau_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bureau_name` varchar(100) NOT NULL COMMENT '海事局名称',
  `alarm_channel_id` varchar(100) COMMENT '告警channelId',
  `notice_channel_id` varchar(100) COMMENT '通告channelId',
  `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用：1-启用，0-禁用',
  `create_time` datetime COMMENT '创建时间',
  `update_time` datetime COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bureau_name` (`bureau_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海事局配置表';

-- 海事信息表
CREATE TABLE `maritime_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `article_id` varchar(100) NOT NULL COMMENT '文章ID（用于去重）',
  `bureau_name` varchar(100) NOT NULL COMMENT '海事局名称',
  `title` varchar(500) NOT NULL COMMENT '标题',
  `info_type` varchar(20) NOT NULL COMMENT '信息类型：ALARM-航行警告，NOTICE-航行通告',
  `publish_date` date NOT NULL COMMENT '发布日期',
  `url` varchar(1000) NOT NULL COMMENT '详情链接',
  `detail_content` text COMMENT '详细内容',
  `create_time` datetime COMMENT '创建时间',
  `update_time` datetime COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_article_id` (`article_id`),
  KEY `idx_bureau_type_date` (`bureau_name`, `info_type`, `publish_date`),
  KEY `idx_publish_date` (`publish_date`),
  KEY `idx_info_type` (`info_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海事信息表';

-- 插入海事局配置数据
INSERT INTO `maritime_bureau_config` (`bureau_name`, `alarm_channel_id`, `notice_channel_id`, `create_time`, `update_time`) VALUES
('上海海事局', '94DF14CE-1110-415D-A44E-67593E76619F', '8DBDED82-F3E5-413B-824E-51445C79726C', NOW(), NOW()),
('天津海事局', 'BDBA5FAD-6E5D-4867-9F97-0FCF8EFB8636', '2410A8D3-6F58-469B-89B6-4910C40590AE', NOW(), NOW()),
('辽宁海事局', 'C8896863-B101-4C43-8705-536A03EB46FF', '50763510-C4FC-4F01-80ED-D95F3304F47E', NOW(), NOW()),
('河北海事局', '93B73989-D220-45F9-BC32-70A6EBA35180', 'F24BC59D-2AA3-4F33-8A1F-6EFA3BED3C93', NOW(), NOW()),
('山东海事局', '36EA3354-C8F8-4953-ABA0-82D6D989C750', 'E8A9BF8F-7A10-4C13-BAD3-7A6A80A85612', NOW(), NOW()),
('浙江海事局', '8E10EA74-EB9E-4C96-90F8-F891968ADD80', 'DC8D821B-39FB-4690-8FD5-0924C86A7AC7', NOW(), NOW()),
('福建海事局', '7B084057-6038-4570-A0FB-44E9204C4B1D', '3D725583-AC13-4DFC-B74A-A8C47B14A164', NOW(), NOW()),
('广东海事局', '1E478D40-9E85-4918-BF12-478B8A19F4A8', '32FA3793-3941-48F7-B5C3-EC112D2BF8AF', NOW(), NOW()),
('广西海事局', '86DE2FFF-FF2C-47F9-8359-FD1F20D6508F', '8375B077-B2CF-4281-A46B-68E2FF8AA08F', NOW(), NOW()),
('海南海事局', 'D3340711-057B-494B-8FA0-9EEDC4C5EAD9', '5EB28631-6746-4A6F-AAA1-FCA5BFF0A2A9', NOW(), NOW()),
('长江海事局', '93404234-06CC-4507-B2FB-8AF2492D2A3D', 'FB8539BB-3EEF-4C84-B2D8-8046A3F0FA36', NOW(), NOW()),
('江苏海事局', 'B5B0F3C7-630D-4967-B1E6-B06208575D15', 'BD86D1EE-D69D-4A1F-918C-438F6E750071', NOW(), NOW()),
('深圳海事局', '325FDC08-92B4-4313-A63E-E5C165BE98EC', '9A9209E8-7533-462E-8CA4-6A13B1709752', NOW(), NOW()),
('连云港海事局', 'FA4501F3-DBE4-4F70-BC72-6F27132D4E04', '23E8B7D1-F2BD-411E-A9AE-F24F27C706C5', NOW(), NOW()),
('江苏省地方海事局', 'D14ED012-960B-4064-9712-70459A4A0D4D', '5BF518AA-3B6B-46FD-985E-61DE4155BCDC', NOW(), NOW()),
('江西省地方海事局', '533B3954-E373-4C81-83E9-7D85B76BC9C5', 'B10AE251-A585-459F-8D35-5CC65ACE002F', NOW(), NOW());

-- 插入仅有通告channelId的海事局
INSERT INTO `maritime_bureau_config` (`bureau_name`, `alarm_channel_id`, `notice_channel_id`, `create_time`, `update_time`) VALUES
('黑龙江海事局', NULL, '262E87DA-2376-497E-8641-8B877EB91584', NOW(), NOW()),
('重庆地方海事局', NULL, '6E5E69A7-33FF-4B64-B59D-D7584E28695B', NOW(), NOW()),
('浙江省地方海事局', NULL, 'B14708F9-04FD-4916-B47C-FA38A5EB9925', NOW(), NOW()),
('湖北省地方海事局', NULL, '9DD3C7B1-75B4-4AAE-8143-6228DAB7DE1A', NOW(), NOW());
