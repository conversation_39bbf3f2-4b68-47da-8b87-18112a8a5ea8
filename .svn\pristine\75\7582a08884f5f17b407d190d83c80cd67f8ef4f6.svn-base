package com.example.multidatasource.controller;

import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.common.config.DataSourceContextHolder;
import com.example.multidatasource.crew.dto.SeafarerMatchRequestDTO;
import com.example.multidatasource.crew.dto.SeafarerMatchResultDTO;
import com.example.multidatasource.crew.service.SeafarerMatchingService;
import com.example.multidatasource.model.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 福建省占比测试Controller
 */
@Slf4j
@RestController
@RequestMapping("/test")
@Tag(name = "福建省占比测试", description = "测试福建省占比计算的不同模式")
@DataSource(DataSourceContextHolder.DataSourceType.CREW)
public class FujianRatioTestController {

    @Autowired
    private SeafarerMatchingService seafarerMatchingService;

    @PostMapping("/fujian-ratio-all")
    @Operation(summary = "测试匹配所有候选人的福建省占比", 
               description = "每个候选人都会显示自己的占比影响信息")
    public ApiResponse<List<SeafarerMatchResultDTO>> testFujianRatioAllCandidates(
            @RequestBody SeafarerMatchRequestDTO request) {
        try {
            // 强制启用福建省占比验证
            request.setEnableFujianRatioCheck(true);
            request.setFujianRatioMin(0.4);
            request.setFujianRatioMax(0.7);
            
            List<SeafarerMatchResultDTO> result = seafarerMatchingService.matchAllCandidates(request);
            
            log.info("匹配所有候选人完成，共{}个在船船员", result.size());
            for (SeafarerMatchResultDTO dto : result) {
                log.info("在船船员：{}，候选人数量：{}", 
                        dto.getOnBoardSeafarer().getSeafarerName(), 
                        dto.getCandidateSeafarers().size());
                
                for (SeafarerMatchResultDTO.CandidateSeafarerDTO candidate : dto.getCandidateSeafarers()) {
                    if (candidate.getFujianRatioInfo() != null) {
                        log.info("  候选人：{}，占比变化：{}%", 
                                candidate.getSeafarerName(),
                                String.format("%.2f", candidate.getFujianRatioInfo().getRatioChange() * 100));
                    }
                }
            }
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("测试福建省占比失败", e);
            return ApiResponse.error("测试失败: " + e.getMessage());
        }
    }

    @PostMapping("/fujian-ratio-best")
    @Operation(summary = "测试匹配最佳候选人的福建省占比", 
               description = "只显示最佳候选人的占比影响信息")
    public ApiResponse<List<SeafarerMatchResultDTO>> testFujianRatioBestCandidate(
            @RequestBody SeafarerMatchRequestDTO request) {
        try {
            // 强制启用福建省占比验证
            request.setEnableFujianRatioCheck(true);
            request.setFujianRatioMin(0.4);
            request.setFujianRatioMax(0.7);
            
            List<SeafarerMatchResultDTO> result = seafarerMatchingService.matchBestCandidate(request);
            
            log.info("匹配最佳候选人完成，共{}个在船船员", result.size());
            for (SeafarerMatchResultDTO dto : result) {
                log.info("在船船员：{}", dto.getOnBoardSeafarer().getSeafarerName());
                
                if (dto.getBestCandidate() != null) {
                    log.info("  最佳候选人：{}", dto.getBestCandidate().getSeafarerName());
                    if (dto.getBestCandidate().getFujianRatioInfo() != null) {
                        log.info("  占比变化：{}%", 
                                String.format("%.2f", dto.getBestCandidate().getFujianRatioInfo().getRatioChange() * 100));
                    }
                } else {
                    log.info("  未找到合适的候选人");
                }
            }
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("测试福建省占比失败", e);
            return ApiResponse.error("测试失败: " + e.getMessage());
        }
    }

    @PostMapping("/fujian-ratio-legacy")
    @Operation(summary = "测试兼容接口的福建省占比", 
               description = "测试旧接口格式的占比信息显示")
    public ApiResponse<List<Map<String, Object>>> testFujianRatioLegacy(
            @RequestBody SeafarerMatchRequestDTO request) {
        try {
            // 强制启用福建省占比验证
            request.setEnableFujianRatioCheck(true);
            request.setFujianRatioMin(0.4);
            request.setFujianRatioMax(0.7);
            
            List<Map<String, Object>> result = seafarerMatchingService.matchSingleSeafarerForShiftChange(request);
            
            log.info("兼容接口匹配完成，共{}个在船船员", result.size());
            for (Map<String, Object> item : result) {
                log.info("在船船员：{}", item.get("seafarerName"));
                
                Object shiftCrew = item.get("shiftCrew");
                if (shiftCrew instanceof Map) {
                    Map<String, Object> candidate = (Map<String, Object>) shiftCrew;
                    if (!candidate.isEmpty()) {
                        log.info("  候选人：{}", candidate.get("seafarerName"));
                    }
                }
                
                Object fujianRatioInfo = item.get("fujianRatioInfo");
                if (fujianRatioInfo instanceof Map) {
                    Map<String, Object> ratioInfo = (Map<String, Object>) fujianRatioInfo;
                    log.info("  占比变化：{}%", ratioInfo.get("ratioChange"));
                }
            }
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("测试福建省占比失败", e);
            return ApiResponse.error("测试失败: " + e.getMessage());
        }
    }
}
