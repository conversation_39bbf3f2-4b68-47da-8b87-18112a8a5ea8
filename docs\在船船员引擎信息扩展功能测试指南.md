# 在船船员引擎信息扩展功能测试指南

## 功能概述

对`/match-all-candidates-v2`和`/match-best-candidate-v2`两个匹配接口进行扩展，为在船船员信息添加4个船舶引擎相关字段。

## 扩展字段说明

### 新增字段（4个）
| 字段名 | 中文名称 | 数据来源 | 示例值 | 说明 |
|--------|----------|----------|--------|------|
| `mainEngineModel` | 主机型号 | voyage数据源 | "MAN B&W 6S50MC-C" | 在船船员所在船舶的主机型号 |
| `mainEngineManufacturer` | 主机制造厂商 | voyage数据源 | "MAN Energy Solutions" | 在船船员所在船舶的主机制造厂商 |
| `auxiliaryEngineModel` | 辅机型号 | voyage数据源 | "Caterpillar 3512C" | 在船船员所在船舶的辅机型号 |
| `auxiliaryEngineManufacturer` | 辅机制造厂商 | voyage数据源 | "Caterpillar" | 在船船员所在船舶的辅机制造厂商 |

### 目标对象
- **在船船员信息**（`OnBoardSeafarerDTO`）
- **不影响候选船员信息**（`CandidateSeafarerDTO`）

## 技术实现架构

### 数据流程
```
匹配接口调用
→ SeafarerMatchingServiceImpl.getMatchingData()
→ 步骤1: getOnBoardCrews查询（crew数据源，添加mmsiCode字段）
→ 步骤2: enrichOnBoardCrewsWithEngineInfo()
    ├── loadShipEngineInfoMap() - 一次性查询所有船舶引擎信息
    ├── 构建Map<mmsiCode, ShipEngineInfo>缓存
    └── 为每个在船船员从缓存中获取引擎信息
→ 步骤3: 返回包含引擎信息的匹配结果
```

### 性能优化策略
- **批量查询**: 一次性查询所有非国际船舶的引擎信息
- **内存缓存**: 构建MMSI码到引擎信息的Map缓存
- **查询优化**: 避免N次数据库查询，改为1次查询 + Map查找

### 跨数据源关联
```
crew数据源: common_vessel.mmsi_code
    ↓ (MMSI码关联)
voyage数据源: t_sh_ship.mmsi
```

## 测试用例

### 测试用例1：基础功能验证
**目的**: 验证接口能正常返回引擎信息字段

**测试步骤**:
1. 调用`/match-all-candidates-v2`或`/match-best-candidate-v2`接口
2. 检查返回结果中的在船船员信息
3. 验证新增的4个引擎信息字段

**验证点**:
- ✅ 在船船员信息包含4个新增字段
- ✅ 字段值格式正确（非null，可能为空字符串）
- ✅ 候选船员信息不受影响

### 测试用例2：数据准确性验证
**目的**: 验证引擎信息的准确性

**测试数据准备**:
```sql
-- 查询测试在船船员的MMSI码
SELECT 
    a.seafarer_id,
    a.seafarer_name,
    a.vessel_name,
    cv.mmsi_code
FROM crew_seafarer_info a
LEFT JOIN common_vessel cv ON a.vessel_id = cv.vessel_id
WHERE a.status_key = 'ONBOARD'
AND a.delete_flag = '0'
LIMIT 5;

-- 查询对应的船舶引擎信息
SELECT 
    mmsi,
    main_engine_model,
    main_engine_manufacturer,
    auxiliary_engine_model,
    auxiliary_engine_manufacturer
FROM t_sh_ship 
WHERE mmsi = 'TEST_MMSI_CODE'
AND business_model != 3
AND is_delete != 1;
```

**验证点**:
- ✅ 引擎信息与数据库查询结果一致
- ✅ MMSI码关联正确
- ✅ 空值处理正确

### 测试用例3：性能测试
**目的**: 验证性能优化效果

**测试方法**:
1. 记录接口调用前后的响应时间
2. 监控数据库查询次数
3. 观察内存使用情况

**验收标准**:
- ✅ 接口响应时间无明显增加
- ✅ 数据库查询次数：在船船员数量 + 1（引擎信息查询）
- ✅ 内存使用合理（几十条船舶记录的缓存）

### 测试用例4：边界情况测试
**目的**: 验证各种边界情况的处理

**测试场景**:
1. **MMSI码为空**: 在船船员的MMSI码为null或空字符串
2. **船舶信息不存在**: MMSI码在voyage数据源中找不到对应记录
3. **国际船舶**: business_model = 3的船舶应被排除
4. **数据源异常**: voyage数据源连接异常

**验证点**:
- ✅ MMSI码为空时返回空字符串
- ✅ 找不到船舶信息时返回空字符串
- ✅ 异常情况有合理降级处理
- ✅ 日志记录完整

## 测试数据准备

### 数据库检查SQL
```sql
-- 1. 检查crew数据源在船船员数据（包含MMSI码）
SELECT 
    a.seafarer_id,
    a.seafarer_name,
    a.vessel_name,
    a.vessel_id,
    cv.mmsi_code,
    a.status_key
FROM crew_seafarer_info a
LEFT JOIN common_vessel cv ON a.vessel_id = cv.vessel_id
WHERE a.status_key = 'ONBOARD'
AND a.delete_flag = '0'
ORDER BY a.seafarer_name
LIMIT 10;

-- 2. 检查voyage数据源船舶引擎数据
SELECT 
    mmsi,
    main_engine_model,
    main_engine_manufacturer,
    auxiliary_engine_model,
    auxiliary_engine_manufacturer,
    business_model
FROM t_sh_ship 
WHERE business_model != 3
AND is_delete != 1
AND mmsi IS NOT NULL
AND mmsi != ''
ORDER BY mmsi
LIMIT 10;

-- 3. 检查MMSI码关联情况
SELECT 
    crew_mmsi.mmsi_code AS crew_mmsi,
    ship_mmsi.mmsi AS voyage_mmsi,
    ship_mmsi.main_engine_model
FROM (
    SELECT DISTINCT cv.mmsi_code
    FROM crew_seafarer_info a
    LEFT JOIN common_vessel cv ON a.vessel_id = cv.vessel_id
    WHERE a.status_key = 'ONBOARD' AND cv.mmsi_code IS NOT NULL
) crew_mmsi
LEFT JOIN (
    SELECT mmsi, main_engine_model
    FROM t_sh_ship 
    WHERE business_model != 3 AND is_delete != 1
) ship_mmsi ON crew_mmsi.mmsi_code = ship_mmsi.mmsi
LIMIT 10;
```

## 问题排查指南

### 常见问题1：引擎信息字段为空
**可能原因**:
- MMSI码为空或null
- voyage数据源中找不到对应船舶
- 船舶被business_model过滤排除

**排查步骤**:
1. 检查crew数据源的MMSI码数据完整性
2. 验证voyage数据源的船舶数据
3. 确认business_model过滤条件

### 常见问题2：性能问题
**可能原因**:
- 船舶引擎信息查询过慢
- Map缓存构建效率低
- 数据库连接池问题

**排查步骤**:
1. 分析船舶引擎信息查询的执行计划
2. 监控Map缓存的构建时间
3. 检查数据库连接池配置

### 常见问题3：数据不一致
**可能原因**:
- MMSI码在两个数据源中不一致
- 船舶信息更新不及时
- 数据源同步问题

**排查步骤**:
1. 对比两个数据源的MMSI码数据
2. 检查数据更新时间戳
3. 验证数据源同步机制

## 接口示例

### 请求示例
```http
POST /api/crew/match-all-candidates-v2
Content-Type: application/json

{
  "vesselId": "12345",
  "applyDutyId": "67890",
  "onBoardDays": "30"
}
```

### 响应示例
```json
{
  "onBoardCrews": [
    {
      "seafarerId": "12345",
      "seafarerName": "张三",
      "applyDutyName": "轮机长",
      "vesselName": "万邦688",
      "onBoardDate": "2024-10-05 00:00:00",
      "mainEngineModel": "MAN B&W 6S50MC-C",           // 新增
      "mainEngineManufacturer": "MAN Energy Solutions", // 新增
      "auxiliaryEngineModel": "Caterpillar 3512C",     // 新增
      "auxiliaryEngineManufacturer": "Caterpillar"     // 新增
    }
  ],
  "candidateCrews": [
    {
      "seafarerId": "67890",
      "seafarerName": "李四",
      "applyDutyName": "大管轮"
      // 候选船员不包含引擎信息字段
    }
  ]
}
```

## 验收标准

### 功能验收
- ✅ 所有新增字段正确返回
- ✅ 数据准确性验证通过
- ✅ 边界情况处理正常
- ✅ 不影响现有功能

### 性能验收
- ✅ 接口响应时间无明显增加
- ✅ 数据库查询次数合理
- ✅ 内存使用正常

### 稳定性验收
- ✅ 异常情况优雅降级
- ✅ 日志记录完整清晰
- ✅ 数据源切换稳定
