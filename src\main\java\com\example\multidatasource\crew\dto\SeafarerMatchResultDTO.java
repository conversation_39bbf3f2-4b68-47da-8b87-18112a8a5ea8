package com.example.multidatasource.crew.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 船员匹配结果DTO
 */
@Data
@Schema(description = "船员匹配结果")
@JsonInclude(JsonInclude.Include.ALWAYS)  // 确保包含所有字段，包括null值
public class SeafarerMatchResultDTO {

    @Schema(description = "在船船员信息")
    private OnBoardSeafarerDTO onBoardSeafarer;

    @Schema(description = "候选船员列表（匹配所有模式）")
    private List<CandidateSeafarerDTO> candidateSeafarers;

    @Schema(description = "最优候选船员（单个匹配模式）")
    private CandidateSeafarerDTO bestCandidate;

    /**
     * 在船船员信息
     */
    @Data
    @Schema(description = "在船船员信息")
    @JsonInclude(JsonInclude.Include.ALWAYS)
    public static class OnBoardSeafarerDTO {
        @Schema(description = "船员ID")
        private String seafarerId;

        @Schema(description = "船员姓名")
        private String seafarerName;

        @Schema(description = "船舶ID")
        private String vesselId;

        @Schema(description = "船舶名称")
        private String vesselName;

        @Schema(description = "船舶类型标识")
        private String vesselTypeFlag;

        @Schema(description = "船舶类型名称")
        private String vesselTypeName;

        @Schema(description = "申请职务ID")
        private String applyDutyId;

        @Schema(description = "申请职务名称")
        private String applyDutyName;

        @Schema(description = "证书等级ID")
        private String crtLevelId;

        @Schema(description = "证书等级名称")
        private String crtLevelName;

        @Schema(description = "在职职务名称")
        private String onDutyName;

        @Schema(description = "状态")
        private String statusKey;

        @Schema(description = "上船日期")
        private LocalDate onBoardDate;

        @Schema(description = "在船天数")
        private BigDecimal onBoardDays;

        @Schema(description = "籍贯省份")
        private String placeOfOriginProvValue;

        @Schema(description = "是否有江海证书（0-无，1-有）")
        private Integer haveJiangCertificate;

        @Schema(description = "主机型号")
        private String mainEngineModel;

        @Schema(description = "主机制造厂商")
        private String mainEngineManufacturer;

        @Schema(description = "辅机型号")
        private String auxiliaryEngineModel;

        @Schema(description = "辅机制造厂商")
        private String auxiliaryEngineManufacturer;
    }

    /**
     * 候选船员信息
     */
    @Data
    @Schema(description = "候选船员信息")
    @JsonInclude(JsonInclude.Include.ALWAYS)
    public static class CandidateSeafarerDTO {
        @Schema(description = "船员ID")
        private String seafarerId;

        @Schema(description = "船员姓名")
        private String seafarerName;

        @Schema(description = "船舶名称")
        private String vesselName;

        @Schema(description = "申请职务ID")
        private String applyDutyId;

        @Schema(description = "申请职务名称")
        private String applyDutyName;

        @Schema(description = "证书等级ID")
        private String crtLevelId;

        @Schema(description = "证书等级名称")
        private String crtLevelName;

        @Schema(description = "在职职务名称")
        private String onDutyName;

        @Schema(description = "状态")
        private String statusKey;

        @Schema(description = "上船日期")
        private LocalDate onBoardDate;

        @Schema(description = "籍贯省份")
        private String placeOfOriginProvValue;

        @Schema(description = "下船日期")
        private LocalDate downBoardDate;

        @Schema(description = "下船天数")
        private BigDecimal downBoardDays;

        @Schema(description = "评估值")
        private String evaluateValue;

        @Schema(description = "评估分数（用于排序）")
        private Integer evaluateScore;

        @Schema(description = "是否有江海证书（0-无，1-有）")
        private Integer haveJiangCertificate;

        @Schema(description = "是否有即将到期证书（0-无，1-有）")
        private Integer hasExpiringCertificate;

        @Schema(description = "证书列表")
        private String certificates;

        @Schema(description = "匹配原因")
        private String matchReason;

        @Schema(description = "福建省占比影响（仅在启用占比验证时显示）")
        private FujianRatioInfo fujianRatioInfo;

        @Schema(description = "入司时间（年）")
        private Double companyYears;

        @Schema(description = "任该职务年限（年）")
        private Double dutyYears;

        @Schema(description = "任该类型船舶时间（年）")
        private Double vesselTypeYears;

        @Schema(description = "职务组合入司时间合计（年）")
        private Double groupCompanyYears;

        @Schema(description = "职务组合任该职务年限合计（年）")
        private Double groupDutyYears;

        @Schema(description = "职务组合任该类型船舶时间合计（年）")
        private Double groupVesselTypeYears;

        @Schema(description = "搭档职务名称")
        private String partnerDutyName;

        @Schema(description = "搭档船员姓名")
        private String partnerSeafarerName;
    }

    /**
     * 福建省占比信息
     */
    @Data
    @Schema(description = "福建省占比信息")
    @JsonInclude(JsonInclude.Include.ALWAYS)
    public static class FujianRatioInfo {
        @Schema(description = "船舶ID")
        private String vesselId;

        @Schema(description = "当前船员总数")
        private Integer currentTotalCount;

        @Schema(description = "当前福建省船员数")
        private Integer currentFujianCount;

        @Schema(description = "当前福建省占比")
        private Double currentFujianRatio;

        @Schema(description = "替换后船员总数")
        private Integer afterTotalCount;

        @Schema(description = "替换后福建省船员数")
        private Integer afterFujianCount;

        @Schema(description = "替换后福建省占比")
        private Double afterFujianRatio;

        @Schema(description = "占比变化")
        private Double ratioChange;

        @Schema(description = "是否满足占比要求")
        private Boolean meetRatioRequirement;

        @Schema(description = "占比要求范围")
        private String ratioRequirement;
    }
}
