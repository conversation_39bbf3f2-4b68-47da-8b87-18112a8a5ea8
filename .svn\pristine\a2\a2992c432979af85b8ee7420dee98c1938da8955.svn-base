package com.example.multidatasource.config;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * MyBatis多数据源配置
 * 支持多个MySQL数据源的动态切换
 */
@Configuration
public class MyBatisDataSourceConfig {

    /**
     * 船管系统数据源
     */
    @Bean(name = "crewDataSource")
    @ConfigurationProperties(prefix = "datasources.crew")
    public DataSource crewDataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    /**
     * 航次动态管理数据源
     */
    @Bean(name = "voyageDataSource")
    @ConfigurationProperties(prefix = "datasources.voyage")
    public DataSource voyageDataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    /**
     * 货物管理数据源
     */
    @Bean(name = "cargoDataSource")
    @ConfigurationProperties(prefix = "datasources.cargo")
    public DataSource cargoDataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    /**
     * 财务管理数据源
     */
    @Bean(name = "financeDataSource")
    @ConfigurationProperties(prefix = "datasources.finance")
    public DataSource financeDataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    /**
     * 动态数据源路由
     */
    @Bean(name = "dynamicDataSource")
    @Primary
    public DataSource dynamicDataSource(
            @Qualifier("crewDataSource") DataSource crewDataSource,
            @Qualifier("voyageDataSource") DataSource voyageDataSource,
            @Qualifier("cargoDataSource") DataSource cargoDataSource,
            @Qualifier("financeDataSource") DataSource financeDataSource) {
        
        DynamicDataSource dynamicDataSource = new DynamicDataSource();
        
        Map<Object, Object> dataSourceMap = new HashMap<>();
        dataSourceMap.put("crew", crewDataSource);
        dataSourceMap.put("voyage", voyageDataSource);
        dataSourceMap.put("cargo", cargoDataSource);
        dataSourceMap.put("finance", financeDataSource);
        
        dynamicDataSource.setTargetDataSources(dataSourceMap);
        dynamicDataSource.setDefaultTargetDataSource(crewDataSource); // 默认使用crew数据源
        
        return dynamicDataSource;
    }

    /**
     * SqlSessionFactory配置
     */
    @Bean(name = "sqlSessionFactory")
    public SqlSessionFactory sqlSessionFactory(@Qualifier("dynamicDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        
        // 设置MyBatis配置
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setCacheEnabled(true);
        configuration.setLazyLoadingEnabled(true);
        configuration.setMultipleResultSetsEnabled(true);
        configuration.setUseColumnLabel(true);
        configuration.setUseGeneratedKeys(true);
        configuration.setAutoMappingBehavior(org.apache.ibatis.session.AutoMappingBehavior.PARTIAL);
        configuration.setDefaultExecutorType(org.apache.ibatis.session.ExecutorType.SIMPLE);
        configuration.setDefaultStatementTimeout(25000);
        
        sessionFactory.setConfiguration(configuration);
        
        // 设置Mapper XML文件位置
        sessionFactory.setMapperLocations(
            new PathMatchingResourcePatternResolver().getResources("classpath:mapper/**/*.xml")
        );
        
        // 设置类型别名包
        sessionFactory.setTypeAliasesPackage("com.example.multidatasource.entity");
        
        return sessionFactory.getObject();
    }

    /**
     * 事务管理器
     */
    @Bean(name = "transactionManager")
    public DataSourceTransactionManager transactionManager(@Qualifier("dynamicDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
