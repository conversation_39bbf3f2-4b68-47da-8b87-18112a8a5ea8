package com.example.multidatasource.voyage.service.impl;

import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.common.config.DataSourceContextHolder;
import com.example.multidatasource.common.dto.CargoCompanyStatsQueryDTO;
import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.common.dto.VoyageQueryDTO;
import com.example.multidatasource.voyage.entity.CargoCompanyStats;
import com.example.multidatasource.voyage.entity.VoyageInfo;
import com.example.multidatasource.voyage.mapper.VoyageMapper;
import com.example.multidatasource.voyage.service.VoyageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 航次管理服务实现
 */
@Slf4j
@Service
@DataSource(DataSourceContextHolder.DataSourceType.VOYAGE)
public class VoyageServiceImpl implements VoyageService {

    @Autowired
    private VoyageMapper voyageMapper;

    @Override
    public PageResult<VoyageInfo> getVoyageList(Integer page, Integer size) {
        int offset = (page - 1) * size;
        List<VoyageInfo> records = voyageMapper.selectVoyageList(offset, size);
        int total = voyageMapper.countVoyage();
        
        return PageResult.of(records, (long) total, page, size);
    }

    @Override
    public VoyageInfo getVoyageById(String id) {
        return voyageMapper.selectVoyageById(id);
    }

    @Override
    public boolean createVoyage(VoyageInfo voyageInfo) {
        int result = voyageMapper.insertVoyage(voyageInfo);
        return result > 0;
    }

    @Override
    public boolean updateVoyage(VoyageInfo voyageInfo) {
        int result = voyageMapper.updateVoyage(voyageInfo);
        return result > 0;
    }

    @Override
    public boolean deleteVoyage(Long id) {
        int result = voyageMapper.deleteVoyageById(id);
        return result > 0;
    }

    @Override
    public List<VoyageInfo> getVoyageByStatus(String status) {
        return voyageMapper.selectVoyageByStatus(status);
    }

    @Override
    public List<VoyageInfo> getVoyageByShipName(String shipName) {
        return voyageMapper.selectVoyageByShipName(shipName);
    }

    @Override
    public boolean updateVoyageStatus(Long id, String status) {
        int result = voyageMapper.updateVoyageStatus(id, status);
        return result > 0;
    }

    @Override
    public PageResult<VoyageInfo> searchVoyage(VoyageQueryDTO queryDTO) {
        List<VoyageInfo> records = voyageMapper.selectVoyageByConditions(
            queryDTO.getVoyageNo(),
            queryDTO.getShipName(),
            queryDTO.getDeparturePort(),
            queryDTO.getArrivalPort(),
            queryDTO.getStatus(),
            queryDTO.getStartDate(),
            queryDTO.getEndDate(),
            queryDTO.getOffset(),
            queryDTO.getLimit()
        );
        
        int total = voyageMapper.countVoyageByConditions(
            queryDTO.getVoyageNo(),
            queryDTO.getShipName(),
            queryDTO.getDeparturePort(),
            queryDTO.getArrivalPort(),
            queryDTO.getStatus(),
            queryDTO.getStartDate(),
            queryDTO.getEndDate()
        );
        
        return PageResult.of(records, (long) total, queryDTO.getPage(), queryDTO.getSize());
    }

    @Override
    public List<String> getAllShipNames() {
        return voyageMapper.selectAllShipNames();
    }

    @Override
    public List<String> getAllPorts() {
        return voyageMapper.selectAllPorts();
    }

    @Override
    public int getVoyageCount() {
        return voyageMapper.countVoyage();
    }

    @Override
    public PageResult<CargoCompanyStats> getCargoCompanyStats(CargoCompanyStatsQueryDTO queryDTO) {
        // 转换日期格式
        String startDate = queryDTO.getStartDate() != null ? queryDTO.getStartDate().toString() : null;
        String endDate = queryDTO.getEndDate() != null ? queryDTO.getEndDate().toString() : null;

        // 查询数据
        List<CargoCompanyStats> records = voyageMapper.selectCargoCompanyStats(
                queryDTO.getCargoCompanyName(),
                startDate,
                endDate,
                queryDTO.getOffset(),
                queryDTO.getLimit()
        );

        // 统计总数
        int total = voyageMapper.countCargoCompanyStats(
                queryDTO.getCargoCompanyName(),
                startDate,
                endDate
        );

        return PageResult.of(records, (long) total, queryDTO.getPage(), queryDTO.getSize());
    }

    @Override
    public Map<String, Object> getVesselDetailInfo(String vesselName) {
        return voyageMapper.getVesselDetailInfo(vesselName);
    }
}
