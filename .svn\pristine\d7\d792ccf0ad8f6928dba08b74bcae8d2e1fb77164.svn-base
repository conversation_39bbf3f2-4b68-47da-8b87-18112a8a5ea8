package com.example.multidatasource.crew.controller;

import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.common.config.DataSourceContextHolder;
import com.example.multidatasource.common.dto.CrewQueryDTO;
import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.crew.entity.CrewInfo;
import com.example.multidatasource.crew.service.CrewService;
import com.example.multidatasource.model.ApiResponse;
import com.example.multidatasource.model.SqlRequest;
import com.example.multidatasource.model.SqlResponse;
import com.example.multidatasource.service.SqlExecutorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 船管系统Controller
 * 专门处理船员管理相关的数据库操作
 */
@RestController
@RequestMapping("/crew")
@Tag(name = "船管系统", description = "船员管理相关API")
@DataSource(DataSourceContextHolder.DataSourceType.CREW)
public class CrewController {

    @Autowired
    private CrewService crewService;
    
    @Autowired
    private SqlExecutorService sqlExecutorService;

    @PostMapping("/execute")
    @Operation(summary = "执行船管系统SQL", description = "在船管系统数据库中执行SQL语句")
    public ApiResponse<Object> executeSql(@Valid @RequestBody SqlRequest request) {
        try {
            // 确保使用crew数据源
            request.setDataSourceName("crew");
            SqlResponse result = sqlExecutorService.executeQuery(request);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("SQL执行失败: " + e.getMessage());
        }
    }

    @GetMapping("/crew-list")
    @Operation(summary = "分页获取船员列表", description = "分页查询所有船员信息")
    public ApiResponse<PageResult<CrewInfo>> getCrewList(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") int size) {
        try {
            PageResult<CrewInfo> result = crewService.getCrewList(page, size);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("查询船员列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/crew/{id}")
    @Operation(summary = "获取船员详情", description = "根据ID查询船员详细信息")
    public ApiResponse<CrewInfo> getCrewById(
            @Parameter(description = "船员ID") @PathVariable String id) {
        try {
            CrewInfo crew = crewService.getCrewById(id);
            if (crew != null) {
                return ApiResponse.success(crew);
            } else {
                return ApiResponse.error("船员不存在");
            }
        } catch (Exception e) {
            return ApiResponse.error("查询船员详情失败: " + e.getMessage());
        }
    }

    @PostMapping("/crew")
    @Operation(summary = "添加船员", description = "添加新的船员信息")
    public ApiResponse<String> addCrew(@RequestBody CrewInfo crewInfo) {
        try {
            boolean success = crewService.addCrew(crewInfo);
            if (success) {
                return ApiResponse.success("船员添加成功");
            } else {
                return ApiResponse.error("添加船员失败");
            }
        } catch (Exception e) {
            return ApiResponse.error("添加船员失败: " + e.getMessage());
        }
    }

    @PutMapping("/crew/{id}")
    @Operation(summary = "更新船员信息", description = "根据ID更新船员信息")
    public ApiResponse<String> updateCrew(
            @Parameter(description = "船员ID") @PathVariable Long id,
            @RequestBody CrewInfo crewInfo) {
        try {
            crewInfo.setId(id);
            boolean success = crewService.updateCrew(crewInfo);
            if (success) {
                return ApiResponse.success("船员信息更新成功");
            } else {
                return ApiResponse.error("更新船员信息失败，船员不存在");
            }
        } catch (Exception e) {
            return ApiResponse.error("更新船员信息失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/crew/{id}")
    @Operation(summary = "删除船员", description = "根据ID删除船员信息")
    public ApiResponse<String> deleteCrew(@Parameter(description = "船员ID") @PathVariable Long id) {
        try {
            boolean success = crewService.deleteCrew(id);
            if (success) {
                return ApiResponse.success("船员删除成功");
            } else {
                return ApiResponse.error("删除船员失败，船员不存在");
            }
        } catch (Exception e) {
            return ApiResponse.error("删除船员失败: " + e.getMessage());
        }
    }

    @PostMapping("/crew/search")
    @Operation(summary = "多条件查询船员", description = "根据多个条件查询船员信息")
    public ApiResponse<PageResult<CrewInfo>> searchCrew(@RequestBody CrewQueryDTO queryDTO) {
        try {
            PageResult<CrewInfo> result = crewService.searchCrew(queryDTO);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("查询船员失败: " + e.getMessage());
        }
    }

    @GetMapping("/crew/search-by-name")
    @Operation(summary = "按姓名查询船员", description = "根据姓名模糊查询船员")
    public ApiResponse<List<CrewInfo>> searchCrewByName(
            @Parameter(description = "姓名关键字") @RequestParam String name) {
        try {
            List<CrewInfo> result = crewService.searchCrewByName(name);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("查询船员失败: " + e.getMessage());
        }
    }

    @GetMapping("/crew/positions")
    @Operation(summary = "获取所有职位", description = "获取系统中所有的船员职位列表")
    public ApiResponse<List<String>> getAllPositions() {
        try {
            List<String> positions = crewService.getAllPositions();
            return ApiResponse.success(positions);
        } catch (Exception e) {
            return ApiResponse.error("获取职位列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/crew/count")
    @Operation(summary = "获取船员总数", description = "统计系统中的船员总数")
    public ApiResponse<Integer> getCrewCount() {
        try {
            int count = crewService.getCrewCount();
            return ApiResponse.success(count);
        } catch (Exception e) {
            return ApiResponse.error("获取船员总数失败: " + e.getMessage());
        }
    }

    @PutMapping("/crew/batch-status")
    @Operation(summary = "批量更新船员状态", description = "批量更新多个船员的状态")
    public ApiResponse<String> batchUpdateCrewStatus(
            @RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> ids = (List<Long>) request.get("ids");
            String status = (String) request.get("status");
            
            boolean success = crewService.batchUpdateCrewStatus(ids, status);
            if (success) {
                return ApiResponse.success("批量更新成功");
            } else {
                return ApiResponse.error("批量更新失败");
            }
        } catch (Exception e) {
            return ApiResponse.error("批量更新失败: " + e.getMessage());
        }
    }
}
