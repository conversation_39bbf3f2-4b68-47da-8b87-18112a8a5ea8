package com.example.multidatasource.config;

import com.example.multidatasource.common.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;

/**
 * JWT认证过滤器
 */
@Slf4j
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtUtil jwtUtil;

    public JwtAuthenticationFilter(JwtUtil jwtUtil) {
        this.jwtUtil = jwtUtil;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        String requestPath = request.getRequestURI();
        
        // 跳过不需要认证的路径
        if (isPublicPath(requestPath)) {
            filterChain.doFilter(request, response);
            return;
        }

        String authorizationHeader = request.getHeader("Authorization");
        
        String username = null;
        String jwt = null;
        Long userId = null;
        String role = null;

        // 从Authorization头中提取JWT令牌
        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            jwt = authorizationHeader.substring(7);
            try {
                username = jwtUtil.getUsernameFromToken(jwt);
                userId = jwtUtil.getUserIdFromToken(jwt);
                role = jwtUtil.getRoleFromToken(jwt);
            } catch (Exception e) {
                log.debug("Failed to parse JWT token: {}", e.getMessage());
            }
        }

        // 如果能够从令牌中获取用户名，并且当前没有认证信息
        if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {

            // 直接验证JWT令牌（避免循环依赖）
            if (jwtUtil.validateToken(jwt, username)) {

                // 创建认证对象
                JwtUserPrincipal principal = new JwtUserPrincipal(userId, username, role);

                // 创建权限列表
                SimpleGrantedAuthority authority = new SimpleGrantedAuthority("ROLE_" + role.toUpperCase());

                UsernamePasswordAuthenticationToken authToken =
                    new UsernamePasswordAuthenticationToken(
                        principal,
                        null,
                        Collections.singletonList(authority)
                    );

                authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                // 设置认证信息到Security上下文
                SecurityContextHolder.getContext().setAuthentication(authToken);

                log.debug("JWT authentication successful for user: {}", username);
            } else {
                log.debug("JWT token validation failed for user: {}", username);
            }
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 检查是否为公开路径
     */
    private boolean isPublicPath(String path) {
        String[] publicPaths = {
            // 认证相关接口
            "/auth/login",
            "/auth/register",
            "/auth/validate",

            // 系统监控接口
            "/health",
            "/actuator",

            // Swagger文档接口
            "/swagger-ui",
            "/v3/api-docs",
            "/swagger-resources",
            "/webjars",

            // 自定义公开接口
            "/public",              // 通用公开接口
            "/crew/public",         // crew模块公开接口
            "/voyage/public",       // voyage模块公开接口
            "/cargo/public",        // cargo模块公开接口
            "/finance/public"       // finance模块公开接口
        };

        for (String publicPath : publicPaths) {
            if (path.startsWith(publicPath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * JWT用户主体信息
     */
    public static class JwtUserPrincipal {
        private final Long id;
        private final String username;
        private final String role;

        public JwtUserPrincipal(Long id, String username, String role) {
            this.id = id;
            this.username = username;
            this.role = role;
        }

        public Long getId() {
            return id;
        }

        public String getUsername() {
            return username;
        }

        public String getRole() {
            return role;
        }

        @Override
        public String toString() {
            return username;
        }
    }
}
