package com.example.multidatasource.voyage.service.impl;

import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.common.config.DataSourceContextHolder;
import com.example.multidatasource.common.dto.CargoCompanyStatsQueryDTO;
import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.common.dto.VoyageQueryDTO;
import com.example.multidatasource.voyage.entity.CargoCompanyStats;
import com.example.multidatasource.voyage.entity.VoyageInfo;
import com.example.multidatasource.voyage.mapper.VoyageMapper;
import com.example.multidatasource.voyage.service.VoyageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 航次管理服务实现
 */
@Slf4j
@Service
@DataSource(DataSourceContextHolder.DataSourceType.VOYAGE)
public class VoyageServiceImpl implements VoyageService {

    @Autowired
    private VoyageMapper voyageMapper;

    @Override
    public PageResult<VoyageInfo> getVoyageList(Integer page, Integer size) {
        int offset = (page - 1) * size;
        List<VoyageInfo> records = voyageMapper.selectVoyageList(offset, size);
        int total = voyageMapper.countVoyage();
        
        return PageResult.of(records, (long) total, page, size);
    }

    @Override
    public VoyageInfo getVoyageById(String id) {
        return voyageMapper.selectVoyageById(id);
    }

    @Override
    public boolean createVoyage(VoyageInfo voyageInfo) {
        int result = voyageMapper.insertVoyage(voyageInfo);
        return result > 0;
    }

    @Override
    public boolean updateVoyage(VoyageInfo voyageInfo) {
        int result = voyageMapper.updateVoyage(voyageInfo);
        return result > 0;
    }

    @Override
    public boolean deleteVoyage(Long id) {
        int result = voyageMapper.deleteVoyageById(id);
        return result > 0;
    }

    @Override
    public List<VoyageInfo> getVoyageByStatus(String status) {
        return voyageMapper.selectVoyageByStatus(status);
    }

    @Override
    public List<VoyageInfo> getVoyageByShipName(String shipName) {
        return voyageMapper.selectVoyageByShipName(shipName);
    }

    @Override
    public boolean updateVoyageStatus(Long id, String status) {
        int result = voyageMapper.updateVoyageStatus(id, status);
        return result > 0;
    }

    @Override
    public PageResult<VoyageInfo> searchVoyage(VoyageQueryDTO queryDTO) {
        List<VoyageInfo> records = voyageMapper.selectVoyageByConditions(
            queryDTO.getVoyageNo(),
            queryDTO.getShipName(),
            queryDTO.getDeparturePort(),
            queryDTO.getArrivalPort(),
            queryDTO.getStatus(),
            queryDTO.getStartDate(),
            queryDTO.getEndDate(),
            queryDTO.getOffset(),
            queryDTO.getLimit()
        );
        
        int total = voyageMapper.countVoyageByConditions(
            queryDTO.getVoyageNo(),
            queryDTO.getShipName(),
            queryDTO.getDeparturePort(),
            queryDTO.getArrivalPort(),
            queryDTO.getStatus(),
            queryDTO.getStartDate(),
            queryDTO.getEndDate()
        );
        
        return PageResult.of(records, (long) total, queryDTO.getPage(), queryDTO.getSize());
    }

    @Override
    public List<String> getAllShipNames() {
        return voyageMapper.selectAllShipNames();
    }

    @Override
    public List<String> getAllPorts() {
        return voyageMapper.selectAllPorts();
    }

    @Override
    public int getVoyageCount() {
        return voyageMapper.countVoyage();
    }

    @Override
    public PageResult<CargoCompanyStats> getCargoCompanyStats(CargoCompanyStatsQueryDTO queryDTO) {
        // 1. 获取所有货品信息，用于计算未承运货品
        List<Map<String, Object>> allGoodsList = voyageMapper.selectAllCargo();
        Map<String, String> allGoodsMap = allGoodsList.stream()
                .collect(Collectors.toMap(
                        g -> String.valueOf(g.get("id")),
                        g -> (String) g.get("cargoName")
                ));

        // 2. 转换日期格式
        String startDate = queryDTO.getStartDate() != null ? queryDTO.getStartDate().toString() : null;
        String endDate = queryDTO.getEndDate() != null ? queryDTO.getEndDate().toString() : null;

        // 3. 查询主要的统计数据
        List<CargoCompanyStats> records = voyageMapper.selectCargoCompanyStats(
                queryDTO.getCargoCompanyName(),
                startDate,
                endDate,
                queryDTO.getSortBy(),
                queryDTO.getOffset(),
                queryDTO.getLimit()
        );

        // 4. 在Java中计算并填充“未承运货品”字段
        records.forEach(stat -> {
            if (stat.getShippedGoodsIds() != null && !stat.getShippedGoodsIds().isEmpty()) {
                // 创建一个所有货品ID的副本，用于移除操作
                Set<String> unshippedGoodsIds = new HashSet<>(allGoodsMap.keySet());
                // 移除已承运的货品ID
                Set<String> shippedIds = new HashSet<>(Arrays.asList(stat.getShippedGoodsIds().split(",")));
                unshippedGoodsIds.removeAll(shippedIds);

                // 将未承运的货品ID转换为名称并拼接
                String unshippedGoodsNames = unshippedGoodsIds.stream()
                        .map(allGoodsMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining("; "));
                stat.setUnshippedGoods(unshippedGoodsNames);
            } else {
                // 如果该公司没有承运任何货品，则所有货品都未承运
                stat.setUnshippedGoods(String.join("; ", allGoodsMap.values()));
            }
        });

        // 5. 统计总数
        int total = voyageMapper.countCargoCompanyStats(
                queryDTO.getCargoCompanyName(),
                startDate,
                endDate
        );

        return PageResult.of(records, (long) total, queryDTO.getPage(), queryDTO.getSize());
    }

    @Override
    public Map<String, Object> getVesselDetailInfo(String vesselName) {
        return voyageMapper.getVesselDetailInfo(vesselName);
    }
}
