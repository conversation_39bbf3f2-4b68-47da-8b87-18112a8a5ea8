# 多数据源SQL执行和API封装系统

一个基于Spring Boot的企业级多数据源管理系统，支持多个MySQL数据库的动态切换、MyBatis集成和模块化API设计。

## 🚀 项目特性

### 核心功能
- ✅ **多MySQL数据源支持** - 支持同时连接多个MySQL数据库
- ✅ **动态数据源切换** - 通过注解自动切换数据源，无需手动管理
- ✅ **MyBatis集成** - 完整的MyBatis支持，包括Mapper接口和XML配置
- ✅ **参数化查询** - 前端传递业务参数，后端通过MyBatis执行预定义SQL
- ✅ **模块化API设计** - 按业务模块划分API入口（船管系统、航次管理、货物管理等）
- ✅ **AOP数据源切换** - 基于注解的自动数据源切换机制
- ✅ **RESTful API** - 标准的REST API设计
- ✅ **双模式查询** - 支持参数化查询和通用SQL执行两种模式
- ✅ **Swagger文档** - 完整的API文档和在线测试界面
- ✅ **连接池管理** - HikariCP高性能连接池
- ✅ **类型安全** - 使用强类型DTO和实体类，避免类型错误
- ✅ **安全防护** - SQL预定义，有效防止SQL注入攻击

### 技术栈
- **Spring Boot 2.7.18** - 主框架（兼容Java 8）
- **MyBatis 3.5.14** - ORM框架
- **MyBatis Plus 3.5.3.1** - MyBatis增强工具
- **HikariCP** - 数据库连接池
- **SpringDoc OpenAPI** - API文档生成
- **Spring AOP** - 面向切面编程
- **Lombok** - 代码简化工具

## 📋 系统架构

### 模块化设计
系统采用**业务模块分离**的清晰架构设计：

```
src/main/java/com/example/multidatasource/
├── MultiDataSourceApplication.java # 主启动类
├── auth/                           # 身份认证模块（完整实现）
│   ├── entity/     # 用户实体类
│   ├── mapper/     # 用户数据访问层
│   ├── service/    # 认证业务逻辑层
│   └── controller/ # 认证控制器层
├── crew/                           # 船管模块（完整实现）
│   ├── entity/     # 实体类
│   ├── mapper/     # 数据访问层
│   ├── service/    # 业务逻辑层
│   └── controller/ # 控制器层
├── voyage/                         # 航次模块（完整实现）
│   ├── entity/     # 实体类
│   ├── mapper/     # 数据访问层
│   ├── service/    # 业务逻辑层
│   └── controller/ # 控制器层
├── common/                         # 公共组件
│   ├── annotation/ # 自定义注解（@DataSource等）
│   ├── aspect/     # AOP切面（数据源切换）
│   ├── config/     # 公共配置（数据源上下文）
│   ├── dto/        # 数据传输对象（登录、注册、查询等）
│   └── util/       # 工具类（JWT工具等）
├── config/         # 应用配置
├── controller/     # 通用控制器
├── model/          # 通用模型
└── service/        # 通用服务
```

### 数据源配置
每个业务模块对应独立的MySQL数据源：
- **crew** - 船管系统数据库 (`crew_management`)
- **voyage** - 航次动态管理数据库 (`voyage_management`) + 用户认证数据
- **cargo** - 货物管理数据库 (`cargo_management`) *[待实现]*
- **finance** - 财务管理数据库 (`finance_management`) *[待实现]*

**注意**：用户认证数据存储在voyage数据库中的`t_ds_users`表

### API模块划分
- `/api/auth/*` - 身份认证API（✅ 已实现）
- `/api/users/*` - 用户管理API（✅ 已实现）
- `/api/crew/*` - 船管系统相关API（✅ 已实现）
- `/api/voyage/*` - 航次管理相关API（✅ 已实现）
- `/api/cargo/*` - 货物管理相关API（🚧 待实现）
- `/api/finance/*` - 财务管理相关API（🚧 待实现）
- `/api/sql/*` - 通用SQL执行API（✅ 已实现）
- `/api/health/*` - 系统健康检查API（✅ 已实现）
- `/api/datasource/*` - 数据源管理API（✅ 已实现）

### 查询模式对比
#### 参数化查询模式（推荐）
- **前端**：传递业务参数（如姓名、职位、状态等）
- **后端**：通过MyBatis执行预定义的SQL
- **优势**：类型安全、防SQL注入、易维护

#### 通用SQL模式（高级用户）
- **前端**：直接编写SQL语句和参数
- **后端**：动态执行SQL语句
- **优势**：灵活性高、支持复杂查询

### 数据源切换机制
使用 `@DataSource` 注解实现自动数据源切换：
```java
@RestController
@RequestMapping("/crew")
@DataSource(DataSourceContextHolder.DataSourceType.CREW)
public class CrewController {
    // 所有方法自动使用crew数据源
}
```

## 🛠️ 快速开始

### 项目特点
- ✅ **模块化架构** - 业务模块完全分离，便于维护和扩展
- ✅ **多数据源支持** - 每个模块独立数据库，自动切换
- ✅ **代码生成器** - 支持根据数据库表自动生成代码
- ✅ **环境配置** - 支持dev/test/prod/local多环境配置
- ✅ **API文档** - 集成Swagger，自动生成API文档
- ✅ **参数化查询** - 支持安全的参数化SQL查询
- ✅ **身份验证** - 集成JWT认证，支持用户登录、注册、权限控制
- ✅ **灵活数据源** - 认证数据可在不同数据源间灵活切换
- ✅ **清晰结构** - 无重复代码，目录结构清晰明了

### 环境要求
- **Java 8+**
- **Maven 3.6+**
- **MySQL 5.7+** （多个实例）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd multi-datasource-api
```

2. **选择运行环境**
系统提供多个环境配置，默认使用开发环境：

| 环境 | 配置文件 | 说明 | 适用场景 |
|------|----------|------|----------|
| dev | application-dev.yml | 开发环境 | 团队开发 |
| local | application-local.yml | 本地环境 | 个人开发 |
| test | application-test.yml | 测试环境 | 集成测试 |
| prod | application-prod.yml | 生产环境 | 生产部署 |

3. **配置数据库连接**
根据选择的环境，编辑对应的配置文件。以开发环境为例：

编辑 `src/main/resources/application-dev.yml` 文件：
```yaml
# 开发环境数据源配置
datasources:
  crew:
    url: ***********************************************?...
    username: root
    password: 123456
  voyage:
    url: *************************************************?...
    username: root
    password: 123456
  # ... 其他数据源
```

**生产环境安全配置**：
生产环境使用环境变量配置敏感信息：
```bash
export DB_CREW_USERNAME=crew_user
export DB_CREW_PASSWORD=secure_password
export DB_VOYAGE_USERNAME=voyage_user
export DB_VOYAGE_PASSWORD=secure_password
# ... 其他数据源环境变量
```

4. **创建数据库**
根据选择的环境创建对应的数据库：

**开发环境数据库**：
```sql
CREATE DATABASE crew_management_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE voyage_management_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE cargo_management_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE finance_management_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

**本地环境数据库**：
```sql
CREATE DATABASE crew_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE voyage_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE cargo_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE finance_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

**生产环境数据库**：
```sql
-- 生产环境由DBA创建，使用独立的数据库服务器
CREATE DATABASE crew_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE voyage_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE cargo_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE finance_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

5. **创建用户表（必需）**
```sql
-- 用户管理表 (voyage_management数据库) - 身份认证必需
USE voyage_management_dev;  -- 根据环境调整数据库名
CREATE TABLE t_ds_users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密后）',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-激活，inactive-未激活，locked-锁定',
    role VARCHAR(20) DEFAULT 'user' COMMENT '角色：admin-管理员，user-普通用户',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    created_time DATETIME DEFAULT NULL COMMENT '创建时间',
    updated_time DATETIME DEFAULT NULL COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_role (role),
    INDEX idx_created_time (created_time)
);

-- 插入默认管理员用户（密码：admin123）
INSERT INTO t_ds_users (username, password, email, real_name, role, status, created_time, updated_time) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKVjzieMwkOBEDQJIrPDVDDn9DGa', '<EMAIL>', '系统管理员', 'admin', 'active', NOW(), NOW());

-- 插入测试用户（密码：user123）
INSERT INTO t_ds_users (username, password, email, real_name, role, status, created_time, updated_time) VALUES
('testuser', '$2a$10$8.UnVuG9HHgffUDAlk8qfOuVGkqRzgVymGe07xd00DMxs.AQubh4a', '<EMAIL>', '测试用户', 'user', 'active', NOW(), NOW());
```

6. **创建业务表结构**
```sql
-- 船员信息表 (crew_management数据库)
USE crew_management_dev;  -- 根据环境调整数据库名
CREATE TABLE crew_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '姓名',
    position VARCHAR(50) NOT NULL COMMENT '职位',
    phone VARCHAR(20) COMMENT '电话',
    email VARCHAR(100) COMMENT '邮箱',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 航次信息表 (voyage_management数据库)
USE voyage_management_dev;  -- 根据环境调整数据库名
CREATE TABLE voyage_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    voyage_no VARCHAR(50) NOT NULL COMMENT '航次号',
    ship_name VARCHAR(100) NOT NULL COMMENT '船舶名称',
    departure_port VARCHAR(100) COMMENT '出发港口',
    arrival_port VARCHAR(100) COMMENT '到达港口',
    departure_time DATETIME COMMENT '出发时间',
    estimated_arrival_time DATETIME COMMENT '预计到达时间',
    actual_arrival_time DATETIME COMMENT '实际到达时间',
    status VARCHAR(20) DEFAULT 'planned' COMMENT '状态',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 货物信息表 (cargo_management数据库)
USE cargo_management_dev;  -- 根据环境调整数据库名
CREATE TABLE cargo_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    cargo_name VARCHAR(200) NOT NULL COMMENT '货物名称',
    cargo_type VARCHAR(50) COMMENT '货物类型',
    weight DECIMAL(10,2) COMMENT '重量(吨)',
    volume DECIMAL(10,2) COMMENT '体积(立方米)',
    origin VARCHAR(100) COMMENT '起始地',
    destination VARCHAR(100) COMMENT '目的地',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '状态',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

7. **编译和运行**
```bash
# 编译项目
mvn clean compile

# 运行应用（默认开发环境）
mvn spring-boot:run

# 指定环境运行
mvn spring-boot:run -Dspring-boot.run.profiles=local
mvn spring-boot:run -Dspring-boot.run.profiles=test
mvn spring-boot:run -Dspring-boot.run.profiles=prod
```

8. **访问应用**
- **应用地址**: http://localhost:54321/multi/source/api
- **Swagger文档**: http://localhost:54321/multi/source/api/swagger-ui/index.html
- **OpenAPI JSON**: http://localhost:54321/multi/source/api/v3/api-docs
- **Swagger认证**: 点击右上角 "Authorize" 按钮，输入 `Bearer <JWT令牌>`

## 📚 API文档生成和使用

### 🎯 已集成的文档工具

#### 1. Swagger UI（交互式文档）- 推荐
- **访问地址**：http://localhost:54321/multi/source/api/swagger-ui/index.html
- **特点**：
  - ✅ 可视化API文档界面
  - ✅ 在线测试所有接口
  - ✅ 支持JWT认证配置
  - ✅ 自动生成参数说明和示例
  - ✅ 实时响应数据展示

#### 2. OpenAPI JSON（标准格式）
- **访问地址**：http://localhost:54321/multi/source/api/v3/api-docs
- **用途**：
  - 前端代码生成（TypeScript、JavaScript客户端）
  - Postman Collection导入
  - 第三方工具集成
  - 静态文档生成

#### 3. 文档生成脚本
- **Windows**: `scripts\generate-api-docs.bat`
- **Linux/Mac**: `scripts\generate-api-docs.sh`

### 🚀 Swagger UI使用指南

#### 步骤1：启动应用并访问文档
1. 启动应用：`mvn spring-boot:run`
2. 访问Swagger UI：http://localhost:54321/multi/source/api/swagger-ui/index.html

#### 步骤2：配置JWT认证
1. **获取JWT令牌**：
   - 找到 `POST /auth/login` 接口
   - 点击 "Try it out"
   - 输入登录信息：
     ```json
     {
       "username": "admin",
       "password": "admin123",
       "rememberMe": false
     }
     ```
   - 执行后复制响应中的 `accessToken`

2. **配置认证**：
   - 点击右上角 🔒 "Authorize" 按钮
   - 输入：`Bearer <your-jwt-token>`（注意包含"Bearer "前缀）
   - 点击 "Authorize" 然后 "Close"

#### 步骤3：测试API接口
配置认证后，所有需要认证的接口都可以正常测试：
- 🔒 图标表示需要认证的接口
- 可以直接在Swagger UI中输入参数并执行
- 查看实时的请求和响应数据

### 📋 前端开发者使用指南

#### 方法1：直接使用Swagger UI（推荐）
前端开发者可以直接访问Swagger UI查看和测试接口：
- **地址**：http://localhost:54321/multi/source/api/swagger-ui/index.html
- **测试账户**：admin/admin123（管理员）、testuser/user123（普通用户）
- **认证方式**：JWT Bearer Token

#### 方法2：导出OpenAPI规范
1. 访问：http://localhost:54321/multi/source/api/v3/api-docs
2. 保存JSON文件
3. 使用代码生成工具：
   ```bash
   # TypeScript客户端
   openapi-generator-cli generate -i openapi.json -g typescript-axios

   # JavaScript客户端
   openapi-generator-cli generate -i openapi.json -g javascript
   ```

#### 方法3：生成静态文档
运行文档生成脚本：
```bash
# Windows
scripts\generate-api-docs.bat

# Linux/Mac
scripts/generate-api-docs.sh
```

生成的文件：
- `docs/api/openapi.json` - OpenAPI规范文档
- `docs/api/README.md` - API使用说明
- `docs/api/frontend-example.js` - 前端集成示例

### 🔐 API认证说明

#### 认证流程
1. **登录获取令牌**：`POST /auth/login`
2. **使用令牌**：在请求头中添加 `Authorization: Bearer <token>`
3. **令牌过期**：默认1小时，可通过 `/auth/refresh` 刷新

#### 权限级别
- **公开接口**：无需认证（如 `/auth/login`、`/health/*`、`/public/*`）
- **需要登录**：所有业务接口（如 `/crew/*`、`/voyage/*`）
- **管理员权限**：用户管理接口（如 `/users/list`、`/users/{id}/reset-password`）
- **用户自己**：个人信息接口（如 `/users/{id}/password`）

#### 前端集成示例
```javascript
// 1. 登录获取令牌
async function login(username, password) {
  const response = await fetch('/multi/source/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, password, rememberMe: false })
  });
  const result = await response.json();
  if (result.code === 200) {
    localStorage.setItem('token', result.data.accessToken);
    return result.data;
  }
  throw new Error(result.message);
}

// 2. 调用需要认证的API
async function apiCall(url, options = {}) {
  const token = localStorage.getItem('token');
  const headers = {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` }),
    ...options.headers
  };

  const response = await fetch(url, { ...options, headers });
  return await response.json();
}

// 3. 使用示例
const crewList = await apiCall('/multi/source/api/crew/crew-list?page=1&size=10');
const voyageDetail = await apiCall('/multi/source/api/voyage/voyage/1');
```

### 📖 API分组说明

| 分组 | 路径 | 认证要求 | 说明 |
|------|------|----------|------|
| 身份认证 | `/auth/*` | 部分公开 | 用户登录、注册、令牌管理 |
| 用户管理 | `/users/*` | 需要认证 | 用户信息管理、权限控制 |
| 船管系统 | `/crew/*` | 需要认证 | 船员信息管理 |
| 航次管理 | `/voyage/*` | 需要认证 | 航次计划和状态管理 |
| 通用SQL | `/sql/*` | 需要认证 | 动态SQL执行 |
| 系统监控 | `/health/*` | 公开 | 系统状态检查 |
| 公开接口 | `/public/*` | 公开 | 无需认证的通用接口 |

## 📊 当前实现状态

### ✅ 已完成模块

- **身份认证模块** (`/api/auth/*`) - 用户认证系统
  - JWT令牌认证
  - 用户登录/注册/登出
  - 令牌刷新和验证
  - 密码加密存储

- **用户管理模块** (`/api/users/*`) - 用户管理系统
  - 用户CRUD操作
  - 角色权限控制
  - 密码修改和重置
  - 用户状态管理

- **Crew模块** (`/api/crew/*`) - 船员管理系统
  - 完整的CRUD操作
  - 多条件查询和分页
  - 参数化安全查询
  - 统计和批量操作

- **Voyage模块** (`/api/voyage/*`) - 航次管理系统
  - 完整的CRUD操作
  - 多条件查询和分页
  - 状态管理
  - 港口和船舶管理

### 🚧 待实现模块
- **Cargo模块** (`/api/cargo/*`) - 货物管理系统
- **Finance模块** (`/api/finance/*`) - 财务管理系统

### 🛠️ 如何扩展新模块
1. 复制现有模块结构（如crew模块）
2. 修改包名和类名
3. 更新数据源配置
4. 使用代码生成器生成基础代码
5. 根据业务需求调整逻辑

### 环境切换方法

#### 方法1：修改配置文件
编辑 `application.yml` 中的 `spring.profiles.active` 值

#### 方法2：启动参数
```bash
java -jar app.jar --spring.profiles.active=prod
```

#### 方法3：环境变量
```bash
export SPRING_PROFILES_ACTIVE=prod
java -jar app.jar
```

## 🔐 身份认证说明

### 默认用户账户
系统预置了以下测试账户：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | admin123 | admin | 系统管理员，拥有所有权限 |
| testuser | user123 | user | 普通用户，基本权限 |

### JWT令牌使用
1. **获取令牌**：通过 `/api/auth/login` 接口登录获取JWT令牌
2. **使用令牌**：在请求头中添加 `Authorization: Bearer <token>`
3. **令牌过期**：默认1小时过期，可通过 `/api/auth/refresh` 刷新
4. **数据存储**：用户认证数据存储在voyage数据库的t_ds_users表中
5. **权限控制**：
   - `admin` 角色：可访问所有接口
   - `user` 角色：只能访问基本业务接口和自己的用户信息

### 安全特性
- ✅ **密码加密**：使用BCrypt加密存储
- ✅ **JWT认证**：无状态令牌认证
- ✅ **权限控制**：基于角色的访问控制
- ✅ **登录记录**：记录最后登录时间和IP
- ✅ **参数验证**：严格的输入验证
- ✅ **SQL注入防护**：参数化查询
- ✅ **数据源灵活性**：认证数据可在不同数据源间切换

## 📚 API接口说明

### 身份认证API (`/api/auth/*`) - 公开访问

#### 1. 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123",
  "rememberMe": false
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 604800,
    "userInfo": {
      "id": 1,
      "username": "admin",
      "realName": "系统管理员",
      "email": "<EMAIL>",
      "role": "admin",
      "status": "active"
    }
  }
}
```

#### 2. 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "newuser",
  "password": "123456",
  "confirmPassword": "123456",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "realName": "新用户",
  "role": "user"
}
```

#### 3. 用户登出
```http
POST /api/auth/logout
Authorization: Bearer <your-jwt-token>
```

#### 4. 验证令牌
```http
GET /api/auth/validate
Authorization: Bearer <your-jwt-token>
```

### 用户管理API (`/api/users/*`) - 需要认证

#### 1. 获取用户列表（管理员权限）
```http
GET /api/users/list?page=1&size=10
Authorization: Bearer <admin-jwt-token>
```

#### 2. 修改密码
```http
PUT /api/users/{id}/password
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "oldPassword": "oldpass123",
  "newPassword": "newpass123"
}
```

### 船管系统API (`/api/crew/*`) - 需要认证

#### 1. 分页获取船员列表
```http
GET /api/crew/crew-list?page=1&size=10
```

#### 2. 多条件查询船员（推荐）
```http
POST /api/crew/crew/search
Content-Type: application/json

{
  "name": "张三",           // 姓名（模糊查询）
  "position": "船长",       // 职位
  "status": "active",      // 状态
  "phone": "138",          // 电话（模糊查询）
  "page": 1,               // 页码
  "size": 10               // 每页大小
}
```

#### 3. 按姓名查询船员
```http
GET /api/crew/crew/search-by-name?name=张三
```

#### 4. 获取船员详情
```http
GET /api/crew/crew/{id}
```

#### 5. 添加船员
```http
POST /api/crew/crew
Content-Type: application/json

{
  "name": "张三",
  "position": "船长",
  "phone": "13800138000",
  "email": "<EMAIL>",
  "status": "active"
}
```

#### 6. 更新船员信息
```http
PUT /api/crew/crew/{id}
Content-Type: application/json

{
  "name": "张三",
  "position": "大副",
  "phone": "13800138000",
  "email": "<EMAIL>",
  "status": "active"
}
```

#### 7. 删除船员
```http
DELETE /api/crew/crew/{id}
```

#### 8. 获取所有职位列表
```http
GET /api/crew/crew/positions
```

#### 9. 获取船员总数
```http
GET /api/crew/crew/count
```

#### 10. 批量更新船员状态
```http
PUT /api/crew/crew/batch-status
Content-Type: application/json

{
  "ids": [1, 2, 3],
  "status": "inactive"
}
```

### 航次管理API (`/api/voyage/*`) - 参数化查询

#### 1. 分页获取航次列表
```http
GET /api/voyage/voyage-list?page=1&size=10
```

#### 2. 多条件查询航次（推荐）
```http
POST /api/voyage/voyage/search
Content-Type: application/json

{
  "voyageNo": "V2024",         // 航次号（模糊查询）
  "shipName": "远洋号",        // 船舶名称（模糊查询）
  "departurePort": "上海",     // 出发港口（模糊查询）
  "arrivalPort": "洛杉矶",     // 到达港口（模糊查询）
  "status": "planned",         // 状态
  "startDate": "2024-01-01T00:00:00",  // 开始日期
  "endDate": "2024-12-31T23:59:59",    // 结束日期
  "page": 1,                   // 页码
  "size": 10                   // 每页大小
}
```

#### 3. 创建航次
```http
POST /api/voyage/voyage
Content-Type: application/json

{
  "voyageNo": "V2024001",
  "shipName": "远洋号",
  "departurePort": "上海港",
  "arrivalPort": "洛杉矶港",
  "departureTime": "2024-01-15T08:00:00",
  "estimatedArrivalTime": "2024-01-25T18:00:00",
  "status": "planned"
}
```

#### 4. 按状态查询航次
```http
GET /api/voyage/voyage/status/{status}
```

#### 5. 更新航次状态
```http
PUT /api/voyage/voyage/{id}/status?status=sailing
```

#### 6. 获取所有船舶名称
```http
GET /api/voyage/voyage/ships
```

#### 7. 获取所有港口列表
```http
GET /api/voyage/voyage/ports
```

### 货物管理API (`/api/cargo/*`) - 🚧 待实现

> **注意**: 货物管理模块尚未实现，以下为规划的API接口。可以参考crew和voyage模块的实现方式来开发。

#### 1. 分页获取货物列表
```http
GET /api/cargo/cargo-list?page=1&size=10
```

#### 2. 多条件查询货物（推荐）
```http
POST /api/cargo/cargo/search
Content-Type: application/json

{
  "cargoName": "集装箱",       // 货物名称（模糊查询）
  "cargoType": "普通货物",     // 货物类型
  "origin": "上海",           // 起始地（模糊查询）
  "destination": "洛杉矶",     // 目的地（模糊查询）
  "status": "pending",        // 状态
  "minWeight": 10.0,          // 最小重量
  "maxWeight": 100.0,         // 最大重量
  "minVolume": 20.0,          // 最小体积
  "maxVolume": 200.0,         // 最大体积
  "page": 1,                  // 页码
  "size": 10                  // 每页大小
}
```

#### 3. 添加货物
```http
POST /api/cargo/cargo
Content-Type: application/json

{
  "cargoName": "集装箱货物",
  "cargoType": "普通货物",
  "weight": 25.5,
  "volume": 68.0,
  "origin": "上海",
  "destination": "洛杉矶",
  "status": "pending"
}
```

#### 4. 按类型查询货物
```http
GET /api/cargo/cargo/type/{type}
```

#### 5. 获取货物重量统计
```http
GET /api/cargo/cargo/weight-stats
```

#### 6. 获取所有货物类型
```http
GET /api/cargo/cargo/types
```

### 通用SQL执行API (`/api/sql/*`)

#### 1. 执行SQL查询
```http
POST /api/sql/execute
Content-Type: application/json

{
  "dataSourceName": "crew",
  "sql": "SELECT * FROM crew_info WHERE status = ?",
  "parameters": ["active"],
  "queryType": "SELECT",
  "page": 1,
  "size": 10,
  "needCount": true
}
```

#### 2. 简单查询接口
```http
POST /api/sql/query?dataSourceName=crew&sql=SELECT * FROM crew_info&page=1&size=10
```

#### 3. 更新数据接口
```http
POST /api/sql/update?dataSourceName=crew&sql=UPDATE crew_info SET status='inactive' WHERE id=1
```

## 📋 响应格式

### 通用API响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "张三",
    "position": "船长"
  },
  "success": true,
  "timestamp": 1640995200000
}
```

### 分页查询响应格式（参数化查询）
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "name": "张三",
        "position": "船长",
        "status": "active",
        "createdTime": "2024-01-01T10:00:00"
      }
    ],
    "total": 100,
    "current": 1,
    "size": 10,
    "pages": 10
  },
  "success": true,
  "timestamp": 1640995200000
}
```

### SQL执行响应格式（通用SQL模式）
```json
{
  "success": true,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "name": "张三",
      "position": "船长",
      "status": "active"
    }
  ],
  "totalCount": 100,
  "currentPage": 1,
  "pageSize": 10,
  "totalPages": 10,
  "executionTime": 150
}
```

## 🔧 核心配置说明

### 数据源切换注解
```java
// 在Controller类上使用，整个类的所有方法都使用指定数据源
@DataSource(DataSourceContextHolder.DataSourceType.CREW)
public class CrewController {
    // ...
}

// 在Service类上使用（如认证服务）
@DataSource(DataSourceContextHolder.DataSourceType.VOYAGE)
public class UserServiceImpl implements UserService {
    // 用户认证数据使用voyage数据源
}

// 在方法上使用，只有该方法使用指定数据源
@DataSource(DataSourceContextHolder.DataSourceType.VOYAGE)
public ApiResponse<List<VoyageInfo>> getVoyageList() {
    // ...
}

// 使用字符串指定数据源名称
@DataSource(name = "cargo")
public ApiResponse<List<CargoInfo>> getCargoList() {
    // ...
}
```

### 认证数据源配置说明
- **当前配置**：用户认证数据存储在 `voyage` 数据源
- **表名**：`t_ds_users`
- **切换方法**：修改 `UserServiceImpl` 类上的 `@DataSource` 注解
- **灵活性**：可以轻松切换到任何其他数据源（crew、cargo、finance等）

### MyBatis配置
```yaml
# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.example.multidatasource.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
```

### 连接池配置
```yaml
# HikariCP连接池配置
hikari:
  maximum-pool-size: 20
  minimum-idle: 5
  connection-timeout: 30000
  idle-timeout: 600000
  max-lifetime: 1800000
```

## 🛡️ 安全注意事项

1. **SQL注入防护** - 使用参数化查询，避免直接拼接SQL
2. **权限控制** - 建议在生产环境中添加认证和授权机制
3. **数据库权限** - 为应用创建专用数据库用户，限制权限
4. **网络安全** - 在生产环境中使用HTTPS
5. **数据源隔离** - 不同业务模块使用独立的数据库和用户

## 🚀 扩展开发

### 添加新的业务模块

1. **创建新的数据源配置**
```yaml
datasources:
  # 新增业务模块数据源
  newmodule:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************
    username: root
    password: password
    type: mysql
```

2. **更新数据源枚举**
```java
public enum DataSourceType {
    CREW("crew", "船管系统"),
    VOYAGE("voyage", "航次动态管理"),
    CARGO("cargo", "货物管理"),
    FINANCE("finance", "财务管理"),
    NEWMODULE("newmodule", "新业务模块"); // 新增
}
```

3. **创建Controller**
```java
@RestController
@RequestMapping("/newmodule")
@DataSource(DataSourceContextHolder.DataSourceType.NEWMODULE)
public class NewModuleController {
    // 业务逻辑
}
```

### 添加新的Mapper

1. **创建实体类**
```java
@Data
public class NewEntity {
    private Long id;
    private String name;
    // ...
}
```

2. **创建Mapper接口**
```java
@Mapper
public interface NewEntityMapper {
    List<NewEntity> selectAll();
    NewEntity selectById(Long id);
    int insert(NewEntity entity);
    int update(NewEntity entity);
    int deleteById(Long id);
}
```

3. **创建Mapper XML**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.multidatasource.mapper.NewEntityMapper">
    <!-- SQL映射 -->
</mapper>
```

## 📝 开发最佳实践

### 查询模式选择
1. **优先使用参数化查询** - 安全、类型安全、易维护
2. **复杂查询使用通用SQL** - 灵活性高，适合特殊需求
3. **避免在前端拼接SQL** - 使用参数化查询替代

### 开发规范
1. **数据源命名** - 使用有意义的业务模块名称
2. **SQL优化** - 合理使用索引，避免全表扫描
3. **事务管理** - 注意跨数据源事务的处理
4. **日志记录** - 记录数据源切换和SQL执行日志
5. **异常处理** - 统一的异常处理和错误响应
6. **参数验证** - 在DTO中添加适当的验证注解

### 安全建议
1. **使用参数化查询** - 防止SQL注入攻击
2. **输入验证** - 验证所有用户输入
3. **权限控制** - 实现适当的认证和授权机制
4. **数据脱敏** - 敏感数据在日志中脱敏处理

## 💡 使用示例对比

### 场景：查询名字包含"张"的活跃船员

#### 参数化查询模式（推荐）
```javascript
// 前端请求
POST /api/crew/crew/search
{
  "name": "张",
  "status": "active",
  "page": 1,
  "size": 10
}

// 优势：
// ✅ 类型安全
// ✅ 防SQL注入
// ✅ 代码简洁
// ✅ 易于维护
```

#### 通用SQL模式（高级用户）
```javascript
// 前端请求
POST /api/sql/execute
{
  "dataSourceName": "crew",
  "sql": "SELECT * FROM crew_info WHERE name LIKE ? AND status = ? LIMIT ?, ?",
  "parameters": ["%张%", "active", 0, 10],
  "queryType": "SELECT"
}

// 优势：
// ✅ 灵活性高
// ✅ 支持复杂查询
// ✅ 适合特殊需求
```

### 场景：统计不同状态的船员数量

#### 参数化查询模式
```javascript
// 需要多次调用
GET /api/crew/crew/count  // 总数
POST /api/crew/crew/search { "status": "active" }   // 活跃数量
POST /api/crew/crew/search { "status": "inactive" } // 非活跃数量
```

#### 通用SQL模式
```javascript
// 一次查询获取所有统计
POST /api/sql/execute
{
  "dataSourceName": "crew",
  "sql": "SELECT status, COUNT(*) as count FROM crew_info GROUP BY status",
  "queryType": "SELECT"
}
```

## 🧹 项目结构清理

### 清理内容
本项目已完成全面的结构清理：

- ✅ **删除重复代码** - 移除了外层的entity、mapper、service、controller等重复目录
- ✅ **模块化重构** - 将代码按业务模块完全分离
- ✅ **统一命名规范** - 所有模块遵循相同的目录结构和命名规范
- ✅ **清理空目录** - 删除了所有空的目录和无用文件
- ✅ **优化配置** - 更新了MyBatis配置以支持模块化结构

### 目录对比

**清理前（混乱）**：
```
src/main/java/com/example/multidatasource/
├── entity/          # 外层实体（重复）
├── mapper/          # 外层Mapper（重复）
├── service/         # 外层Service（重复）
├── controller/      # 外层Controller（重复）
├── crew/            # 船管模块
└── voyage/          # 航次模块
```

**清理后（清晰）**：
```
src/main/java/com/example/multidatasource/
├── crew/            # 船管模块（完整独立）
├── voyage/          # 航次模块（完整独立）
├── common/          # 公共组件
├── config/          # 应用配置
├── controller/      # 通用控制器
├── model/           # 通用模型
└── service/         # 通用服务
```

### 清理效果
- **代码重复度**: 0%
- **结构清晰度**: 100%
- **维护便利性**: 极高
- **扩展灵活性**: 极高

## 🔄 数据源切换说明

### 认证数据源配置
当前用户认证数据存储配置：
- **数据库**：`voyage_management_dev`
- **表名**：`t_ds_users`
- **数据源**：`VOYAGE`
- **配置位置**：`UserServiceImpl` 类的 `@DataSource` 注解

### 如何切换认证数据源
如果需要将用户认证数据切换到其他数据库：

1. **修改Service注解**：
   ```java
   @Service
   @DataSource(DataSourceContextHolder.DataSourceType.CREW)  // 切换到CREW
   public class UserServiceImpl implements UserService {
       // ...
   }
   ```

2. **在目标数据库中创建用户表**：
   ```sql
   USE crew_management_dev;  -- 或其他目标数据库
   -- 运行 t_ds_users 表创建脚本
   ```

3. **迁移用户数据**（如果需要）：
   ```sql
   INSERT INTO crew_management_dev.t_ds_users
   SELECT * FROM voyage_management_dev.t_ds_users;
   ```

### 数据源切换的优势
- **灵活性**：可根据业务需求调整数据存储位置
- **扩展性**：支持独立的认证数据库
- **维护性**：清晰的数据源划分便于管理

## 📚 相关文档

### 📖 项目文档
- [模块化架构设计](docs/module-architecture.md) - 详细的架构说明
- [清理后的项目结构](docs/clean-architecture.md) - 项目重构说明
- [Spring Security配置指南](docs/security-config-guide.md) - 安全配置说明
- [启动问题排查指南](docs/startup-troubleshooting.md) - 启动问题解决方案
- [数据库反向生成工具](src/test/resources/database-reverse-guide.md) - 代码生成器使用
- [环境配置指南](src/main/resources/config-guide.md) - 多环境配置说明

### 📚 API文档
- **在线文档**: http://localhost:54321/multi/source/api/swagger-ui/index.html - Swagger UI交互式文档
- **OpenAPI规范**: http://localhost:54321/multi/source/api/v3/api-docs - 标准OpenAPI JSON格式
- [Swagger使用指南](docs/swagger-guide.md) - API文档使用方法
- **文档生成脚本**:
  - Windows: `scripts\generate-api-docs.bat`
  - Linux/Mac: `scripts/generate-api-docs.sh`
- **生成的文档**: `docs/api/` 目录下的静态文档文件

## 📞 技术支持

如有问题，请提交Issue或联系开发团队。

## 📄 许可证

MIT License
