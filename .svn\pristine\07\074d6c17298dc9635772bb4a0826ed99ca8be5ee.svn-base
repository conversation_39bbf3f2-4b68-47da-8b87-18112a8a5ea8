package com.example.multidatasource.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 */
@RestController
@RequestMapping("/health")
@Tag(name = "系统健康检查", description = "系统状态和健康检查API")
public class HealthController {
    
    @GetMapping
    @Operation(summary = "健康检查", description = "检查系统运行状态")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("timestamp", LocalDateTime.now());
        response.put("application", "Multi-DataSource API");
        response.put("version", "1.0.0");
        
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/info")
    @Operation(summary = "系统信息", description = "获取系统基本信息")
    public ResponseEntity<Map<String, Object>> info() {
        Map<String, Object> response = new HashMap<>();
        response.put("name", "多数据源SQL执行API");
        response.put("description", "支持多种数据库的SQL执行和结果封装API系统");
        response.put("version", "1.0.0");
        response.put("features", new String[]{
            "多数据源连接支持",
            "动态SQL执行",
            "RESTful API接口",
            "Swagger API文档",
            "分页查询支持",
            "连接池管理"
        });
        
        return ResponseEntity.ok(response);
    }
}
