package com.example.multidatasource.crew.mapper;

import com.example.multidatasource.crew.entity.CrewInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 船员信息Mapper接口
 */
@Mapper
public interface SeafarerScheduleMapper {

    /**
     * 查询所有船员信息（分页）
     */
    List<CrewInfo> selectCrewList(
            @Param("offset") int offset,
            @Param("limit") int limit,
            @Param("seafarerId") String seafarerId,
            @Param("seafarerName") String seafarerName,
            @Param("applyDutyId") String applyDutyId
    );

    /**
     * 统计船员总数
     */
    int countCrew(
            @Param("seafarerId") String seafarerId,
            @Param("seafarerName") String seafarerName,
            @Param("applyDutyId") String applyDutyId
    );

    /**
     * 查询船员证书到期列表
     * @param seafarerId 船员ID
     * @return 证书信息列表
     */
    List<Map<String, Object>> getSeafarerCertificateInfo(@Param("seafarerId") String seafarerId);

    /**
     * 查询船员服务资历
     * @param seafarerId 船员ID
     * @param applyDutyId 申请职务ID（可选）
     * @return 服务资历列表
     */
    List<Map<String, Object>> getSeafarerQualificationInfo(
            @Param("seafarerId") String seafarerId,
            @Param("applyDutyId") String applyDutyId
    );

    /**
     * 查询在船即将到期船员列表
     * @param onBoardDays 在船天数阈值
     * @param vesselId 船舶ID（可选）
     * @param vesselIdIn 船舶ID列表（可选）
     * @param applyDutyId 申请职务ID（可选）
     * @param applyDutyIdIn 申请职务ID列表（可选）
     * @param haveJiangCertificate 是否有江海证书（可选）
     * @return 在船船员列表
     */
    List<Map<String, Object>> getOnBoardCrews(
            @Param("onBoardDays") String onBoardDays,
            @Param("vesselId") String vesselId,
            @Param("vesselIdIn") String vesselIdIn,
            @Param("applyDutyId") String applyDutyId,
            @Param("applyDutyIdIn") String applyDutyIdIn,
            @Param("haveJiangCertificate") String haveJiangCertificate
    );

    /**
     * 查询下船可上船员列表
     * @param downBoardDaysSt 下船天数起始值
     * @param downBoardDaysEd 下船天数结束值
     * @param certificateExpireDate 证书到期天数
     * @param applyDutyId 申请职务ID（可选）
     * @param applyDutyIdIn 申请职务ID列表（可选）
     * @param crtLevelId 证书等级ID（可选）
     * @param crtLevelIdIn 证书等级ID列表（可选）
     * @param haveJiangCertificate 是否有江海证书（可选）
     * @return 候选船员列表
     */
    List<Map<String, Object>> getAvailableCrews(
            @Param("downBoardDaysSt") String downBoardDaysSt,
            @Param("downBoardDaysEd") String downBoardDaysEd,
            @Param("certificateExpireDate") String certificateExpireDate,
            @Param("applyDutyId") String applyDutyId,
            @Param("applyDutyIdIn") String applyDutyIdIn,
            @Param("crtLevelId") String crtLevelId,
            @Param("crtLevelIdIn") String crtLevelIdIn,
            @Param("haveJiangCertificate") String haveJiangCertificate,
            @Param("targetVesselTypeFlag") String targetVesselTypeFlag
    );

    /**
     * 获取船舶当前船员统计信息（包含福建省占比）
     * @param vesselId 船舶ID
     * @return 船员统计信息
     */
    Map<String, Object> getVesselCrewStats(@Param("vesselId") String vesselId);

    /**
     * 批量获取船员籍贯信息
     * @param seafarerIds 船员ID列表
     * @return 船员籍贯信息列表
     */
    List<Map<String, Object>> batchGetSeafarerOrigin(@Param("seafarerIds") List<String> seafarerIds);

    /**
     * 获取单个船员籍贯信息
     * @param seafarerId 船员ID
     * @return 船员籍贯信息
     */
    Map<String, Object> getSeafarerOrigin(@Param("seafarerId") String seafarerId);

    /**
     * 获取搭档统计信息
     * @param partnerDutyName 搭档职务名称
     * @param targetVesselTypeFlag 目标船舶类型
     * @param vesselId 船舶ID（用于过滤特定船舶的搭档）
     * @return 搭档统计信息
     */
    Map<String, Object> getPartnerStats(@Param("partnerDutyName") String partnerDutyName,
                                       @Param("targetVesselTypeFlag") String targetVesselTypeFlag,
                                       @Param("vesselId") String vesselId);
}
