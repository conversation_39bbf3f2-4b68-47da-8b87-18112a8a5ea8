package com.example.multidatasource.voyage.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 航次信息实体类
 */
@Data
public class VoyageInfo {
    private Long id;
    private String voyageNo;
    private String shipName;
    private String departurePort;
    private String arrivalPort;
    private LocalDateTime departureTime;
    private LocalDateTime estimatedArrivalTime;
    private LocalDateTime actualArrivalTime;
    private String status;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
}
