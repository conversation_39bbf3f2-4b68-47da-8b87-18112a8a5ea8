package com.example.multidatasource;

import org.junit.jupiter.api.Test;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

public class PasswordTest {

    @Test
    public void testPasswordMatching() {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        
        // 数据库中的加密密码
        String adminHashFromDB = "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKVjzieMwkOBEDQJIrPDVDDn9DGa";
        String userHashFromDB = "$2a$10$8.UnVuG9HHgffUDAlk8qfOuVGkqRzgVymGe07xd00DMxs.AQubh4a";
        
        // 明文密码
        String adminPlainPassword = "admin123";
        String userPlainPassword = "user123";
        
        // 验证admin密码
        boolean adminMatch = encoder.matches(adminPlainPassword, adminHashFromDB);
        System.out.println("Admin password matches: " + adminMatch);
        System.out.println("Admin - Plain: " + adminPlainPassword);
        System.out.println("Admin - Hash:  " + adminHashFromDB);
        
        // 验证testuser密码
        boolean userMatch = encoder.matches(userPlainPassword, userHashFromDB);
        System.out.println("User password matches: " + userMatch);
        System.out.println("User - Plain: " + userPlainPassword);
        System.out.println("User - Hash:  " + userHashFromDB);
        
        // 生成新的密码哈希用于对比
        System.out.println("\n=== 生成新的密码哈希 ===");
        String newAdminHash = encoder.encode("admin123");
        String newUserHash = encoder.encode("user123");
        
        System.out.println("New admin123 hash: " + newAdminHash);
        System.out.println("New user123 hash:  " + newUserHash);
        
        // 验证新生成的哈希
        System.out.println("New admin hash matches admin123: " + encoder.matches("admin123", newAdminHash));
        System.out.println("New user hash matches user123: " + encoder.matches("user123", newUserHash));
    }
}
