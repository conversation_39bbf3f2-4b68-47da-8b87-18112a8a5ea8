package com.example.multidatasource.auth.controller;

import com.example.multidatasource.auth.entity.User;
import com.example.multidatasource.auth.service.UserService;
import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.common.dto.UserQueryDTO;
import com.example.multidatasource.model.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/users")
@Tag(name = "用户管理", description = "用户信息管理相关API")
public class UserController {

    @Autowired
    private UserService userService;

    @GetMapping("/list")
    @Operation(summary = "分页获取用户列表", description = "分页查询所有用户信息")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<PageResult<User>> getUserList(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") int size) {
        try {
            PageResult<User> result = userService.getUserList(page, size);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("Get user list failed: {}", e.getMessage());
            return ApiResponse.error("查询用户列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取用户详情", description = "根据ID查询用户详细信息")
    @PreAuthorize("hasRole('ADMIN') or #id == authentication.principal.id")
    public ApiResponse<User> getUserById(@Parameter(description = "用户ID") @PathVariable Long id) {
        try {
            User user = userService.getUserById(id);
            if (user != null) {
                // 清除密码信息
                user.setPassword(null);
                return ApiResponse.success(user);
            } else {
                return ApiResponse.error("用户不存在");
            }
        } catch (Exception e) {
            log.error("Get user by id failed: {}", e.getMessage());
            return ApiResponse.error("查询用户详情失败: " + e.getMessage());
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新用户信息", description = "根据ID更新用户信息")
    @PreAuthorize("hasRole('ADMIN') or #id == authentication.principal.id")
    public ApiResponse<String> updateUser(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @RequestBody User user) {
        try {
            user.setId(id);
            // 不允许通过此接口修改密码
            user.setPassword(null);
            
            boolean success = userService.updateUser(user);
            if (success) {
                return ApiResponse.success("用户信息更新成功");
            } else {
                return ApiResponse.error("更新用户信息失败，用户不存在");
            }
        } catch (Exception e) {
            log.error("Update user failed: {}", e.getMessage());
            return ApiResponse.error("更新用户信息失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户", description = "根据ID删除用户")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<String> deleteUser(@Parameter(description = "用户ID") @PathVariable Long id) {
        try {
            boolean success = userService.deleteUser(id);
            if (success) {
                return ApiResponse.success("用户删除成功");
            } else {
                return ApiResponse.error("删除用户失败，用户不存在");
            }
        } catch (Exception e) {
            log.error("Delete user failed: {}", e.getMessage());
            return ApiResponse.error("删除用户失败: " + e.getMessage());
        }
    }

    @PostMapping("/search")
    @Operation(summary = "多条件查询用户", description = "根据多个条件查询用户信息")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<PageResult<User>> searchUsers(@RequestBody UserQueryDTO queryDTO) {
        try {
            PageResult<User> result = userService.searchUsers(queryDTO);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("Search users failed: {}", e.getMessage());
            return ApiResponse.error("查询用户失败: " + e.getMessage());
        }
    }

    @PutMapping("/{id}/status")
    @Operation(summary = "更新用户状态", description = "更新指定用户的状态")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<String> updateUserStatus(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @Parameter(description = "新状态") @RequestParam String status) {
        try {
            boolean success = userService.updateUserStatus(id, status);
            if (success) {
                return ApiResponse.success("用户状态更新成功");
            } else {
                return ApiResponse.error("更新用户状态失败");
            }
        } catch (Exception e) {
            log.error("Update user status failed: {}", e.getMessage());
            return ApiResponse.error("更新用户状态失败: " + e.getMessage());
        }
    }

    @PutMapping("/{id}/password")
    @Operation(summary = "修改用户密码", description = "用户修改自己的密码")
    @PreAuthorize("#id == authentication.principal.id")
    public ApiResponse<String> updateUserPassword(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @RequestBody Map<String, String> passwordData) {
        try {
            String oldPassword = passwordData.get("oldPassword");
            String newPassword = passwordData.get("newPassword");
            
            if (oldPassword == null || newPassword == null) {
                return ApiResponse.error("原密码和新密码不能为空");
            }
            
            boolean success = userService.updateUserPassword(id, oldPassword, newPassword);
            if (success) {
                return ApiResponse.success("密码修改成功");
            } else {
                return ApiResponse.error("密码修改失败");
            }
        } catch (Exception e) {
            log.error("Update user password failed: {}", e.getMessage());
            return ApiResponse.error("密码修改失败: " + e.getMessage());
        }
    }

    @PutMapping("/{id}/reset-password")
    @Operation(summary = "重置用户密码", description = "管理员重置用户密码")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<String> resetUserPassword(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @RequestBody Map<String, String> passwordData) {
        try {
            String newPassword = passwordData.get("newPassword");
            
            if (newPassword == null) {
                return ApiResponse.error("新密码不能为空");
            }
            
            boolean success = userService.resetUserPassword(id, newPassword);
            if (success) {
                return ApiResponse.success("密码重置成功");
            } else {
                return ApiResponse.error("密码重置失败");
            }
        } catch (Exception e) {
            log.error("Reset user password failed: {}", e.getMessage());
            return ApiResponse.error("密码重置失败: " + e.getMessage());
        }
    }

    @GetMapping("/roles/{role}")
    @Operation(summary = "按角色查询用户", description = "根据角色查询用户列表")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<List<User>> getUsersByRole(@Parameter(description = "角色") @PathVariable String role) {
        try {
            List<User> users = userService.getUsersByRole(role);
            return ApiResponse.success(users);
        } catch (Exception e) {
            log.error("Get users by role failed: {}", e.getMessage());
            return ApiResponse.error("查询用户失败: " + e.getMessage());
        }
    }

    @GetMapping("/count")
    @Operation(summary = "获取用户总数", description = "统计系统中的用户总数")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Integer> getUserCount() {
        try {
            int count = userService.getUserCount();
            return ApiResponse.success(count);
        } catch (Exception e) {
            log.error("Get user count failed: {}", e.getMessage());
            return ApiResponse.error("获取用户总数失败: " + e.getMessage());
        }
    }
}
