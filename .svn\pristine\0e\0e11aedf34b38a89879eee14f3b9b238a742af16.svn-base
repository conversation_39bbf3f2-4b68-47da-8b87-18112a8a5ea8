<component name="libraryTable">
  <library name="Maven: com.mysql:mysql-connector-j:8.0.33">
    <CLASSES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33-sources.jar!/" />
    </SOURCES>
  </library>
</component>