<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.multidatasource.auth.mapper.UserMapper">

    <!-- 结果映射 -->
    <resultMap id="UserResultMap" type="com.example.multidatasource.auth.entity.User">
        <id column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="email" property="email"/>
        <result column="phone" property="phone"/>
        <result column="real_name" property="realName"/>
        <result column="status" property="status"/>
        <result column="role" property="role"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="last_login_ip" property="lastLoginIp"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <!-- 根据用户名查询用户 -->
    <select id="selectUserByUsername" resultMap="UserResultMap">
        SELECT * FROM t_ds_users WHERE username = #{username}
    </select>

    <!-- 根据ID查询用户 -->
    <select id="selectUserById" resultMap="UserResultMap">
        SELECT * FROM t_ds_users WHERE id = #{id}
    </select>

    <!-- 插入用户 -->
    <insert id="insertUser" parameterType="com.example.multidatasource.auth.entity.User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_ds_users (username, password, email, phone, real_name, status, role, created_time, updated_time)
        VALUES (#{username}, #{password}, #{email}, #{phone}, #{realName}, #{status}, #{role}, #{createdTime}, #{updatedTime})
    </insert>

    <!-- 更新用户信息 -->
    <update id="updateUser" parameterType="com.example.multidatasource.auth.entity.User">
        UPDATE t_ds_users
        SET email = #{email},
            phone = #{phone},
            real_name = #{realName},
            role = #{role},
            updated_time = #{updatedTime}
        WHERE id = #{id}
    </update>

    <!-- 删除用户 -->
    <delete id="deleteUserById">
        DELETE FROM t_ds_users WHERE id = #{id}
    </delete>

    <!-- 更新用户状态 -->
    <update id="updateUserStatus">
        UPDATE t_ds_users SET status = #{status}, updated_time = NOW() WHERE id = #{id}
    </update>

    <!-- 更新用户密码 -->
    <update id="updateUserPassword">
        UPDATE t_ds_users SET password = #{password}, updated_time = NOW() WHERE id = #{id}
    </update>

    <!-- 更新最后登录信息 -->
    <update id="updateLastLoginInfo">
        UPDATE t_ds_users
        SET last_login_time = #{lastLoginTime},
            last_login_ip = #{lastLoginIp},
            updated_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 查询所有用户（分页） -->
    <select id="selectUserList" resultMap="UserResultMap">
        SELECT * FROM t_ds_users
        ORDER BY created_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计用户总数 -->
    <select id="countUsers" resultType="int">
        SELECT COUNT(*) FROM t_ds_users
    </select>

    <!-- 根据角色查询用户 -->
    <select id="selectUsersByRole" resultMap="UserResultMap">
        SELECT * FROM t_ds_users WHERE role = #{role} ORDER BY created_time DESC
    </select>

    <!-- 根据状态查询用户 -->
    <select id="selectUsersByStatus" resultMap="UserResultMap">
        SELECT * FROM t_ds_users WHERE status = #{status} ORDER BY created_time DESC
    </select>

    <!-- 检查用户名是否存在 -->
    <select id="checkUsernameExists" resultType="int">
        SELECT COUNT(*) FROM t_ds_users WHERE username = #{username}
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="checkEmailExists" resultType="int">
        SELECT COUNT(*) FROM t_ds_users WHERE email = #{email}
    </select>

    <!-- 根据多个条件查询用户 -->
    <select id="selectUsersByConditions" resultMap="UserResultMap">
        SELECT * FROM t_ds_users
        <where>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="email != null and email != ''">
                AND email LIKE CONCAT('%', #{email}, '%')
            </if>
            <if test="realName != null and realName != ''">
                AND real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
            <if test="role != null and role != ''">
                AND role = #{role}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        ORDER BY created_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 统计符合条件的用户总数 -->
    <select id="countUsersByConditions" resultType="int">
        SELECT COUNT(*) FROM t_ds_users
        <where>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="email != null and email != ''">
                AND email LIKE CONCAT('%', #{email}, '%')
            </if>
            <if test="realName != null and realName != ''">
                AND real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
            <if test="role != null and role != ''">
                AND role = #{role}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
    </select>

</mapper>
