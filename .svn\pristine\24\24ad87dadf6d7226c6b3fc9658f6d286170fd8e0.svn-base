package com.example.multidatasource.auth.mapper;

import com.example.multidatasource.auth.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户Mapper接口
 */
@Mapper
public interface UserMapper {

    /**
     * 根据用户名查询用户
     */
    User selectUserByUsername(@Param("username") String username);

    /**
     * 根据ID查询用户
     */
    User selectUserById(@Param("id") Long id);

    /**
     * 插入用户
     */
    int insertUser(User user);

    /**
     * 更新用户信息
     */
    int updateUser(User user);

    /**
     * 删除用户
     */
    int deleteUserById(@Param("id") Long id);

    /**
     * 更新用户状态
     */
    int updateUserStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 更新用户密码
     */
    int updateUserPassword(@Param("id") Long id, @Param("password") String password);

    /**
     * 更新最后登录信息
     */
    int updateLastLoginInfo(@Param("id") Long id, 
                           @Param("lastLoginTime") LocalDateTime lastLoginTime,
                           @Param("lastLoginIp") String lastLoginIp);

    /**
     * 查询所有用户（分页）
     */
    List<User> selectUserList(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 统计用户总数
     */
    int countUsers();

    /**
     * 根据角色查询用户
     */
    List<User> selectUsersByRole(@Param("role") String role);

    /**
     * 根据状态查询用户
     */
    List<User> selectUsersByStatus(@Param("status") String status);

    /**
     * 检查用户名是否存在
     */
    int checkUsernameExists(@Param("username") String username);

    /**
     * 检查邮箱是否存在
     */
    int checkEmailExists(@Param("email") String email);

    /**
     * 根据多个条件查询用户
     */
    List<User> selectUsersByConditions(@Param("username") String username,
                                      @Param("email") String email,
                                      @Param("realName") String realName,
                                      @Param("role") String role,
                                      @Param("status") String status,
                                      @Param("offset") Integer offset,
                                      @Param("limit") Integer limit);

    /**
     * 统计符合条件的用户总数
     */
    int countUsersByConditions(@Param("username") String username,
                              @Param("email") String email,
                              @Param("realName") String realName,
                              @Param("role") String role,
                              @Param("status") String status);
}
