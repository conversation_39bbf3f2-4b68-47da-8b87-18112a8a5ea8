package com.example.multidatasource.crew.mapper;

import com.example.multidatasource.crew.dto.VoyageConsumptionSummaryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 航次油耗信息Mapper
 */
@Mapper
public interface OilVoyageConsumptionMapper {

    /**
     * 获取每个船舶最新的航次油耗记录（包含轻油和重油明细）
     * 核心功能：主表取最新记录 + 子表关联查询
     * @param vesselId 船舶ID（可选，为空则查询所有船舶）
     * @param vesselName 船舶名称（可选，支持模糊查询）
     * @param startDate 开始日期（可选，格式：yyyy-MM-dd）
     * @param endDate 结束日期（可选，格式：yyyy-MM-dd）
     * @return 最新航次油耗记录列表
     */
    List<VoyageConsumptionSummaryDTO> getLatestVoyageConsumptionByVessel(
            @Param("vesselId") String vesselId,
            @Param("vesselName") String vesselName,
            @Param("mmsiCode") String mmsiCode,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate
    );
}
