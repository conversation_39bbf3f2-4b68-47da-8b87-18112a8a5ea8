# 船员证书等级匹配功能说明

## 功能概述
在crew模块的船员上下船匹配系统中新增了智能证书等级匹配规则，确保上船船员的证书等级满足船舶要求。

## 数据模型

### 船员证书等级体系（crew数据源）
```
甲类证书: 200(甲类), 201(甲一), 202(甲二)
乙类证书: 221(乙一), 其他乙类
丙类证书: 210(丙类), 211(丙一), 212(丙二)  
丁类证书: 230(丁类), 230(其他)
```

### 船舶分类规则（voyage数据源）
- **航区分类**: 1(沿海)→丙类船舶, 2(无限航区)→甲类船舶
- **等级分类**: ≥3000吨/KW→一类, <3000吨/KW→二类
- **职务差异**: 船长/大副看总吨位，轮机长/大管轮看主机功率

## 匹配规则

### 证书类别优先级体系
```
优先级排序：甲 > 乙 > 丙 > 丁
高优先级证书可以上低优先级要求的船舶
低优先级证书不能上高优先级要求的船舶
```

### 四大头职务（船长、大副、轮机长、大管轮）
**支持跨类别优先级匹配 + 精确等级匹配**
```
甲一 → 可上甲一、甲二、丙一、丙二船舶
甲二 → 可上甲二、丙一、丙二船舶  
甲类 → 可上甲类、丙类船舶
乙一 → 可上丙一、丙二船舶
乙类 → 可上丙类船舶
丙一 → 可上丙一、丙二船舶
丙二 → 可上丙二船舶
丙类 → 可上丙类船舶
丁类 → 不能上任何船舶
```

### 非四大头职务（二副、三副等）
**仅支持首字精确匹配，不支持优先级跨类别匹配**
```
甲类 → 只能上甲类船舶（首字匹配）
乙类 → 只能上乙类船舶（首字匹配）
丙类 → 只能上丙类船舶（首字匹配）
丁类 → 只能上丁类船舶（首字匹配）
```

**重要说明**: 非四大头职务不享受优先级跨类别匹配的便利，必须严格按照证书类别首字与船舶类别首字完全匹配。

## 匹配矩阵

### 四大头职务匹配矩阵
| 船员证书 \ 船舶要求 | 甲类船舶 | 乙类船舶 | 丙类船舶 | 丁类船舶 |
|-------------------|---------|---------|---------|---------|
| **甲类证书**       | ✅      | ✅      | ✅      | ✅      |
| **乙类证书**       | ❌      | ✅      | ✅      | ✅      |
| **丙类证书**       | ❌      | ❌      | ✅      | ✅      |
| **丁类证书**       | ❌      | ❌      | ❌      | ✅      |

### 非四大头职务匹配矩阵
| 船员证书 \ 船舶要求 | 甲类船舶 | 乙类船舶 | 丙类船舶 | 丁类船舶 |
|-------------------|---------|---------|---------|---------|
| **甲类证书**       | ✅      | ❌      | ❌      | ❌      |
| **乙类证书**       | ❌      | ✅      | ❌      | ❌      |
| **丙类证书**       | ❌      | ❌      | ✅      | ❌      |
| **丁类证书**       | ❌      | ❌      | ❌      | ✅      |

## 典型场景示例

### 四大头职务场景
```
✅ 甲一船长 → 甲一船舶（完全匹配）
✅ 甲一船长 → 甲二船舶（同类别向下兼容）
✅ 甲一船长 → 丙一船舶（跨类别向下兼容）
✅ 乙一船长 → 丙二船舶（跨类别向下兼容）
❌ 甲二船长 → 甲一船舶（同类别不能向上）
❌ 乙一船长 → 甲二船舶（跨类别不能向上）
❌ 丙一船长 → 甲二船舶（跨类别不能向上）
```

### 非四大头职务场景（严格首字匹配）
```
✅ 甲类二副 → 甲类船舶（首字匹配）
✅ 丙类二副 → 丙类船舶（首字匹配）
✅ 乙类二副 → 乙类船舶（首字匹配）
❌ 甲类二副 → 丙类船舶（首字不匹配，不支持跨类别）
❌ 丙类二副 → 甲类船舶（首字不匹配，不支持跨类别）
❌ 乙一二副 → 丙类船舶（首字不匹配，不支持跨类别）
❌ 甲一二副 → 丙类船舶（首字不匹配，不支持跨类别）
```

**关键区别**: 非四大头职务不享受四大头职务的优先级跨类别匹配特权，必须严格按证书类别首字匹配。

## 技术实现

### 跨数据源设计
由于船舶信息分布在两个数据源中，且船舶ID不一致：
- **crew数据源**: 存储船员信息，包含`vessel_name`（船舶名称）
- **voyage数据源**: 存储船舶详细信息，包含`ship_name_cn`（中文船名）、`navigation_area`、`gross_tonage`、`main_engine_power_kw`等关键参数

**关联策略**: 通过船舶名称进行关联：`crew.vessel_name = voyage.ship_name_cn`

**设计原因**: 两个数据源中的船舶ID字段值不同，无法直接通过ID关联，因此采用船舶名称作为关联键。

### 核心类
- **CertificateLevelMatcher**: 证书等级匹配工具类
- **SeafarerMatchingServiceImpl**: 船员匹配服务（增强版）
- **VoyageService**: 提供船舶信息查询服务

### 关键方法
- `CertificateLevelMatcher.isLevelMatched()`: 主入口，判断证书等级是否匹配
- `VoyageService.getVesselDetailInfo()`: 通过船舶名称查询船舶详细信息
- `SeafarerMatchingServiceImpl.isCertificateLevelMatched()`: 证书等级匹配验证

### 匹配流程
```
1. 职务匹配 → 2. 船-证匹配 → 3. 证书等级匹配 → 4. 服务资历匹配
   ↓
   证书等级匹配详细流程：
   a. 获取候选船员的证书等级和职务信息
   b. 获取目标船舶名称（从在船船员信息中获取）
   c. 通过目标船舶名称跨数据源查询船舶详细信息
   d. 执行证书等级兼容性判断
```

**重要说明**: 证书等级匹配是基于目标船舶（需要上船的船舶）的要求，而不是候选船员当前所在船舶的信息。

## API接口

### 现有接口自动增强
- `POST /crew/match-all-candidates-v2`: 匹配所有候选人
- `POST /crew/match-best-candidate-v2`: 匹配最优候选人

### 无需额外参数
系统自动根据船员证书等级和船舶信息进行匹配，匹配结果中会自动过滤掉证书等级不符合要求的候选人。

## 关键修正

### 目标船舶信息获取
在船员匹配过程中，证书等级匹配需要基于**目标船舶**（需要上船的船舶）的要求进行判断：

- **正确做法**: 从`onBoardCrew`（在船船员信息）中获取`vesselName`，这是目标船舶的名称
- **错误做法**: 从`candidate`（候选船员信息）中获取船舶信息，这是候选船员当前所在船舶

**逻辑原因**: 候选船员在匹配时通常是不在船状态，即使在船也不是目标船舶，因此需要匹配的是目标船舶的证书要求。

## 数据库配置

### 跨数据源查询
```sql
-- 在VoyageMapper.xml中通过船舶名称查询船舶信息
SELECT
    id AS vesselId,
    ship_name_cn AS shipName,
    navigation_area AS navigationArea,
    gross_tonage AS grossTonage,
    main_engine_power_kw AS mainEnginePowerKw
FROM t_sh_ship
WHERE ship_name_cn = #{vesselName}
LIMIT 1
```

### 数据关联关系
- crew数据源中的`vessel_name`字段 = voyage数据源中的`ship_name_cn`字段
- 通过船舶名称进行跨数据源关联查询

## 测试验证

### 测试覆盖
- ✅ 四大头职务跨类别优先级匹配
- ✅ 四大头职务同类别等级匹配  
- ✅ 非四大头职务首字精确匹配
- ✅ 边界情况和异常处理
- ✅ 船舶等级计算（基于吨位/功率）

### 运行测试
```bash
mvn test -Dtest=CertificateLevelMatcherTest
```

## 部署说明

### 无需额外配置
- 功能自动集成到现有匹配接口
- 无需修改数据库结构
- 无需更新API接口参数

### 兼容性保证
- 向下兼容：原有匹配逻辑完全保留
- 增量增强：只是增加了证书等级验证
- 安全限制：低优先级证书仍不能上高优先级船舶

## 监控建议

### 关键指标
- 匹配成功率变化
- 四大头职务跨类别匹配使用频率
- 证书等级不匹配的拒绝率

### 调试日志
```yaml
logging:
  level:
    com.example.multidatasource.crew.util.CertificateLevelMatcher: DEBUG
```

## 总结

此功能实现了科学的证书等级匹配体系：
1. **四大头职务**：支持优先级跨类别匹配，提高配置灵活性
2. **非四大头职务**：保持首字精确匹配，确保专业对口
3. **安全保障**：低优先级证书不能上高优先级船舶
4. **向下兼容**：不影响现有正确匹配逻辑

功能已通过全面测试验证，可以安全部署使用。
