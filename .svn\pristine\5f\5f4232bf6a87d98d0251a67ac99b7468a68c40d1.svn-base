<component name="libraryTable">
  <library name="Maven: com.oracle.database.jdbc:ojdbc8:21.5.0.0">
    <CLASSES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/com/oracle/database/jdbc/ojdbc8/21.5.0.0/ojdbc8-21.5.0.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/com/oracle/database/jdbc/ojdbc8/21.5.0.0/ojdbc8-21.5.0.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/com/oracle/database/jdbc/ojdbc8/21.5.0.0/ojdbc8-21.5.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>