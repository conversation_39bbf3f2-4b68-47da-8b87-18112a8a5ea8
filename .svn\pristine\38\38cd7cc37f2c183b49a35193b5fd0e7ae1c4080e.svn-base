package com.example.multidatasource.crew.service.impl;

import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.common.config.DataSourceContextHolder;
import com.example.multidatasource.crew.dto.SeafarerMatchRequestDTO;
import com.example.multidatasource.crew.dto.SeafarerMatchResultDTO;
import com.example.multidatasource.crew.mapper.SeafarerScheduleMapper;
import com.example.multidatasource.crew.service.SeafarerMatchingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 船员匹配服务实现类
 * 优化后的匹配逻辑，提升性能和可维护性
 */
@Slf4j
@Service
@DataSource(DataSourceContextHolder.DataSourceType.CREW)
public class SeafarerMatchingServiceImpl implements SeafarerMatchingService {

    @Autowired
    private SeafarerScheduleMapper seafarerScheduleMapper;

    /**
     * 船舶类型与证书的映射关系
     */
    private static final Map<Integer, Set<String>> VESSEL_CERTIFICATE_MAPPING = new HashMap<>();
    
    static {
        // 1001油化船：T01、T02和T03
        VESSEL_CERTIFICATE_MAPPING.put(1001, new HashSet<>(Arrays.asList("T01", "T02", "T03")));
        // 1002液化气船：T04、T05
        VESSEL_CERTIFICATE_MAPPING.put(1002, new HashSet<>(Arrays.asList("T04", "T05")));
        // 112化学品船：T01和T03
        VESSEL_CERTIFICATE_MAPPING.put(112, new HashSet<>(Arrays.asList("T01", "T03")));
        // 134油船：T01、T02
        VESSEL_CERTIFICATE_MAPPING.put(134, new HashSet<>(Arrays.asList("T01", "T02")));
    }

    /**
     * 评估等级与分数的映射关系
     */
    private static final Map<String, Integer> EVALUATE_SCORE_MAPPING = new HashMap<>();
    
    static {
        EVALUATE_SCORE_MAPPING.put("优异", 100);
        EVALUATE_SCORE_MAPPING.put("良好", 85);
        EVALUATE_SCORE_MAPPING.put("合格", 70);
        EVALUATE_SCORE_MAPPING.put("较弱", 55);
        EVALUATE_SCORE_MAPPING.put("差", 40);
        EVALUATE_SCORE_MAPPING.put("新选项", 25);
    }

    @Override
    public List<SeafarerMatchResultDTO> matchAllCandidates(SeafarerMatchRequestDTO request) {
        log.info("开始匹配所有候选人, 请求参数: {}", request);
        
        try {
            // 1. 获取基础数据
            MatchingData matchingData = getMatchingData(request);
            if (matchingData.onBoardCrews.isEmpty()) {
                log.info("未查询到需要下船的船员");
                return Collections.emptyList();
            }
            
            // 2. 执行匹配逻辑
            List<SeafarerMatchResultDTO> results = new ArrayList<>();
            for (Map<String, Object> onBoardCrew : matchingData.onBoardCrews) {
                SeafarerMatchResultDTO result = new SeafarerMatchResultDTO();
                result.setOnBoardSeafarer(convertToOnBoardSeafarer(onBoardCrew));
                result.setCandidateSeafarers(findAllMatchingCandidates(onBoardCrew, matchingData.availableCrews, request));

                // 添加福建省占比信息
                if (request.getEnableFujianRatioCheck() != null && request.getEnableFujianRatioCheck()) {
                    String vesselId = (String) onBoardCrew.get("vesselId");
                    String replacedSeafarerId = (String) onBoardCrew.get("seafarerId");
                    result.setFujianRatioInfo(calculateFujianRatioInfo(vesselId, null, replacedSeafarerId, request));
                }

                results.add(result);
            }
            
            log.info("匹配完成，共处理{}个在船船员", results.size());
            return results;
            
        } catch (Exception e) {
            log.error("匹配所有候选人失败", e);
            throw new RuntimeException("匹配所有候选人失败: " + e.getMessage());
        }
    }

    @Override
    public List<SeafarerMatchResultDTO> matchBestCandidate(SeafarerMatchRequestDTO request) {
        log.info("开始匹配最优候选人, 请求参数: {}", request);
        
        try {
            // 1. 获取基础数据
            MatchingData matchingData = getMatchingData(request);
            if (matchingData.onBoardCrews.isEmpty()) {
                log.info("未查询到需要下船的船员");
                return Collections.emptyList();
            }
            
            // 2. 执行单个匹配逻辑
            Set<String> assignedCandidateIds = new HashSet<>();
            List<SeafarerMatchResultDTO> results = new ArrayList<>();
            
            for (Map<String, Object> onBoardCrew : matchingData.onBoardCrews) {
                SeafarerMatchResultDTO result = new SeafarerMatchResultDTO();
                result.setOnBoardSeafarer(convertToOnBoardSeafarer(onBoardCrew));

                SeafarerMatchResultDTO.CandidateSeafarerDTO bestCandidate = findBestMatchingCandidate(
                        onBoardCrew, matchingData.availableCrews, assignedCandidateIds, request);
                result.setBestCandidate(bestCandidate);

                // 添加福建省占比信息
                if (request.getEnableFujianRatioCheck() != null && request.getEnableFujianRatioCheck()) {
                    String vesselId = (String) onBoardCrew.get("vesselId");
                    String replacedSeafarerId = (String) onBoardCrew.get("seafarerId");
                    String candidateSeafarerId = bestCandidate != null ? bestCandidate.getSeafarerId() : null;
                    result.setFujianRatioInfo(calculateFujianRatioInfo(vesselId, candidateSeafarerId, replacedSeafarerId, request));
                }

                results.add(result);
            }
            
            log.info("单个匹配完成，共分配{}个候选人", assignedCandidateIds.size());
            return results;
            
        } catch (Exception e) {
            log.error("匹配最优候选人失败", e);
            throw new RuntimeException("匹配最优候选人失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> matchSeafarerForShiftChange(SeafarerMatchRequestDTO request) {
        List<SeafarerMatchResultDTO> results = matchAllCandidates(request);
        return convertToLegacyFormat(results, false);
    }

    @Override
    public List<Map<String, Object>> matchSingleSeafarerForShiftChange(SeafarerMatchRequestDTO request) {
        List<SeafarerMatchResultDTO> results = matchBestCandidate(request);
        return convertToLegacyFormat(results, true);
    }

    /**
     * 获取匹配所需的基础数据
     */
    private MatchingData getMatchingData(SeafarerMatchRequestDTO request) {
        // 查询在船即将到期船员列表
        List<Map<String, Object>> onBoardCrews = seafarerScheduleMapper.getOnBoardCrews(
                request.getOnBoardDays(),
                request.getVesselId(),
                processSplitParam(request.getVesselIdIn()),
                request.getApplyDutyId(),
                processSplitParam(request.getApplyDutyIdIn()),
                request.getHaveJiangCertificate()
        );

        // 查询下船可上船员列表
        List<Map<String, Object>> availableCrews = Collections.emptyList();
        if (!onBoardCrews.isEmpty()) {
            availableCrews = seafarerScheduleMapper.getAvailableCrews(
                    request.getDownBoardDaysSt(),
                    request.getDownBoardDaysEd(),
                    request.getCertificateExpireDate(),
                    request.getApplyDutyId(),
                    processSplitParam(request.getApplyDutyIdIn()),
                    request.getCrtLevelId(),
                    processSplitParam(request.getCrtLevelIdIn()),
                    request.getHaveJiangCertificate()
            );
            
            // 预处理候选船员数据，计算评估分数
            availableCrews = availableCrews.stream()
                    .peek(this::calculateEvaluateScore)
                    .collect(Collectors.toList());
        }

        log.info("查询到{}条在船船员，{}条候选船员", onBoardCrews.size(), availableCrews.size());
        return new MatchingData(onBoardCrews, availableCrews);
    }

    /**
     * 查找所有匹配的候选人
     */
    private List<SeafarerMatchResultDTO.CandidateSeafarerDTO> findAllMatchingCandidates(
            Map<String, Object> onBoardCrew, List<Map<String, Object>> availableCrews, SeafarerMatchRequestDTO request) {

        String onApplyDutyId = (String) onBoardCrew.get("applyDutyId");
        Integer onVesselTypeFlag = (Integer) onBoardCrew.get("vesselTypeFlag");
        String vesselId = (String) onBoardCrew.get("vesselId");
        String replacedSeafarerId = (String) onBoardCrew.get("seafarerId");

        return availableCrews.stream()
                .filter(candidate -> {
                    if (request.getEnableFujianRatioCheck() != null && request.getEnableFujianRatioCheck()) {
                        return isMatchingCandidateWithFujianRatio(candidate, onApplyDutyId, onVesselTypeFlag,
                                                                vesselId, replacedSeafarerId, request);
                    } else {
                        return isMatchingCandidate(candidate, onApplyDutyId, onVesselTypeFlag);
                    }
                })
                .map(this::convertToCandidateSeafarer)
                .collect(Collectors.toList());
    }

    /**
     * 查找最佳匹配的候选人
     */
    private SeafarerMatchResultDTO.CandidateSeafarerDTO findBestMatchingCandidate(
            Map<String, Object> onBoardCrew, List<Map<String, Object>> availableCrews,
            Set<String> assignedIds, SeafarerMatchRequestDTO request) {

        String onApplyDutyId = (String) onBoardCrew.get("applyDutyId");
        Integer onVesselTypeFlag = (Integer) onBoardCrew.get("vesselTypeFlag");
        String vesselId = (String) onBoardCrew.get("vesselId");
        String replacedSeafarerId = (String) onBoardCrew.get("seafarerId");

        return availableCrews.stream()
                .filter(candidate -> {
                    String candidateId = (String) candidate.get("seafarerId");
                    if (assignedIds.contains(candidateId)) {
                        return false;
                    }

                    if (request.getEnableFujianRatioCheck() != null && request.getEnableFujianRatioCheck()) {
                        return isMatchingCandidateWithFujianRatio(candidate, onApplyDutyId, onVesselTypeFlag,
                                                                vesselId, replacedSeafarerId, request);
                    } else {
                        return isMatchingCandidate(candidate, onApplyDutyId, onVesselTypeFlag);
                    }
                })
                .findFirst()
                .map(candidate -> {
                    String candidateId = (String) candidate.get("seafarerId");
                    assignedIds.add(candidateId);
                    SeafarerMatchResultDTO.CandidateSeafarerDTO result = convertToCandidateSeafarer(candidate);
                    result.setMatchReason("最优匹配");
                    return result;
                })
                .orElse(null);
    }

    /**
     * 判断候选人是否匹配
     */
    private boolean isMatchingCandidate(Map<String, Object> candidate, String requiredDutyId, Integer vesselTypeFlag) {
        String candidateDutyId = (String) candidate.get("applyDutyId");
        String certificates = (String) candidate.get("certificates");
        String candidateId = (String) candidate.get("seafarerId");

        // 1. 职务匹配
        if (!requiredDutyId.equals(candidateDutyId)) {
            return false;
        }

        // 2. 船-证匹配
        if (!isCertificateValidForVesselType(certificates, vesselTypeFlag)) {
            return false;
        }

        // 3. 服务资历匹配
        List<Map<String, Object>> qualifications = seafarerScheduleMapper.getSeafarerQualificationInfo(
                candidateId, candidateDutyId);

        return qualifications != null && !qualifications.isEmpty();
    }

    /**
     * 判断候选人是否匹配（包含福建省占比验证）
     */
    private boolean isMatchingCandidateWithFujianRatio(
            Map<String, Object> candidate,
            String requiredDutyId,
            Integer vesselTypeFlag,
            String vesselId,
            String replacedSeafarerId,
            SeafarerMatchRequestDTO request) {

        // 1. 基本匹配条件
        if (!isMatchingCandidate(candidate, requiredDutyId, vesselTypeFlag)) {
            return false;
        }

        // 2. 福建省占比验证（如果启用）
        if (request.getEnableFujianRatioCheck() != null && request.getEnableFujianRatioCheck()) {
            String candidateId = (String) candidate.get("seafarerId");
            return validateFujianRatio(vesselId, candidateId, replacedSeafarerId, request);
        }

        return true;
    }

    /**
     * 船舶-证书匹配验证（优化版本）
     */
    private boolean isCertificateValidForVesselType(String certificates, Integer vesselTypeFlag) {
        if (!StringUtils.hasText(certificates) || vesselTypeFlag == null) {
            return false;
        }

        Set<String> requiredCertificates = VESSEL_CERTIFICATE_MAPPING.get(vesselTypeFlag);
        if (requiredCertificates == null) {
            log.warn("未知的船舶类型: {}", vesselTypeFlag);
            return false;
        }

        return requiredCertificates.stream().allMatch(certificates::contains);
    }

    /**
     * 计算评估分数
     */
    private void calculateEvaluateScore(Map<String, Object> candidate) {
        String evaluateValue = (String) candidate.get("evaluateValue");
        Integer score = null;

        if (StringUtils.hasText(evaluateValue)) {
            // 处理数字分数格式（如"85分"）
            if (evaluateValue.matches("^\\d+分$")) {
                String numberStr = evaluateValue.substring(0, evaluateValue.length() - 1);
                score = Integer.parseInt(numberStr);
            } else {
                // 处理文字评估格式
                score = EVALUATE_SCORE_MAPPING.get(evaluateValue);
            }
        }

        candidate.put("evaluateScore", score != null ? score : 0);
    }

    /**
     * 匹配数据容器类
     */
    private static class MatchingData {
        final List<Map<String, Object>> onBoardCrews;
        final List<Map<String, Object>> availableCrews;

        MatchingData(List<Map<String, Object>> onBoardCrews, List<Map<String, Object>> availableCrews) {
            this.onBoardCrews = onBoardCrews;
            this.availableCrews = availableCrews;
        }
    }

    /**
     * 转换为在船船员DTO
     */
    private SeafarerMatchResultDTO.OnBoardSeafarerDTO convertToOnBoardSeafarer(Map<String, Object> data) {
        SeafarerMatchResultDTO.OnBoardSeafarerDTO dto = new SeafarerMatchResultDTO.OnBoardSeafarerDTO();
        dto.setSeafarerId((String) data.get("seafarerId"));
        dto.setSeafarerName((String) data.get("seafarerName"));
        dto.setVesselId((String) data.get("vesselId"));
        dto.setVesselName((String) data.get("vesselName"));
        dto.setVesselTypeFlag((Integer) data.get("vesselTypeFlag"));
        dto.setVesselTypeName((String) data.get("vesselTypeName"));
        dto.setApplyDutyId((String) data.get("applyDutyId"));
        dto.setApplyDutyName((String) data.get("applyDutyName"));
        dto.setCrtLevelId((String) data.get("crtLevelId"));
        dto.setCrtLevelName((String) data.get("crtLevelName"));
        dto.setOnDutyName((String) data.get("onDutyName"));
        dto.setStatusKey((String) data.get("statusKey"));
        dto.setOnBoardDate(parseDate(data.get("onBoardDate")));
        dto.setOnBoardDays(parseBigDecimal(data.get("onBoardDays")));
        dto.setPlaceOfOriginProvValue((String) data.get("placeOfOriginProvValue"));
        dto.setHaveJiangCertificate((Integer) data.get("haveJiangCertificate"));
        return dto;
    }

    /**
     * 转换为候选船员DTO
     */
    private SeafarerMatchResultDTO.CandidateSeafarerDTO convertToCandidateSeafarer(Map<String, Object> data) {
        SeafarerMatchResultDTO.CandidateSeafarerDTO dto = new SeafarerMatchResultDTO.CandidateSeafarerDTO();
        dto.setSeafarerId((String) data.get("seafarerId"));
        dto.setSeafarerName((String) data.get("seafarerName"));
        dto.setVesselName((String) data.get("vesselName"));
        dto.setApplyDutyId((String) data.get("applyDutyId"));
        dto.setApplyDutyName((String) data.get("applyDutyName"));
        dto.setCrtLevelId((String) data.get("crtLevelId"));
        dto.setCrtLevelName((String) data.get("crtLevelName"));
        dto.setOnDutyName((String) data.get("onDutyName"));
        dto.setStatusKey((String) data.get("statusKey"));
        dto.setOnBoardDate(parseDate(data.get("onBoardDate")));
        dto.setPlaceOfOriginProvValue((String) data.get("placeOfOriginProvValue"));
        dto.setDownBoardDate(parseDate(data.get("downBoardDate")));
        dto.setDownBoardDays(parseBigDecimal(data.get("downBoardDays")));
        dto.setEvaluateValue((String) data.get("evaluateValue"));
        dto.setEvaluateScore((Integer) data.get("evaluateScore"));
        dto.setHaveJiangCertificate((Integer) data.get("haveJiangCertificate"));
        dto.setHasExpiringCertificate((Integer) data.get("hasExpiringCertificate"));
        dto.setCertificates((String) data.get("certificates"));
        return dto;
    }

    /**
     * 转换为兼容原有接口的格式
     */
    private List<Map<String, Object>> convertToLegacyFormat(List<SeafarerMatchResultDTO> results, boolean singleMode) {
        return results.stream().map(result -> {
            Map<String, Object> legacyResult = convertOnBoardSeafarerToMap(result.getOnBoardSeafarer());

            if (singleMode) {
                // 单个匹配模式：shiftCrew是单个对象或空对象
                if (result.getBestCandidate() != null) {
                    legacyResult.put("shiftCrew", convertCandidateSeafarerToMap(result.getBestCandidate()));
                } else {
                    legacyResult.put("shiftCrew", new HashMap<>());
                }
            } else {
                // 匹配所有模式：shiftCrew是数组
                List<Map<String, Object>> candidateList = result.getCandidateSeafarers().stream()
                        .map(this::convertCandidateSeafarerToMap)
                        .collect(Collectors.toList());
                legacyResult.put("shiftCrew", candidateList);
            }

            // 添加福建省占比信息
            if (result.getFujianRatioInfo() != null) {
                legacyResult.put("fujianRatioInfo", convertFujianRatioInfoToMap(result.getFujianRatioInfo()));
            }

            return legacyResult;
        }).collect(Collectors.toList());
    }

    /**
     * 转换在船船员DTO为Map
     */
    private Map<String, Object> convertOnBoardSeafarerToMap(SeafarerMatchResultDTO.OnBoardSeafarerDTO dto) {
        Map<String, Object> map = new HashMap<>();
        map.put("seafarerId", dto.getSeafarerId());
        map.put("seafarerName", dto.getSeafarerName());
        map.put("vesselId", dto.getVesselId());
        map.put("vesselName", dto.getVesselName());
        map.put("vesselTypeFlag", dto.getVesselTypeFlag());
        map.put("vesselTypeName", dto.getVesselTypeName());
        map.put("applyDutyId", dto.getApplyDutyId());
        map.put("applyDutyName", dto.getApplyDutyName());
        map.put("crtLevelId", dto.getCrtLevelId());
        map.put("crtLevelName", dto.getCrtLevelName());
        map.put("onDutyName", dto.getOnDutyName());
        map.put("statusKey", dto.getStatusKey());
        map.put("onBoardDate", dto.getOnBoardDate());
        map.put("onBoardDays", dto.getOnBoardDays());
        map.put("placeOfOriginProvValue", dto.getPlaceOfOriginProvValue());
        map.put("haveJiangCertificate", dto.getHaveJiangCertificate());
        return map;
    }

    /**
     * 转换候选船员DTO为Map
     */
    private Map<String, Object> convertCandidateSeafarerToMap(SeafarerMatchResultDTO.CandidateSeafarerDTO dto) {
        Map<String, Object> map = new HashMap<>();
        map.put("seafarerId", dto.getSeafarerId());
        map.put("seafarerName", dto.getSeafarerName());
        map.put("vesselName", dto.getVesselName());
        map.put("applyDutyId", dto.getApplyDutyId());
        map.put("applyDutyName", dto.getApplyDutyName());
        map.put("crtLevelId", dto.getCrtLevelId());
        map.put("crtLevelName", dto.getCrtLevelName());
        map.put("onDutyName", dto.getOnDutyName());
        map.put("statusKey", dto.getStatusKey());
        map.put("onBoardDate", dto.getOnBoardDate());
        map.put("placeOfOriginProvValue", dto.getPlaceOfOriginProvValue());
        map.put("downBoardDate", dto.getDownBoardDate());
        map.put("downBoardDays", dto.getDownBoardDays());
        map.put("evaluateValue", dto.getEvaluateValue());
        map.put("evaluateScore", dto.getEvaluateScore());
        map.put("haveJiangCertificate", dto.getHaveJiangCertificate());
        map.put("hasExpiringCertificate", dto.getHasExpiringCertificate());
        map.put("certificates", dto.getCertificates());
        map.put("matchReason", dto.getMatchReason());
        return map;
    }

    /**
     * 处理分割参数（将逗号分隔的字符串转换为SQL IN子句格式）
     */
    private String processSplitParam(String param) {
        if (!StringUtils.hasText(param)) {
            return null;
        }

        return Arrays.stream(param.split(","))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .map(s -> "'" + s + "'")
                .collect(Collectors.joining(","));
    }

    /**
     * 解析日期
     */
    private LocalDate parseDate(Object dateObj) {
        if (dateObj == null) {
            return null;
        }

        if (dateObj instanceof LocalDate) {
            return (LocalDate) dateObj;
        }

        if (dateObj instanceof java.sql.Date) {
            return ((java.sql.Date) dateObj).toLocalDate();
        }

        if (dateObj instanceof String) {
            String dateStr = (String) dateObj;
            if (StringUtils.hasText(dateStr)) {
                try {
                    return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                } catch (Exception e) {
                    log.warn("日期解析失败: {}", dateStr, e);
                }
            }
        }

        return null;
    }

    /**
     * 解析BigDecimal
     */
    private BigDecimal parseBigDecimal(Object obj) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof BigDecimal) {
            return (BigDecimal) obj;
        }

        if (obj instanceof Number) {
            return BigDecimal.valueOf(((Number) obj).doubleValue());
        }

        if (obj instanceof String) {
            String str = (String) obj;
            if (StringUtils.hasText(str)) {
                try {
                    return new BigDecimal(str);
                } catch (NumberFormatException e) {
                    log.warn("数字解析失败: {}", str, e);
                }
            }
        }

        return null;
    }

    /**
     * 验证福建省占比是否满足要求
     */
    private boolean validateFujianRatio(String vesselId, String candidateSeafarerId,
                                       String replacedSeafarerId, SeafarerMatchRequestDTO request) {
        try {
            // 获取当前船舶统计信息
            Map<String, Object> stats = seafarerScheduleMapper.getVesselCrewStats(vesselId);
            if (stats == null) {
                log.warn("未找到船舶{}的船员统计信息", vesselId);
                return true; // 无法验证时放行
            }

            Integer totalCount = (Integer) stats.get("totalCount");
            Integer fujianCount = (Integer) stats.get("fujianCount");

            if (totalCount == null || totalCount == 0) {
                log.warn("船舶{}无在船船员", vesselId);
                return true; // 无船员时放行
            }

            // 批量获取籍贯信息
            List<String> seafarerIds = Arrays.asList(candidateSeafarerId, replacedSeafarerId);
            Map<String, Boolean> fujianMap = batchCheckFujianOrigin(seafarerIds);

            boolean replacedIsFujian = fujianMap.getOrDefault(replacedSeafarerId, false);
            boolean candidateIsFujian = fujianMap.getOrDefault(candidateSeafarerId, false);

            // 计算替换后的福建省船员数
            int newFujianCount = fujianCount;
            if (replacedIsFujian) newFujianCount--;
            if (candidateIsFujian) newFujianCount++;

            // 计算新的占比
            double newRatio = (double) newFujianCount / totalCount;

            // 获取占比要求范围
            double minRatio = request.getFujianRatioMin() != null ? request.getFujianRatioMin() : 0.4;
            double maxRatio = request.getFujianRatioMax() != null ? request.getFujianRatioMax() : 0.7;

            // 验证是否在要求范围内
            boolean isValid = newRatio >= minRatio && newRatio <= maxRatio;

            log.debug("船舶{} 福建省占比验证: 当前{}/{} ({:.1f}%), 替换后{}/{} ({:.1f}%), 要求范围[{:.1f}%-{:.1f}%], 验证结果: {}",
                    vesselId, fujianCount, totalCount, fujianCount * 100.0 / totalCount,
                    newFujianCount, totalCount, newRatio * 100, minRatio * 100, maxRatio * 100, isValid);

            return isValid;

        } catch (Exception e) {
            log.error("福建省占比验证失败, vesselId: {}, candidateId: {}, replacedId: {}",
                    vesselId, candidateSeafarerId, replacedSeafarerId, e);
            return true; // 异常时放行，避免影响基本匹配功能
        }
    }

    /**
     * 批量检查船员是否来自福建省
     */
    private Map<String, Boolean> batchCheckFujianOrigin(List<String> seafarerIds) {
        if (seafarerIds == null || seafarerIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            List<Map<String, Object>> results = seafarerScheduleMapper.batchGetSeafarerOrigin(seafarerIds);

            return results.stream().collect(Collectors.toMap(
                row -> (String) row.get("seafarerId"),
                row -> {
                    Integer provKey = (Integer) row.get("placeOfOriginProvKey");
                    return provKey != null && provKey == 350000;
                }
            ));
        } catch (Exception e) {
            log.error("批量获取船员籍贯信息失败, seafarerIds: {}", seafarerIds, e);
            return seafarerIds.stream().collect(Collectors.toMap(id -> id, id -> false));
        }
    }

    /**
     * 检查单个船员是否来自福建省
     */
    private boolean isFromFujian(String seafarerId) {
        try {
            Map<String, Object> origin = seafarerScheduleMapper.getSeafarerOrigin(seafarerId);
            if (origin != null) {
                Integer provKey = (Integer) origin.get("placeOfOriginProvKey");
                return provKey != null && provKey == 350000;
            }
        } catch (Exception e) {
            log.error("获取船员籍贯信息失败, seafarerId: {}", seafarerId, e);
        }
        return false;
    }

    /**
     * 计算福建省占比信息
     */
    private SeafarerMatchResultDTO.FujianRatioInfo calculateFujianRatioInfo(
            String vesselId, String candidateSeafarerId, String replacedSeafarerId, SeafarerMatchRequestDTO request) {

        SeafarerMatchResultDTO.FujianRatioInfo ratioInfo = new SeafarerMatchResultDTO.FujianRatioInfo();
        ratioInfo.setVesselId(vesselId);

        try {
            // 获取当前船舶统计信息
            Map<String, Object> stats = seafarerScheduleMapper.getVesselCrewStats(vesselId);
            if (stats == null) {
                return ratioInfo;
            }

            Integer totalCount = (Integer) stats.get("totalCount");
            Integer fujianCount = (Integer) stats.get("fujianCount");

            if (totalCount == null || totalCount == 0) {
                return ratioInfo;
            }

            // 设置当前信息
            ratioInfo.setCurrentTotalCount(totalCount);
            ratioInfo.setCurrentFujianCount(fujianCount);
            ratioInfo.setCurrentFujianRatio((double) fujianCount / totalCount);

            // 获取籍贯信息
            boolean replacedIsFujian = isFromFujian(replacedSeafarerId);
            boolean candidateIsFujian = candidateSeafarerId != null ? isFromFujian(candidateSeafarerId) : false;

            // 计算替换后信息
            int newFujianCount = fujianCount;
            if (replacedIsFujian) newFujianCount--;
            if (candidateIsFujian) newFujianCount++;

            ratioInfo.setAfterTotalCount(totalCount);
            ratioInfo.setAfterFujianCount(newFujianCount);
            ratioInfo.setAfterFujianRatio((double) newFujianCount / totalCount);
            ratioInfo.setRatioChange(ratioInfo.getAfterFujianRatio() - ratioInfo.getCurrentFujianRatio());

            // 设置要求范围和验证结果
            double minRatio = request.getFujianRatioMin() != null ? request.getFujianRatioMin() : 0.4;
            double maxRatio = request.getFujianRatioMax() != null ? request.getFujianRatioMax() : 0.7;
            ratioInfo.setRatioRequirement(String.format("%.0f%%-%.0f%%", minRatio * 100, maxRatio * 100));
            ratioInfo.setMeetRatioRequirement(ratioInfo.getAfterFujianRatio() >= minRatio &&
                                            ratioInfo.getAfterFujianRatio() <= maxRatio);

        } catch (Exception e) {
            log.error("计算福建省占比信息失败", e);
        }

        return ratioInfo;
    }

    /**
     * 转换福建省占比信息为Map
     */
    private Map<String, Object> convertFujianRatioInfoToMap(SeafarerMatchResultDTO.FujianRatioInfo ratioInfo) {
        Map<String, Object> map = new HashMap<>();
        map.put("vesselId", ratioInfo.getVesselId());
        map.put("currentTotalCount", ratioInfo.getCurrentTotalCount());
        map.put("currentFujianCount", ratioInfo.getCurrentFujianCount());
        map.put("currentFujianRatio", ratioInfo.getCurrentFujianRatio());
        map.put("afterTotalCount", ratioInfo.getAfterTotalCount());
        map.put("afterFujianCount", ratioInfo.getAfterFujianCount());
        map.put("afterFujianRatio", ratioInfo.getAfterFujianRatio());
        map.put("ratioChange", ratioInfo.getRatioChange());
        map.put("meetRatioRequirement", ratioInfo.getMeetRatioRequirement());
        map.put("ratioRequirement", ratioInfo.getRatioRequirement());
        return map;
    }
}
