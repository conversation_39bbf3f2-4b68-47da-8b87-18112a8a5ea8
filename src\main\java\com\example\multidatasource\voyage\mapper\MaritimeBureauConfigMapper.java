package com.example.multidatasource.voyage.mapper;

import com.example.multidatasource.voyage.entity.MaritimeBureauConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 海事局配置Mapper
 */
@Mapper
public interface MaritimeBureauConfigMapper {
    
    /**
     * 查询所有启用的海事局配置
     */
    List<MaritimeBureauConfig> selectEnabledConfigs();
    
    /**
     * 根据海事局名称查询配置
     */
    List<MaritimeBureauConfig> selectByBureauNames(@Param("bureauNames") List<String> bureauNames);
    
    /**
     * 查询所有海事局配置
     */
    List<MaritimeBureauConfig> selectAll();
    
    /**
     * 根据ID查询
     */
    MaritimeBureauConfig selectById(@Param("id") Long id);
    
    /**
     * 插入配置
     */
    int insert(MaritimeBureauConfig config);
    
    /**
     * 更新配置
     */
    int updateById(MaritimeBureauConfig config);
    
    /**
     * 删除配置
     */
    int deleteById(@Param("id") Long id);
}
