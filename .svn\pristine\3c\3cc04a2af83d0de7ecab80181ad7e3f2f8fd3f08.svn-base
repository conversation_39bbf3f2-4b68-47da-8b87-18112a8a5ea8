package com.example.multidatasource.voyage.service;

import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.voyage.dto.MaritimeCrawlRequestDTO;
import com.example.multidatasource.voyage.dto.MaritimeCrawlResultDTO;
import com.example.multidatasource.voyage.dto.MaritimeQueryDTO;
import com.example.multidatasource.voyage.entity.MaritimeBureauConfig;
import com.example.multidatasource.voyage.entity.MaritimeInfo;

import java.util.List;

/**
 * 海事信息服务接口
 */
public interface MaritimeService {
    
    /**
     * 爬取海事信息
     */
    MaritimeCrawlResultDTO crawlMaritimeInfo(MaritimeCrawlRequestDTO request);
    
    /**
     * 分页查询海事信息
     */
    PageResult<MaritimeInfo> queryMaritimeInfo(MaritimeQueryDTO query);
    
    /**
     * 根据ID查询海事信息详情
     */
    MaritimeInfo getMaritimeInfoById(Long id);
    
    /**
     * 查询所有海事局配置
     */
    List<MaritimeBureauConfig> getAllBureauConfigs();
    
    /**
     * 根据ID查询海事局配置
     */
    MaritimeBureauConfig getBureauConfigById(Long id);
    
    /**
     * 保存海事局配置
     */
    MaritimeBureauConfig saveBureauConfig(MaritimeBureauConfig config);
    
    /**
     * 更新海事局配置
     */
    MaritimeBureauConfig updateBureauConfig(Long id, MaritimeBureauConfig config);
    
    /**
     * 删除海事局配置
     */
    void deleteBureauConfig(Long id);
}
