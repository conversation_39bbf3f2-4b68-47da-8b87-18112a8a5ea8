package com.example.multidatasource.crew.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 船员信息实体类
 */
@Data
public class CrewSeafarerInfo {
    /**
     * 船员ID
     */
    private String seafarerId;

    /**
     * 公司Id
     */
    private String compId;

    /**
     * 船员姓名
     */
    private String seafarerName;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    /**
     * 入司时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date enterCompanyDate;

    /**
     * 证书职务ID
     */
    private String crtDutyId;

    /**
     * 证书职务名称
     */
    private String crtDutyName;

    /**
     * 证书登记ID
     */
    private String crtLevelId;

    /**
     * 证书等级名称
     */
    private String crtLevelName;

    /**
     * 国籍Key
     */
    private String nationalityKey;

    /**
     * 国籍
     */
    private String nationalityValue;

    /**
     * 出生省key
     */
    private Integer brithplaceProvKey;

    /**
     * 出生省
     */
    private String brithplaceProvValue;

    /**
     * 出生城市key
     */
    private Integer brithplaceCityKey;

    /**
     * 出生城市
     */
    private String brithplaceCityValue;

    /**
     * 民族
     */
    private String nation;

    /**
     * 政治面貌key
     */
    private String politicalKey;

    /**
     * 政治面貌
     */
    private String politicalValue;

    /**
     * 籍贯省ID
     */
    private Integer placeOfOriginProvKey;

    /**
     * 籍贯省
     */
    private String placeOfOriginProvValue;

    /**
     * 籍贯城市
     */
    private Integer placeOfOriginCityKey;

    /**
     * 籍贯城市
     */
    private String placeOfOriginCityValue;

    /**
     * 身份证号
     */
    private String seafarerIdNo;

    /**
     * 婚姻状况key
     */
    private String marriageStatusKey;

    /**
     * 婚姻状况
     */
    private String marriageStatusValue;

    /**
     * 健康状况
     */
    private String healthy;

    /**
     * 性别key
     */
    private String seafarerSexKey;

    /**
     * 性别
     */
    private String seafarerSexValue;

    /**
     * 腰围
     */
    private String waistline;

    /**
     * 身高
     */
    private String height;

    /**
     * 血型key
     */
    private String bloodKey;

    /**
     * 血型
     */
    private String bloodValue;

    /**
     * 体重
     */
    private String weight;

    /**
     * 工作服号
     */
    private String suitSize;

    /**
     * 鞋号
     */
    private String shoeSize;

    /**
     * 船员类型key
     */
    private String seafarerTypeKey;

    /**
     * 船员类型
     */
    private String seafarerTypeValue;

    /**
     * 用工形式ID
     */
    private String employmentId;

    /**
     * 用工形式名称
     */
    private String employmentName;

    /**
     * 航海经验key
     */
    private String maritimeExperienceKey;

    /**
     * 航海经验
     */
    private String maritimeExperienceValue;

    /**
     * 遣返地
     */
    private String repatriateCity;

    /**
     * 参加工作日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginWorkDate;

    /**
     * 其他电话
     */
    private String otherTel;

    /**
     * 邮箱
     */
    private String email;

    /**
     * qq或微信
     */
    private String qqWechat;

    /**
     * 邮政编号
     */
    private String postCode;

    /**
     * 通讯地址
     */
    private String currentAddress;

    /**
     * 英文地址
     */
    private String homeAddressEn;

    /**
     * 户口地址
     */
    private String censusRegister;

    /**
     * 联络备注
     */
    private String liaisonRemark;

    /**
     * 所属外派公司ID
     */
    private String ownedOutCompId;

    /**
     * 外派公司名称
     */
    private String ownedOutCompName;

    /**
     * 船员在船船舶Id
     */
    private String vesselId;

    /**
     * 船舶名称
     */
    private String vesselName;

    /**
     * 在船职务ID
     */
    private String onDutyId;

    /**
     * 在船职务
     */
    private String onDutyName;

    /**
     * 家庭住址
     */
    private String homeAddress;

    /**
     * 手机号
     */
    private String mobilePhone;

    /**
     * 备注
     */
    private String remarkId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 毕业学校
     */
    private String school;

    /**
     * 学校所在地
     */
    private String schoolPlace;

    /**
     * 学校性质key
     */
    private String schoolNatureKey;

    /**
     * 学校性质
     */
    private String schoolNatureValue;

    /**
     * 学历key
     */
    private String educationKey;

    /**
     * 学历
     */
    private String educationValue;

    /**
     * 学历证号
     */
    private String educationCertNo;

    /**
     * 入学日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date enrollmentDate;

    /**
     * 结业日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date graduationDate;

    /**
     * 专业
     */
    private String major;

    /**
     * 职称
     */
    private String jobTitle;

    /**
     * 第二学历标识
     */
    private String secondEducationKey;

    /**
     * 第二学历
     */
    private String secondEducationValue;

    /**
     * 第二学历证号
     */
    private String secondEducationCertNo;

    /**
     * 英语等级
     */
    private String englishLevel;

    /**
     * 船型ID
     */
    private String vesselKey;

    /**
     * 船型
     */
    private String vesselValue;

    /**
     * 月薪要求
     */
    private String salary;

    /**
     * 币种key
     */
    private String currencyKey;

    /**
     * 币种名称
     */
    private String currencyValue;

    /**
     * 要求载重吨key
     */
    private String tonKey;

    /**
     * 要求载重吨
     */
    private String tonValue;

    /**
     * 航区key
     */
    private String navigatingAreaKey;

    /**
     * 航区
     */
    private String navigatingAreaValue;

    /**
     * 计划上船时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planDate;

    /**
     * 上船要求_备注
     */
    private String otherRemark;

    /**
     * 船员状态key
     */
    private String statusKey;

    /**
     * 状态
     */
    private String statusValue;

    /**
     * 在船船员预警状态key
     */
    private String onStatusKey;

    /**
     * 在船船员预警状态
     */
    private String onStatusValue;

    /**
     * 最近服务资历id
     */
    private String serviceId;

    /**
     * 上船日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date onBoardDate;

    /**
     * 在船时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String onBoardTime;

    /**
     * 显示在船船员预警天数
     */
    private String alertDays;

    /**
     * 合同下船日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date downBoardDate;

    /**
     * 船员头像地址
     */
    private String seafarerHeadimgAddress;

    private String seafarerNameEn;

    /**
     * 船员号
     */
    private String seafarerNumber;

    /**
     * 申请职务ID
     */
    private String applyDutyId;

    /**
     * 申请职务姓名
     */
    private String applyDutyName;

    /**
     * 社保号
     */
    private String socialSecurityNo;

    /**
     * 签名地址
     */
    private String autographUrl;

    /**
     * 当前船舶ID
     */
    private String currentVesselId;

    /**
     * 当前船舶名称
     */
    private String currentVesselName;

    /**
     * 是否ISM培训
     */
    private String ismTraining;

    /**
     * 有医保社保
     */
    private String withMedicalSocial;

    /**
     * 安全背景
     */
    private String securityBackground;

    /**
     * 删除标识
     */
    private String deleteFlag;

    /**
     * 更新次数
     */
    private Long updateCount;

    /**
     * 作成年月日
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 作成用户ID
     */
    private String createUserId;

    /**
     * 更新年月日
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 更新用户ID
     */
    private String updateUserId;

    /**
     * 更新标识
     */
    private String updateFlag;

    /**
     * 当前版本号
     */
    private Long nowVersionNo;

    /**
     * 更新前版本号
     */
    private Long prevVersionNo;
}
