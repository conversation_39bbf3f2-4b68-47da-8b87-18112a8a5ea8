package com.example.multidatasource.crew.controller;

import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.common.config.DataSourceContextHolder;
import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.crew.dto.QualificationWithEvaluationDTO;
import com.example.multidatasource.crew.dto.SeafarerMatchRequestDTO;
import com.example.multidatasource.crew.dto.SeafarerMatchResultDTO;
import com.example.multidatasource.crew.entity.CrewInfo;
import com.example.multidatasource.crew.service.SeafarerMatchingService;
import com.example.multidatasource.crew.service.SeafarerScheduleService;
import com.example.multidatasource.model.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import java.util.List;
import java.util.Map;

/**
 * 船管系统Controller
 * 专门处理船员管理相关的数据库操作
 * 船员排期相关api
 */
@RestController
@RequestMapping("/crew/seafarer-schedule")
@Tag(name = "船管系统_船员排期", description = "船员排期相关API")
@DataSource(DataSourceContextHolder.DataSourceType.CREW)
public class SeafarerScheduleController {

    @Autowired
    private SeafarerScheduleService seafarerScheduleService;

    @Autowired
    private SeafarerMatchingService seafarerMatchingService;

    @GetMapping("/crew-list")
    @Operation(summary = "分页获取船员列表", description = "分页查询所有船员信息")
    public ApiResponse<PageResult<CrewInfo>> getCrewList(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageSize,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") int pageIndex,
            @Parameter(description = "船员id（可选）") @RequestParam(required = false) String seafarerId,
            @Parameter(description = "船员名（可选，支持模糊查询）") @RequestParam(required = false) String seafarerName,
            @Parameter(description = "申请职位id（可选）") @RequestParam(required = false) String applyDutyId
    ) {
        try {
            PageResult<CrewInfo> result = seafarerScheduleService.queryCrewsPage(pageSize, pageIndex, seafarerId, seafarerName, applyDutyId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("查询船员列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/certificate-info")
    @Operation(summary = "查询船员证书到期列表", description = "查询指定船员的证书信息，包含到期提醒（240天内到期）")
    public ApiResponse<List<Map<String, Object>>> getSeafarerCertificateInfo(
            @Parameter(description = "船员ID", required = true) @RequestParam String seafarerId
    ) {
        try {
            if (seafarerId == null || seafarerId.trim().isEmpty()) {
                return ApiResponse.error("船员ID不能为空");
            }
            List<Map<String, Object>> result = seafarerScheduleService.getSeafarerCertificateInfo(seafarerId.trim());
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("查询船员证书信息失败: " + e.getMessage());
        }
    }

    @GetMapping("/qualification-info")
    @Operation(summary = "查询船员服务资历与最新考评", description = "查询指定船员的服务资历信息，并附带其最新的年度或下船考评信息")
    public ApiResponse<QualificationWithEvaluationDTO> getSeafarerQualificationInfo(
            @Parameter(description = "船员ID", required = true) @RequestParam String seafarerId,
            @Parameter(description = "申请职务ID（可选）") @RequestParam(required = false) String applyDutyId
    ) {
        try {
            if (seafarerId == null || seafarerId.trim().isEmpty()) {
                return ApiResponse.error("船员ID不能为空");
            }
            QualificationWithEvaluationDTO result = seafarerScheduleService.getSeafarerQualificationInfo(
                    seafarerId.trim(),
                    applyDutyId != null ? applyDutyId.trim() : null
            );

            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("查询船员服务资历失败: " + e.getMessage());
        }
    }

    @GetMapping("/qualification-info-page")
    @Operation(summary = "分页查询船员服务资历与最新考评", description = "分页查询指定船员的服务资历信息，并附带其最新的年度或下船考评信息")
    public ApiResponse<QualificationWithEvaluationDTO> getSeafarerQualificationInfoPage(
            @Parameter(description = "船员ID", required = true) @RequestParam String seafarerId,
            @Parameter(description = "申请职务ID（可选）") @RequestParam(required = false) String applyDutyId,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "20") @RequestParam(defaultValue = "20") Integer size
    ) {
        try {
            if (seafarerId == null || seafarerId.trim().isEmpty()) {
                return ApiResponse.error("船员ID不能为空");
            }

            // 参数校验
            if (page < 1) {
                page = 1;
            }
            if (size < 1 || size > 1000) {
                size = 20;
            }

            QualificationWithEvaluationDTO result = seafarerScheduleService.getSeafarerQualificationInfoPage(
                    seafarerId.trim(),
                    applyDutyId != null ? applyDutyId.trim() : null,
                    page,
                    size
            );

            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("分页查询船员服务资历失败: " + e.getMessage());
        }
    }

    @PostMapping("/match-shift-change")
    @Operation(summary = "匹配上下船船员数据（匹配所有候选人）",
               description = "根据条件匹配在船即将到期船员和候选船员，每个在船船员返回所有符合条件的候选人列表。" +
                           "支持福建省占比验证：enableFujianRatioCheck=true时，确保替换后福建省船员占比在指定范围内（默认40%-70%）")
    public ApiResponse<List<Map<String, Object>>> matchSeafarerForShiftChange(
            @RequestBody SeafarerMatchRequestDTO request) {
        try {
            List<Map<String, Object>> result = seafarerScheduleService.matchSeafarerForShiftChange(request);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("匹配上下船船员数据失败: " + e.getMessage());
        }
    }

    @PostMapping("/match-single-shift-change")
    @Operation(summary = "匹配上下船船员数据（单个最优匹配）",
               description = "根据条件匹配在船即将到期船员和候选船员，每个在船船员只匹配一个最优候选人，避免重复分配。" +
                           "支持福建省占比验证：enableFujianRatioCheck=true时，确保替换后福建省船员占比在指定范围内（默认40%-70%）")
    public ApiResponse<List<Map<String, Object>>> matchSingleSeafarerForShiftChange(
            @RequestBody SeafarerMatchRequestDTO request) {
        try {
            List<Map<String, Object>> result = seafarerScheduleService.matchSingleSeafarerForShiftChange(request);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("匹配上下船船员数据失败: " + e.getMessage());
        }
    }

    @PostMapping("/match-all-candidates-v2")
    @Operation(summary = "匹配上下船船员数据（优化版 - 匹配所有候选人）",
               description = "使用优化后的匹配服务，返回结构化的DTO数据。" +
                           "每个候选人都会显示替换后对福建籍船员占比的影响信息，" +
                           "帮助用户了解选择不同候选人对船舶地域分布的影响。")
    public ApiResponse<List<SeafarerMatchResultDTO>> matchAllCandidatesV2(
            @RequestBody SeafarerMatchRequestDTO request) {
        try {
            List<SeafarerMatchResultDTO> result = seafarerMatchingService.matchAllCandidates(request);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("匹配上下船船员数据失败: " + e.getMessage());
        }
    }

    @PostMapping("/match-best-candidate-v2")
    @Operation(summary = "匹配上下船船员数据（优化版 - 单个最优匹配）",
               description = "使用优化后的匹配服务，返回结构化的DTO数据。" +
                           "最佳候选人会显示替换后对福建籍船员占比的影响信息，" +
                           "帮助用户了解选择该候选人对船舶地域分布的影响。")
    public ApiResponse<List<SeafarerMatchResultDTO>> matchBestCandidateV2(
            @RequestBody SeafarerMatchRequestDTO request) {
        try {
            List<SeafarerMatchResultDTO> result = seafarerMatchingService.matchBestCandidate(request);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("匹配上下船船员数据失败: " + e.getMessage());
        }
    }

}
