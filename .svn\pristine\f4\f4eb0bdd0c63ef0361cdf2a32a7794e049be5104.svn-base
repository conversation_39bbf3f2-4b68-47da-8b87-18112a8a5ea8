<component name="libraryTable">
  <library name="Maven: org.springframework.security:spring-security-core:5.7.11">
    <CLASSES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/org/springframework/security/spring-security-core/5.7.11/spring-security-core-5.7.11.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/org/springframework/security/spring-security-core/5.7.11/spring-security-core-5.7.11-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/org/springframework/security/spring-security-core/5.7.11/spring-security-core-5.7.11-sources.jar!/" />
    </SOURCES>
  </library>
</component>