-- MySQL 5.7 性能优化索引建议
-- 针对航次油耗查询的索引优化

-- 1. 主表优化索引
-- 为 vessel_id + fill_date + delete_flag + status_flag 创建复合索引
CREATE INDEX idx_vessel_fill_date_flags ON oil_voyage_consumption_info 
(vessel_id, fill_date DESC, delete_flag, status_flag);

-- 2. 为 delete_flag + status_flag + vessel_id + fill_date 创建复合索引（备选方案）
CREATE INDEX idx_flags_vessel_fill_date ON oil_voyage_consumption_info 
(delete_flag, status_flag, vessel_id, fill_date DESC);

-- 3. 明细表优化索引
-- 为 consum_id + delete_flag + oil_name_cn 创建复合索引
CREATE INDEX idx_consum_flag_oil_name ON oil_voyage_consumption_detail 
(consum_id, delete_flag, oil_name_cn);

-- 4. 为 delete_flag + oil_name_cn + consum_id 创建复合索引（备选方案）
CREATE INDEX idx_flag_oil_name_consum ON oil_voyage_consumption_detail 
(delete_flag, oil_name_cn, consum_id);

-- 5. 查看现有索引
SHOW INDEX FROM oil_voyage_consumption_info;
SHOW INDEX FROM oil_voyage_consumption_detail;

-- 6. 分析查询执行计划
-- 使用以下语句分析查询性能：
EXPLAIN SELECT 
    latest.vessel_id,
    latest.vessel_name,
    latest.fill_date,
    latest.consum_id,
    detail.oil_id,
    detail.oil_name_cn,
    detail.oil_name_en,
    detail.oil_mark,
    detail.oil_type_id,
    detail.fuel_type_id,
    detail.oil_unit_id,
    detail.oil_unit_name,
    detail.last_voyage_inventory
FROM (
    SELECT 
        t1.vessel_id,
        t1.vessel_name,
        t1.fill_date,
        t1.consum_id
    FROM oil_voyage_consumption_info t1
    INNER JOIN (
        SELECT 
            vessel_id,
            MAX(fill_date) AS max_fill_date
        FROM oil_voyage_consumption_info
        WHERE delete_flag = '0' AND status_flag != '99'
        GROUP BY vessel_id
    ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date
    WHERE t1.delete_flag = '0' AND t1.status_flag != '99'
) latest
INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id
WHERE detail.delete_flag = '0' 
AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油')
ORDER BY latest.vessel_id, detail.oil_name_cn;

-- 7. 性能监控查询
-- 查看慢查询日志设置
SHOW VARIABLES LIKE 'slow_query_log%';
SHOW VARIABLES LIKE 'long_query_time';

-- 8. 表统计信息更新（定期执行）
ANALYZE TABLE oil_voyage_consumption_info;
ANALYZE TABLE oil_voyage_consumption_detail;
