<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.multidatasource.voyage.mapper.VoyageQualificationMapper">

    <!-- 根据船舶ID和时间区间查询航次相关信息 -->
    <select id="getVoyageQualificationInfo" resultType="java.util.Map">
        SELECT
            <!-- 航次号列表（去重，逗号分隔） -->
            GROUP_CONCAT(DISTINCT 
                CASE 
                    WHEN v.voyage_no IS NOT NULL AND TRIM(v.voyage_no) != '' 
                    THEN TRIM(v.voyage_no) 
                    ELSE NULL 
                END 
                ORDER BY v.voyage_no SEPARATOR ','
            ) AS voyageNumbers,
            
            <!-- 装卸货品列表（去重，逗号分隔） -->
            GROUP_CONCAT(DISTINCT 
                CASE 
                    WHEN cg.cargo_name IS NOT NULL AND TRIM(cg.cargo_name) != '' 
                    THEN TRIM(cg.cargo_name) 
                    ELSE NULL 
                END 
                ORDER BY cg.cargo_name SEPARATOR ','
            ) AS cargos,
            
            <!-- 码头列表（去重，逗号分隔） -->
            GROUP_CONCAT(DISTINCT 
                CASE 
                    WHEN t.terminal_name IS NOT NULL AND TRIM(t.terminal_name) != '' 
                    THEN TRIM(t.terminal_name) 
                    ELSE NULL 
                END 
                ORDER BY t.terminal_name SEPARATOR ','
            ) AS terminals,
            
            <!-- 主机型号（去重，逗号分隔） -->
            GROUP_CONCAT(DISTINCT 
                CASE 
                    WHEN s.main_engine_model IS NOT NULL AND TRIM(s.main_engine_model) != '' 
                    THEN TRIM(s.main_engine_model) 
                    ELSE NULL 
                END 
                ORDER BY s.main_engine_model SEPARATOR ','
            ) AS mainEngineModel,
            
            <!-- 主机制造厂商（去重，逗号分隔） -->
            GROUP_CONCAT(DISTINCT 
                CASE 
                    WHEN s.main_engine_manufacturer IS NOT NULL AND TRIM(s.main_engine_manufacturer) != '' 
                    THEN TRIM(s.main_engine_manufacturer) 
                    ELSE NULL 
                END 
                ORDER BY s.main_engine_manufacturer SEPARATOR ','
            ) AS mainEngineManufacturer,
            
            <!-- 辅机型号（去重，逗号分隔） -->
            GROUP_CONCAT(DISTINCT 
                CASE 
                    WHEN s.auxiliary_engine_model IS NOT NULL AND TRIM(s.auxiliary_engine_model) != '' 
                    THEN TRIM(s.auxiliary_engine_model) 
                    ELSE NULL 
                END 
                ORDER BY s.auxiliary_engine_model SEPARATOR ','
            ) AS auxiliaryEngineModel,
            
            <!-- 辅机制造厂商（去重，逗号分隔） -->
            GROUP_CONCAT(DISTINCT 
                CASE 
                    WHEN s.auxiliary_engine_manufacturer IS NOT NULL AND TRIM(s.auxiliary_engine_manufacturer) != '' 
                    THEN TRIM(s.auxiliary_engine_manufacturer) 
                    ELSE NULL 
                END 
                ORDER BY s.auxiliary_engine_manufacturer SEPARATOR ','
            ) AS auxiliaryEngineManufacturer
            
        FROM t_vy_stat_voyage sv
        
        <!-- 关联航次表获取航次号 -->
        LEFT JOIN t_vy_voyage v ON sv.voyage_id = v.id AND v.is_available = 0
        
        <!-- 关联航线步骤表 -->
        LEFT JOIN t_vy_stat_line sl ON sv.id = sl.stat_voyage_id AND sl.is_available = 0
        
        <!-- 关联货物表 -->
        LEFT JOIN t_vy_stat_cargo sc ON sl.id = sc.stat_line_id AND sc.is_available = 0
        
        <!-- 关联货品基础表获取货品名称 -->
        LEFT JOIN t_cg_cargo cg ON sc.goods_id = cg.id
        
        <!-- 关联码头基础表获取码头名称 -->
        LEFT JOIN t_pt_terminal t ON sl.wharf_id = t.id
        
        <!-- 关联船舶基础表获取主机/辅机信息（通过MMSI码关联） -->
        LEFT JOIN t_sh_ship s ON sv.ship_id = s.id AND s.is_delete != 1 AND s.mmsi = #{mmsiCode}

        WHERE sv.is_available = 0
        AND sv.voyage_over_date IS NOT NULL
        <!-- 通过MMSI码过滤船舶，而不是直接使用ship_id -->
        AND EXISTS (
            SELECT 1 FROM t_sh_ship ship
            WHERE ship.id = sv.ship_id
            AND ship.mmsi = #{mmsiCode}
            AND ship.is_delete != 1
        )
        
        <!-- 时间区间过滤：航次完结时间在船员上船和下船时间之间 -->
        AND DATE(sv.voyage_over_date) >= DATE(#{onBoardDate})
        <if test="downBoardDate != null and downBoardDate != ''">
            AND DATE(sv.voyage_over_date) &lt;= DATE(#{downBoardDate})
        </if>
        <if test="downBoardDate == null or downBoardDate == ''">
            AND DATE(sv.voyage_over_date) &lt;= CURDATE()
        </if>
    </select>

</mapper>
