2025-08-13 15:26:56 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication v1.0.0 using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 4092 (D:\augmentSpace\target\multi-datasource-api-1.0.0.jar started by <PERSON><PERSON><PERSON><PERSON> in D:\augmentSpace)
2025-08-13 15:26:56 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-13 15:26:56 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-08-13 15:26:58 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/auth/mapper/UserMapper.class]
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/crew/mapper/CrewMapper.class]
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/crew/mapper/OilVoyageConsumptionMapper.class]
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/crew/mapper/SeafarerBaseInfoMapper.class]
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/crew/mapper/SeafarerScheduleMapper.class]
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/voyage/mapper/ShipEngineMapper.class]
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/voyage/mapper/VoyageMapper.class]
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: URL [jar:file:/D:/augmentSpace/target/multi-datasource-api-1.0.0.jar!/BOOT-INF/classes!/com/example/multidatasource/voyage/mapper/VoyageQualificationMapper.class]
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerBaseInfoMapper' and 'com.example.multidatasource.crew.mapper.SeafarerBaseInfoMapper' mapperInterface
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerScheduleMapper' and 'com.example.multidatasource.crew.mapper.SeafarerScheduleMapper' mapperInterface
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'shipEngineMapper' and 'com.example.multidatasource.voyage.mapper.ShipEngineMapper' mapperInterface
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-08-13 15:26:58 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageQualificationMapper' and 'com.example.multidatasource.voyage.mapper.VoyageQualificationMapper' mapperInterface
2025-08-13 15:27:01 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-08-13 15:27:01 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-13 15:27:01 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-13 15:27:01 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-08-13 15:27:01 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4914 ms
2025-08-13 15:27:01 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 15:27:02 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-08-13 15:27:02 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/auth/UserMapper.xml]'
2025-08-13 15:27:02 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/crew/CrewMapper.xml]'
2025-08-13 15:27:02 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/crew/OilVoyageConsumptionMapper.xml]'
2025-08-13 15:27:02 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/crew/SeafarerBaseInfoMapper.xml]'
2025-08-13 15:27:03 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/crew/SeafarerScheduleMapper.xml]'
2025-08-13 15:27:03 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/voyage/ShipEngineMapper.xml]'
2025-08-13 15:27:03 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/voyage/VoyageMapper.xml]'
2025-08-13 15:27:03 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'class path resource [mapper/voyage/VoyageQualificationMapper.xml]'
2025-08-13 15:27:04 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 63507bdf-558f-4792-b691-439ba4ae43ab

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-13 15:27:04 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@478db956, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6ca18a14, org.springframework.security.web.context.SecurityContextPersistenceFilter@7ac296f6, org.springframework.security.web.header.HeaderWriterFilter@2f01783a, org.springframework.web.filter.CorsFilter@c667f46, org.springframework.security.web.authentication.logout.LogoutFilter@17d919b6, com.example.multidatasource.config.JwtAuthenticationFilter@503d687a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4b86805d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4149c063, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@51bd8b5c, org.springframework.security.web.session.SessionManagementFilter@41488b16, org.springframework.security.web.access.ExceptionTranslationFilter@2bdd8394, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7905a0b8]
2025-08-13 15:27:07 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 54321 (http) with context path '/multi/source/api'
2025-08-13 15:27:07 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 11.48 seconds (JVM running for 12.165)
