# 船员经验统计功能 - 项目结构说明

## 项目概览

本项目为多数据源Spring Boot应用，主要功能是船员匹配和经验统计。

## 核心模块结构

```
src/main/java/com/example/multidatasource/
├── crew/                           # 船员模块（主要功能）
│   ├── controller/                 # 控制器层
│   │   └── SeafarerController.java # 船员匹配接口
│   ├── service/                    # 服务层
│   │   ├── SeafarerMatchingService.java        # 匹配服务接口
│   │   └── impl/
│   │       └── SeafarerMatchingServiceImpl.java # 匹配服务实现（核心逻辑）
│   ├── mapper/                     # 数据访问层
│   │   └── SeafarerScheduleMapper.java         # 船员数据查询接口
│   ├── dto/                        # 数据传输对象
│   │   ├── SeafarerMatchRequestDTO.java        # 请求参数DTO
│   │   └── SeafarerMatchResultDTO.java         # 返回结果DTO（包含新增统计字段）
│   └── util/                       # 工具类
│       └── SeafarerStatsDebugUtil.java         # 统计功能调试工具
├── voyage/                         # 航次模块（跨数据源查询）
│   ├── controller/
│   ├── service/
│   └── mapper/
└── config/                         # 配置类
    ├── DataSourceConfig.java       # 多数据源配置
    └── DataSourceContextHolder.java # 数据源切换工具
```

## 资源文件结构

```
src/main/resources/
├── mapper/
│   ├── crew/
│   │   └── SeafarerScheduleMapper.xml          # 船员查询SQL（包含统计逻辑）
│   └── voyage/
│       └── VoyageMapper.xml                    # 航次查询SQL
├── application.yml                             # 应用配置
└── application-dev.yml                         # 开发环境配置
```

## 文档结构

```
docs/
├── 船员经验统计功能说明.md                      # 功能详细说明
├── 船员经验统计功能测试指南.md                   # 测试指南
└── 项目结构说明.md                             # 本文档
```

## 核心功能流程

### 1. 接口调用流程
```
客户端请求 
→ SeafarerController 
→ SeafarerMatchingServiceImpl 
→ SeafarerScheduleMapper 
→ 数据库查询 
→ 结果处理 
→ 返回响应
```

### 2. 统计计算流程
```
个人统计（SQL层）:
getAvailableCrews → duty_stats子查询 → vessel_type_stats子查询 → 返回年数

组合统计（应用层）:
获取候选人职务 → 映射搭档职务 → getPartnerStats查询 → 计算组合统计 → 设置搭档信息
```

## 关键文件说明

### 1. SeafarerMatchingServiceImpl.java
**作用**: 船员匹配服务的核心实现类
**关键方法**:
- `matchSingleShiftChange()`: 单个换班匹配
- `matchAllCandidatesV2()`: 全部候选人匹配
- `convertToCandidateSeafarer()`: 候选人数据转换（包含统计计算）
- `getMatchingData()`: 获取匹配数据

**新增功能**:
- 职务搭档关系映射（DUTY_PARTNER_MAP）
- 搭档统计查询和计算
- 个人统计字段设置

### 2. SeafarerScheduleMapper.xml
**作用**: 船员数据查询SQL定义
**关键查询**:
- `getOnBoardCrews`: 查询在船即将到期船员
- `getAvailableCrews`: 查询候选船员（包含个人统计）
- `getPartnerStats`: 查询搭档统计信息（新增）

**统计逻辑**:
- 个人统计通过LEFT JOIN子查询实现
- 时间计算使用DATEDIFF函数
- 年数转换公式：天数 / 360.0，保留2位小数

### 3. SeafarerMatchResultDTO.java
**作用**: 匹配结果数据传输对象
**新增字段**:
- 个人统计：`companyYears`, `dutyYears`, `vesselTypeYears`
- 组合统计：`groupCompanyYears`, `groupDutyYears`, `groupVesselTypeYears`
- 搭档信息：`partnerDutyName`, `partnerSeafarerName`

## 数据库表关系

### 主要表结构
```
crew_seafarer_info                              # 船员基础信息表
├── seafarer_id (主键)
├── seafarer_name                               # 船员姓名
├── apply_duty_name                             # 申请职务名称
├── enter_company_date                          # 入司日期
└── status_key                                  # 状态（ONBOARD/其他）

crew_seafarer_service_qualification_info        # 船员服务履历表
├── seafarer_id                                 # 船员ID（外键）
├── vessel_id                                   # 船舶ID
├── duty_id                                     # 职务ID
├── on_board_date                               # 上船日期
└── down_board_date                             # 下船日期

common_vessel                                   # 船舶信息表（voyage库）
├── vessel_id                                   # 船舶ID
├── vessel_name                                 # 船舶名称
└── vessel_type_flag                            # 船舶类型标识
```

### 跨数据源查询
- crew库：船员相关数据
- voyage库：船舶相关数据
- 通过vessel_id关联查询

## 配置说明

### 数据源配置
```yaml
spring:
  datasource:
    crew:                                       # 船员数据源
      url: ********************************
      username: ${DB_USERNAME}
      password: ${DB_PASSWORD}
    voyage:                                     # 航次数据源
      url: **********************************
      username: ${DB_USERNAME}
      password: ${DB_PASSWORD}
```

### 数据源切换
使用`@DataSource`注解进行数据源切换：
```java
@DataSource(DataSourceContextHolder.DataSourceType.CREW)
public class SeafarerMatchingServiceImpl {
    // 默认使用crew数据源
}
```

## 调试和排查

### 1. 日志配置
```yaml
logging:
  level:
    com.example.multidatasource.crew: DEBUG     # 开启详细日志
```

### 2. 关键日志位置
- 搭档查询异常：`获取搭档统计信息失败`
- SQL执行日志：MyBatis SQL日志
- 数据源切换日志：DataSource切换日志

### 3. 调试工具
使用`SeafarerStatsDebugUtil`进行：
- 个人统计验证
- 组合统计验证
- 搭档信息调试
- SQL查询调试
- 数据完整性检查

### 4. 常见问题排查
1. **统计字段为0**: 检查基础数据和SQL查询
2. **搭档信息异常**: 检查职务映射和在船船员数据
3. **跨数据源查询失败**: 检查数据源配置和连接
4. **性能问题**: 检查SQL执行时间和索引

## 部署注意事项

1. **数据库连接**: 确保两个数据源都能正常连接
2. **环境变量**: 配置正确的数据库用户名密码
3. **索引优化**: 为统计查询相关字段添加索引
4. **监控配置**: 配置接口响应时间和SQL执行时间监控

## 版本控制

- **SVN管理**: 代码通过SVN进行版本控制
- **回滚方案**: 如有问题可回滚到功能开发前版本
- **分支策略**: 建议在独立分支开发和测试
