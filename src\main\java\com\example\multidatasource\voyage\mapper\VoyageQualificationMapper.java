package com.example.multidatasource.voyage.mapper;

import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.common.config.DataSourceContextHolder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 航次资历信息Mapper接口
 *
 * 用于查询船员服务资历相关的航次信息，包括：
 * - 航次号
 * - 装卸货品
 * - 码头信息
 * - 主机/辅机型号和制造厂商
 *
 * 数据源：使用voyage数据源进行查询
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Mapper
@DataSource(DataSourceContextHolder.DataSourceType.VOYAGE)
public interface VoyageQualificationMapper {

    /**
     * 根据船舶MMSI码和时间区间查询航次相关信息
     *
     * 功能说明：
     * 1. 根据船员的上船日期和下船日期，查找该时间段内的所有航次
     * 2. 通过MMSI码关联crew和voyage数据源中的船舶信息
     * 3. 聚合航次号、装卸货品、码头、主机/辅机信息
     * 4. 多个值用英文逗号分隔，去重处理
     *
     * @param mmsiCode 船舶MMSI码（用于跨数据源船舶关联的唯一标识）
     * @param onBoardDate 上船日期（格式：2024-10-05 00:00:00）
     * @param downBoardDate 下船日期（格式：2024-10-05 00:00:00）
     * @return 航次相关信息Map，包含以下字段：
     *         - voyageNumbers: 航次号列表（逗号分隔）
     *         - cargos: 装卸货品列表（逗号分隔）
     *         - terminals: 码头列表（逗号分隔）
     *         - mainEngineModel: 主机型号（通常单个值）
     *         - mainEngineManufacturer: 主机制造厂商（通常单个值）
     *         - auxiliaryEngineModel: 辅机型号（通常单个值）
     *         - auxiliaryEngineManufacturer: 辅机制造厂商（通常单个值）
     */
    Map<String, Object> getVoyageQualificationInfo(
            @Param("mmsiCode") String mmsiCode,
            @Param("onBoardDate") String onBoardDate,
            @Param("downBoardDate") String downBoardDate
    );
}
