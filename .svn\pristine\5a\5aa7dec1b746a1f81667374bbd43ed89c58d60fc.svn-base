package com.example.multidatasource.auth.service.impl;

import com.example.multidatasource.auth.entity.User;
import com.example.multidatasource.auth.mapper.UserMapper;
import com.example.multidatasource.auth.service.UserService;
import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.common.config.DataSourceContextHolder;
import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.common.dto.RegisterRequest;
import com.example.multidatasource.common.dto.UserQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务实现
 * 使用voyage数据源存储用户信息
 */
@Slf4j
@Service
@DataSource(DataSourceContextHolder.DataSourceType.VOYAGE)
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    @Lazy
    private PasswordEncoder passwordEncoder;

    @Override
    public User getUserByUsername(String username) {
        return userMapper.selectUserByUsername(username);
    }

    @Override
    public User getUserById(Long id) {
        return userMapper.selectUserById(id);
    }

    @Override
    public boolean createUser(RegisterRequest registerRequest) {
        // 检查用户名是否已存在
        if (isUsernameExists(registerRequest.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (isEmailExists(registerRequest.getEmail())) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 检查密码是否一致
        if (!registerRequest.isPasswordMatch()) {
            throw new RuntimeException("两次输入的密码不一致");
        }
        
        User user = new User();
        user.setUsername(registerRequest.getUsername());
        user.setPassword(encodePassword(registerRequest.getPassword()));
        user.setEmail(registerRequest.getEmail());
        user.setPhone(registerRequest.getPhone());
        user.setRealName(registerRequest.getRealName());
        user.setRole(registerRequest.getRole());
        user.setStatus("active");
        user.setCreatedTime(LocalDateTime.now());
        user.setUpdatedTime(LocalDateTime.now());
        
        int result = userMapper.insertUser(user);
        return result > 0;
    }

    @Override
    public boolean updateUser(User user) {
        user.setUpdatedTime(LocalDateTime.now());
        int result = userMapper.updateUser(user);
        return result > 0;
    }

    @Override
    public boolean deleteUser(Long id) {
        int result = userMapper.deleteUserById(id);
        return result > 0;
    }

    @Override
    public boolean updateUserStatus(Long id, String status) {
        int result = userMapper.updateUserStatus(id, status);
        return result > 0;
    }

    @Override
    public boolean updateUserPassword(Long id, String oldPassword, String newPassword) {
        User user = getUserById(id);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        if (!validatePassword(oldPassword, user.getPassword())) {
            throw new RuntimeException("原密码不正确");
        }
        
        String encodedNewPassword = encodePassword(newPassword);
        int result = userMapper.updateUserPassword(id, encodedNewPassword);
        return result > 0;
    }

    @Override
    public boolean resetUserPassword(Long id, String newPassword) {
        String encodedNewPassword = encodePassword(newPassword);
        int result = userMapper.updateUserPassword(id, encodedNewPassword);
        return result > 0;
    }

    @Override
    public boolean updateLastLoginInfo(Long id, String ip) {
        int result = userMapper.updateLastLoginInfo(id, LocalDateTime.now(), ip);
        return result > 0;
    }

    @Override
    public PageResult<User> getUserList(Integer page, Integer size) {
        int offset = (page - 1) * size;
        List<User> records = userMapper.selectUserList(offset, size);
        int total = userMapper.countUsers();
        
        // 清除密码信息
        records.forEach(user -> user.setPassword(null));
        
        return PageResult.of(records, (long) total, page, size);
    }

    @Override
    public PageResult<User> searchUsers(UserQueryDTO queryDTO) {
        List<User> records = userMapper.selectUsersByConditions(
            queryDTO.getUsername(),
            queryDTO.getEmail(),
            queryDTO.getRealName(),
            queryDTO.getRole(),
            queryDTO.getStatus(),
            queryDTO.getOffset(),
            queryDTO.getLimit()
        );
        
        int total = userMapper.countUsersByConditions(
            queryDTO.getUsername(),
            queryDTO.getEmail(),
            queryDTO.getRealName(),
            queryDTO.getRole(),
            queryDTO.getStatus()
        );
        
        // 清除密码信息
        records.forEach(user -> user.setPassword(null));
        
        return PageResult.of(records, (long) total, queryDTO.getPage(), queryDTO.getSize());
    }

    @Override
    public List<User> getUsersByRole(String role) {
        List<User> users = userMapper.selectUsersByRole(role);
        // 清除密码信息
        users.forEach(user -> user.setPassword(null));
        return users;
    }

    @Override
    public List<User> getUsersByStatus(String status) {
        List<User> users = userMapper.selectUsersByStatus(status);
        // 清除密码信息
        users.forEach(user -> user.setPassword(null));
        return users;
    }

    @Override
    public boolean isUsernameExists(String username) {
        return userMapper.checkUsernameExists(username) > 0;
    }

    @Override
    public boolean isEmailExists(String email) {
        return userMapper.checkEmailExists(email) > 0;
    }

    @Override
    public boolean validatePassword(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    @Override
    public String encodePassword(String rawPassword) {
        return passwordEncoder.encode(rawPassword);
    }

    @Override
    public int getUserCount() {
        return userMapper.countUsers();
    }
}
