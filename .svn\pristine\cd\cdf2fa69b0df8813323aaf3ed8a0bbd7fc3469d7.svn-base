<component name="libraryTable">
  <library name="Maven: io.jsonwebtoken:jjwt-impl:0.11.5">
    <CLASSES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/io/jsonwebtoken/jjwt-impl/0.11.5/jjwt-impl-0.11.5-sources.jar!/" />
    </SOURCES>
  </library>
</component>