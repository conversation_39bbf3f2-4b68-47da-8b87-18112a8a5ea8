package com.example.multidatasource.crew.mapper;

import com.example.multidatasource.crew.entity.CrewInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 船员信息Mapper接口
 */
@Mapper
public interface SeafarerScheduleMapper {

    /**
     * 查询所有船员信息（分页）
     */
    List<CrewInfo> selectCrewList(
            @Param("offset") int offset,
            @Param("limit") int limit,
            @Param("seafarerId") String seafarerId,
            @Param("seafarerName") String seafarerName,
            @Param("applyDutyId") String applyDutyId
    );

    /**
     * 统计船员总数
     */
    int countCrew(
            @Param("seafarerId") String seafarerId,
            @Param("seafarerName") String seafarerName,
            @Param("applyDutyId") String applyDutyId
    );

    /**
     * 查询船员证书到期列表
     * @param seafarerId 船员ID
     * @return 证书信息列表
     */
    List<Map<String, Object>> getSeafarerCertificateInfo(@Param("seafarerId") String seafarerId);

    /**
     * 查询船员服务资历
     * @param seafarerId 船员ID
     * @param applyDutyId 申请职务ID（可选）
     * @return 服务资历列表
     */
    List<Map<String, Object>> getSeafarerQualificationInfo(
            @Param("seafarerId") String seafarerId,
            @Param("applyDutyId") String applyDutyId
    );
}
