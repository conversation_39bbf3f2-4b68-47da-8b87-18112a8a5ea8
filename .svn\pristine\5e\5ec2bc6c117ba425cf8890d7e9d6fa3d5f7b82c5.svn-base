-- 用户管理表结构 - voyage数据库
-- 注意：此表将创建在voyage_management_dev数据库中，作为用户认证的数据源

-- 使用voyage数据库
USE voyage_management_dev;

-- 创建用户表（使用TIMESTAMP解决MySQL兼容性问题）
CREATE TABLE IF NOT EXISTS t_ds_users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密后）',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-激活，inactive-未激活，locked-锁定',
    role VARCHAR(20) DEFAULT 'user' COMMENT '角色：admin-管理员，user-普通用户',
    last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_role (role),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 插入默认管理员用户
-- 密码为：admin123（BCrypt加密后的值）
INSERT INTO t_ds_users (username, password, email, real_name, role, status) VALUES 
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKVjzieMwkOBEDQJIrPDVDDn9DGa', '<EMAIL>', '系统管理员', 'admin', 'active')
ON DUPLICATE KEY UPDATE 
    password = VALUES(password),
    email = VALUES(email),
    real_name = VALUES(real_name),
    role = VALUES(role),
    status = VALUES(status);

-- 插入测试用户
-- 密码为：user123（BCrypt加密后的值）
INSERT INTO t_ds_users (username, password, email, real_name, role, status) VALUES 
('testuser', '$2a$10$8.UnVuG9HHgffUDAlk8qfOuVGkqRzgVymGe07xd00DMxs.AQubh4a', '<EMAIL>', '测试用户', 'user', 'active')
ON DUPLICATE KEY UPDATE 
    password = VALUES(password),
    email = VALUES(email),
    real_name = VALUES(real_name),
    role = VALUES(role),
    status = VALUES(status);

-- 查看创建的用户
SELECT id, username, email, real_name, role, status, created_time FROM t_ds_users;

-- 用户表字段说明：
-- id: 主键，自增
-- username: 用户名，唯一，用于登录
-- password: 密码，BCrypt加密存储
-- email: 邮箱，唯一
-- phone: 手机号，可选
-- real_name: 真实姓名
-- status: 用户状态
--   - active: 正常激活状态
--   - inactive: 未激活状态
--   - locked: 账户被锁定
-- role: 用户角色
--   - admin: 管理员，拥有所有权限
--   - user: 普通用户，基本权限
-- last_login_time: 最后登录时间
-- last_login_ip: 最后登录IP地址
-- created_time: 账户创建时间
-- updated_time: 最后更新时间

-- 默认账户信息：
-- 管理员账户：admin / Admin.xt_602309
-- 测试账户：testuser / Admin.xt_602309

-- 注意事项：
-- 1. 使用TIMESTAMP替代DATETIME解决MySQL版本兼容性问题
-- 2. 表名改为t_ds_users以符合命名规范
-- 3. 数据存储在voyage_management_dev数据库中
-- 4. 认证服务已配置使用VOYAGE数据源
