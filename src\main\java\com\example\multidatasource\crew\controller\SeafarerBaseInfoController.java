package com.example.multidatasource.crew.controller;

import com.example.multidatasource.model.ApiResponse;
import com.example.multidatasource.crew.dto.SeafarerBaseInfoDTO;
import com.example.multidatasource.crew.service.SeafarerBaseInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 船员基础信息控制器
 */
@Slf4j
@RestController
@RequestMapping("/crew/seafarer-base-info")
@Tag(name = "船员基础信息管理", description = "船员基础信息相关接口，提供下拉选项数据")
public class SeafarerBaseInfoController {

    @Autowired
    private SeafarerBaseInfoService seafarerBaseInfoService;

    @GetMapping("/query")
    @Operation(summary = "查询船员基础信息", 
               description = "获取船舶列表、职务列表、证书等级列表，用于前端下拉选项")
    public ApiResponse<SeafarerBaseInfoDTO> querySeafarerBaseInfo() {
        try {
            SeafarerBaseInfoDTO result = seafarerBaseInfoService.querySeafarerBaseInfo();
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("查询船员基础信息失败", e);
            return ApiResponse.error("查询船员基础信息失败: " + e.getMessage());
        }
    }
}
