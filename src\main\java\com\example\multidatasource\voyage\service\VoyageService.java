package com.example.multidatasource.voyage.service;

import com.example.multidatasource.common.dto.CargoCompanyStatsQueryDTO;
import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.common.dto.VoyageQueryDTO;
import com.example.multidatasource.voyage.entity.CargoCompanyStats;
import com.example.multidatasource.voyage.entity.VoyageInfo;

import java.util.Map;

import java.util.List;

/**
 * 航次管理服务接口
 */
public interface VoyageService {

    /**
     * 分页查询航次列表
     */
    PageResult<VoyageInfo> getVoyageList(Integer page, Integer size);

    /**
     * 根据ID查询航次详情
     */
    VoyageInfo getVoyageById(String id);

    /**
     * 创建航次
     */
    boolean createVoyage(VoyageInfo voyageInfo);

    /**
     * 更新航次信息
     */
    boolean updateVoyage(VoyageInfo voyageInfo);

    /**
     * 删除航次
     */
    boolean deleteVoyage(Long id);

    /**
     * 根据状态查询航次列表
     */
    List<VoyageInfo> getVoyageByStatus(String status);

    /**
     * 根据船舶名称查询航次列表
     */
    List<VoyageInfo> getVoyageByShipName(String shipName);

    /**
     * 更新航次状态
     */
    boolean updateVoyageStatus(Long id, String status);

    /**
     * 多条件查询航次（分页）
     */
    PageResult<VoyageInfo> searchVoyage(VoyageQueryDTO queryDTO);

    /**
     * 获取所有船舶名称列表
     */
    List<String> getAllShipNames();

    /**
     * 获取所有港口列表
     */
    List<String> getAllPorts();

    /**
     * 统计航次总数
     */
    int getVoyageCount();

    /**
     * 查询货主关联信息统计（分页）
     */
    PageResult<CargoCompanyStats> getCargoCompanyStats(CargoCompanyStatsQueryDTO queryDTO);

    /**
     * 通过船舶名称查询船舶详细信息
     * @param vesselName 船舶名称
     * @return 船舶详细信息
     */
    Map<String, Object> getVesselDetailInfo(String vesselName);
}
