package com.example.multidatasource.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * OpenAPI/Swagger配置
 */
@Configuration
public class OpenApiConfig {

    private static final String SECURITY_SCHEME_NAME = "bearerAuth";

    @Value("${swagger.server.url:http://localhost:54321/multi/source/api}")
    private String serverUrl;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("多数据源管理API系统")
                        .description("基于Spring Boot的多MySQL数据源管理系统，支持船管、航次、货物、财务等多个业务模块的独立数据源管理。" +
                                   "\n\n## 🔐 身份认证说明" +
                                   "\n本系统使用JWT令牌进行身份认证：" +
                                   "\n1. 通过 `/auth/login` 接口登录获取JWT令牌" +
                                   "\n2. 点击右上角 'Authorize' 按钮，输入 `Bearer <token>`" +
                                   "\n3. 默认账户：admin/admin123（管理员）、testuser/user123（普通用户）" +
                                   "\n\n## 📋 API模块说明" +
                                   "\n- **身份认证**：用户登录、注册、权限管理" +
                                   "\n- **用户管理**：用户信息管理、角色权限控制" +
                                   "\n- **船管系统**：船员信息管理" +
                                   "\n- **航次管理**：航次计划和状态管理" +
                                   "\n- **通用SQL**：动态SQL执行" +
                                   "\n- **系统管理**：健康检查、数据源状态")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("开发团队")
                                .email("<EMAIL>")
                                .url("https://example.com"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")))

                // 添加JWT安全配置
                .addSecurityItem(new SecurityRequirement().addList(SECURITY_SCHEME_NAME))
                .components(new Components()
                        .addSecuritySchemes(SECURITY_SCHEME_NAME,
                                new SecurityScheme()
                                        .name(SECURITY_SCHEME_NAME)
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("请输入JWT令牌，格式：Bearer <token>")))

                .servers(Arrays.asList(
                        new Server()
                                .url(serverUrl)
                                .description("环境")
                ));
    }
}
