# 服务资历扩展功能测试指南

## 功能概述

对`SeafarerScheduleController`中的`/qualification-info`接口进行扩展，新增8个航次相关字段，提供更丰富的船员服务资历信息。

## 扩展字段说明

### 新增字段（8个）
| 字段名 | 中文名称 | 数据来源 | 示例值 | 说明 |
|--------|----------|----------|--------|------|
| `seafarerName` | 船员姓名 | crew数据源 | "张三" | 来自crew_seafarer_info表 |
| `voyageNumbers` | 航次号 | voyage数据源 | "V.2501,V.2502,V.2503" | 多个用逗号分隔，去重 |
| `cargos` | 装卸货品 | voyage数据源 | "苯酚,甲醇,丙烯" | 多个用逗号分隔，去重 |
| `terminals` | 码头 | voyage数据源 | "福炼,揭阳,惠州" | 多个用逗号分隔，去重 |
| `mainEngineModel` | 主机型号 | voyage数据源 | "MAN B&W 6S50MC-C" | 多个用逗号分隔，去重 |
| `mainEngineManufacturer` | 主机制造厂商 | voyage数据源 | "MAN Energy Solutions" | 多个用逗号分隔，去重 |
| `auxiliaryEngineModel` | 辅机型号 | voyage数据源 | "Caterpillar 3512C" | 多个用逗号分隔，去重 |
| `auxiliaryEngineManufacturer` | 辅机制造厂商 | voyage数据源 | "Caterpillar" | 多个用逗号分隔，去重 |

### 已有字段（保持不变）
- `vesselName` - 船舶名称
- `onBoardDate` - 上船日期
- `downBoardDate` - 下船日期
- 其他原有字段...

## 技术实现架构

### 数据流程
```
客户端请求
→ SeafarerScheduleController.getSeafarerQualificationInfo()
→ SeafarerScheduleServiceImpl.getSeafarerQualificationInfo() [@DataSource(CREW)]
→ 步骤1: 查询crew数据源（基础信息 + 船员姓名）
→ 步骤2: 为每条记录查询voyage数据源（航次信息）
    └── VoyageQualificationMapper.getVoyageQualificationInfo() [@DataSource(VOYAGE)]
→ 步骤3: 应用层聚合和清理数据
→ 返回完整结果
```

### 数据源切换机制
- **Service层**: 使用`@DataSource(CREW)`注解，默认使用crew数据源
- **Mapper层**: `VoyageQualificationMapper`使用`@DataSource(VOYAGE)`注解，自动切换到voyage数据源
- **优势**: 声明式数据源切换，代码更清晰，避免手动切换的风险

### 关键查询逻辑
```sql
-- 跨数据源船舶关联（通过MMSI码）
crew数据源: common_vessel.mmsi_code
voyage数据源: t_sh_ship.mmsi

-- 时间区间过滤条件
DATE(crew_seafarer_service_qualification_info.on_board_date) <= DATE(t_vy_stat_voyage.voyage_over_date)
AND DATE(t_vy_stat_voyage.voyage_over_date) <= DATE(crew_seafarer_service_qualification_info.down_board_date)

-- 表关联关系
t_vy_stat_voyage → t_vy_voyage (航次号)
t_vy_stat_voyage → t_vy_stat_line → t_vy_stat_cargo → t_cg_cargo (货品)
t_vy_stat_line → t_pt_terminal (码头)
t_vy_stat_voyage → t_sh_ship (主机/辅机信息，通过MMSI码关联)
```

## 测试用例

### 测试用例1：基础功能验证
**目的**: 验证接口能正常返回扩展字段

**测试步骤**:
1. 调用`/qualification-info`接口
2. 传入有效的`seafarerId`参数
3. 检查返回结果包含所有新增字段

**验证点**:
- ✅ 返回数据包含`seafarerName`字段
- ✅ 返回数据包含所有7个voyage相关字段
- ✅ 字段值格式正确（逗号分隔，无空值）

### 测试用例2：数据准确性验证
**目的**: 验证航次信息的准确性

**测试数据准备**:
```sql
-- 查询测试船员的基础信息
SELECT seafarer_id, seafarer_name, vessel_id, on_board_date, down_board_date
FROM crew_seafarer_service_qualification_info 
WHERE seafarer_id = 'TEST_SEAFARER_ID';

-- 查询对应的航次信息
SELECT sv.voyage_over_date, v.voyage_no, cg.cargo_name, t.terminal_name
FROM t_vy_stat_voyage sv
LEFT JOIN t_vy_voyage v ON sv.voyage_id = v.id
-- ... 其他关联表
WHERE sv.ship_id = 'TEST_VESSEL_ID'
AND DATE(sv.voyage_over_date) BETWEEN 'ON_BOARD_DATE' AND 'DOWN_BOARD_DATE';
```

**验证点**:
- ✅ 航次号与数据库查询结果一致
- ✅ 货品信息正确且去重
- ✅ 码头信息正确且去重
- ✅ 主机/辅机信息正确

### 测试用例3：边界情况测试
**目的**: 验证各种边界情况的处理

**测试场景**:
1. **无航次数据**: 船员服务期间无对应航次
2. **部分数据缺失**: 某些字段为null或空
3. **时间边界**: 上船/下船日期边界情况
4. **数据源异常**: voyage数据源连接异常

**验证点**:
- ✅ 无航次数据时返回空字符串
- ✅ 空值被正确过滤
- ✅ 异常情况有合理降级处理
- ✅ 日志记录完整

### 测试用例4：性能测试
**目的**: 验证扩展功能对性能的影响

**测试方法**:
1. 查询包含多条服务资历的船员
2. 记录接口响应时间
3. 监控数据库查询次数和时间

**验收标准**:
- ✅ 单次查询响应时间 < 5秒
- ✅ 无明显的性能下降
- ✅ 数据源切换正常

## 测试数据准备

### 数据库检查SQL
```sql
-- 1. 检查crew数据源基础数据（包含MMSI码）
SELECT
    a.seafarer_id,
    b.seafarer_name,
    a.vessel_name,
    a.vessel_id,
    cv.mmsi_code,
    a.on_board_date,
    a.down_board_date
FROM crew_seafarer_service_qualification_info a
LEFT JOIN crew_seafarer_info b ON a.seafarer_id = b.seafarer_id
LEFT JOIN common_vessel cv ON a.vessel_id = cv.vessel_id
WHERE a.delete_flag = '0'
AND a.seafarer_id = 'YOUR_TEST_SEAFARER_ID'
ORDER BY a.on_board_date DESC;

-- 2. 检查voyage数据源航次数据（通过MMSI码关联）
SELECT
    sv.ship_id,
    s.mmsi,
    sv.voyage_over_date,
    v.voyage_no,
    COUNT(*) as voyage_count
FROM t_vy_stat_voyage sv
LEFT JOIN t_vy_voyage v ON sv.voyage_id = v.id
LEFT JOIN t_sh_ship s ON sv.ship_id = s.id
WHERE sv.is_available = 0
AND s.mmsi = 'YOUR_TEST_MMSI_CODE'
AND s.is_delete != 1
GROUP BY sv.ship_id, s.mmsi, sv.voyage_over_date, v.voyage_no
ORDER BY sv.voyage_over_date;

-- 3. 检查货品和码头数据
SELECT 
    sc.voyage_id,
    cg.cargo_name,
    t.terminal_name
FROM t_vy_stat_cargo sc
LEFT JOIN t_cg_cargo cg ON sc.goods_id = cg.id
LEFT JOIN t_vy_stat_line sl ON sc.stat_line_id = sl.id
LEFT JOIN t_pt_terminal t ON sl.wharf_id = t.id
WHERE sc.voyage_id = 'YOUR_TEST_VOYAGE_ID';
```

## 问题排查指南

### 常见问题1：日期类型转换异常
**错误信息**: `java.lang.ClassCastException: java.time.LocalDateTime cannot be cast to java.lang.String`

**原因分析**:
- 数据库中的日期字段（`on_board_date`、`down_board_date`）是`datetime`类型
- MyBatis查询返回的是`LocalDateTime`对象，而不是`String`
- 直接强制转换会导致类型转换异常

**解决方案**:
- 添加`convertToDateString()`方法处理多种日期类型转换
- 支持`LocalDateTime`、`LocalDate`、`Date`、`Timestamp`等类型
- 统一转换为`yyyy-MM-dd HH:mm:ss`格式的字符串

**排查步骤**:
1. 检查日志中的具体异常堆栈
2. 确认数据库字段类型和MyBatis映射配置
3. 验证日期转换方法的处理逻辑

### 常见问题2：新增字段为空
**可能原因**:
- voyage数据源连接失败
- 时间区间过滤条件不匹配
- 船舶ID映射错误

**排查步骤**:
1. 检查日志中的数据源切换信息
2. 验证时间格式和区间逻辑
3. 确认vessel_id字段的数据一致性

### 常见问题3：数据重复或格式异常
**可能原因**:
- SQL查询未正确去重
- 字符串清理逻辑异常
- 数据库中存在脏数据

**排查步骤**:
1. 直接执行voyage数据源SQL查询
2. 检查`cleanString`方法的处理逻辑
3. 验证数据库数据质量

### 常见问题4：性能问题
**可能原因**:
- 缺少必要的数据库索引
- 查询逻辑效率低下
- 数据源切换频繁

**排查步骤**:
1. 分析SQL执行计划
2. 监控数据库连接池状态
3. 考虑添加缓存机制

## 接口示例

### 请求示例
```http
GET /api/crew/qualification-info?seafarerId=12345&applyDutyId=67890
```

### 响应示例
```json
[
  {
    "seafarerId": "12345",
    "seafarerName": "张三",
    "compName": "万邦船务",
    "vesselName": "万邦688",
    "vesselType": "油化船",
    "onBoardDate": "2024-10-05 00:00:00",
    "downBoardDate": "2024-12-15 00:00:00",
    "onBoardDays": 71,
    "voyageNumbers": "V.2501,V.2502,V.2503",
    "cargos": "苯酚,甲醇,丙烯",
    "terminals": "福炼,揭阳,惠州",
    "mainEngineModel": "MAN B&W 6S50MC-C",
    "mainEngineManufacturer": "MAN Energy Solutions",
    "auxiliaryEngineModel": "Caterpillar 3512C",
    "auxiliaryEngineManufacturer": "Caterpillar"
  }
]
```

## 验收标准

### 功能验收
- ✅ 所有新增字段正确返回
- ✅ 数据准确性验证通过
- ✅ 边界情况处理正常
- ✅ 错误处理机制完善

### 性能验收
- ✅ 接口响应时间符合要求
- ✅ 数据库查询效率合理
- ✅ 内存使用正常

### 稳定性验收
- ✅ 异常情况优雅降级
- ✅ 日志记录完整清晰
- ✅ 数据源切换稳定
