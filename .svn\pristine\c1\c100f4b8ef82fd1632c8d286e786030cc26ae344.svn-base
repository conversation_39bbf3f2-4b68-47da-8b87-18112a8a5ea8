package com.example.multidatasource.voyage.mapper;

import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.common.config.DataSourceContextHolder;
import com.example.multidatasource.voyage.dto.ShipEngineInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 船舶引擎信息Mapper接口
 * 
 * 用于查询船舶的主机和辅机相关信息，支持批量查询和缓存优化
 * 
 * 数据源：使用voyage数据源进行查询
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Mapper
@DataSource(DataSourceContextHolder.DataSourceType.VOYAGE)
public interface ShipEngineMapper {

    /**
     * 查询所有非国际船舶的主机/辅机信息
     * 
     * 功能说明：
     * 1. 查询所有business_model != 3的船舶（排除国际船舶）
     * 2. 获取船舶的主机型号、制造厂商、辅机型号、制造厂商
     * 3. 用于构建MMSI码到船舶引擎信息的映射缓存
     * 
     * 性能优化：
     * - 一次性查询所有相关船舶，避免N次查询
     * - 查询结果通常只有几十条记录，内存占用很小
     * - 支持应用层Map缓存，提升查询效率
     * 
     * @return 船舶引擎信息列表，包含MMSI码和引擎相关信息
     */
    List<ShipEngineInfo> getAllShipEngineInfo();
}
