package com.example.multidatasource.voyage.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 货主关联信息统计实体类
 */
@Data
@Schema(description = "货主关联信息统计")
public class CargoCompanyStats {
    
    @Schema(description = "货主公司ID", example = "1001")
    private Long cargoCompanyId;
    
    @Schema(description = "货主公司名称", example = "中远海运集团")
    private String cargoCompanyName;
    
    @Schema(description = "货物名称列表", example = "煤炭; 铁矿石; 原油")
    private String cargoNames;
    
    @Schema(description = "船舶名称列表", example = "远洋号; 海运星; 货运王")
    private String shipNames;
    
    @Schema(description = "装货总量", example = "15000.50")
    private BigDecimal loadUnitAmount;
    
    @Schema(description = "卸货总量", example = "14800.25")
    private BigDecimal unloadUnitAmount;
    
    @Schema(description = "货损量", example = "200.25")
    private BigDecimal goodsLossAmount;
    
    @Schema(description = "货损率（千分比）", example = "13.3500")
    private BigDecimal goodsLossRate;
    
    @Schema(description = "总锚泊时间", example = "48.50")
    private BigDecimal totalAnchorTime;
    
    @Schema(description = "航次数量", example = "25")
    private Integer voyageCount;

    @Schema(description = "公司介绍")
    private String intro;

    @Schema(description = "航次平均待泊时长", example = "2.5")
    private BigDecimal averageMooringTime;

    @Schema(description = "公司未承运货品列表", example = "甲苯; 石脑油")
    private String unshippedGoods;

    // 辅助字段，用于在服务层计算未承运货品，不返回给前端
    @Schema(hidden = true)
    private String shippedGoodsIds;
}
