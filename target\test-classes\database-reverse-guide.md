# 数据库反向生成工具使用指南

## 🛠️ 方法一：MyBatis Plus Generator（推荐）

### 1. 运行代码生成器
```bash
# 运行多数据源生成器
cd src/test/java/com/example/multidatasource/generator
java MultiDataSourceGenerator.java
```

### 2. 按提示选择
- 选择业务模块（船管、航次、货物、财务）
- 输入数据库连接信息
- 自动生成Entity、Mapper、Service、Controller

### 3. 生成的文件结构
```
src/main/java/com/example/multidatasource/
├── entity/
│   └── CrewInfo.java          # 实体类
├── mapper/
│   └── CrewInfoMapper.java    # Mapper接口
├── service/
│   ├── CrewInfoService.java   # Service接口
│   └── impl/
│       └── CrewInfoServiceImpl.java  # Service实现
└── controller/
    └── CrewInfoController.java       # Controller

src/main/resources/mapper/crew/
└── CrewInfoMapper.xml         # MyBatis XML映射文件
```

## 🎯 方法二：IDEA Database Tools（最简单）

### 1. 连接数据库
1. 打开IDEA，点击右侧 `Database` 面板
2. 点击 `+` → `Data Source` → `MySQL`
3. 输入数据库连接信息：
   ```
   Host: localhost
   Port: 3306
   Database: crew_management_dev
   User: root
   Password: 123456
   ```

### 2. 生成实体类
1. 在Database面板中展开数据库
2. 右键点击表名 → `Scripted Extensions` → `Generate POJOs.groovy`
3. 选择输出目录：`src/main/java/com/example/multidatasource/entity`
4. 自动生成实体类

### 3. 自定义生成模板
在IDEA中：`File` → `Settings` → `Database` → `User Parameters`

## 🔧 方法三：Easycode插件

### 1. 安装插件
1. `File` → `Settings` → `Plugins`
2. 搜索 `EasyCode` 并安装
3. 重启IDEA

### 2. 配置模板
1. `File` → `Settings` → `Other Settings` → `EasyCode`
2. 配置生成模板（Entity、Mapper、Service、Controller）

### 3. 生成代码
1. 在Database面板中右键表名
2. 选择 `EasyCode` → `Generate Code`
3. 选择要生成的文件类型

## 📋 各工具对比

| 工具 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| MyBatis Plus Generator | 功能完整、可定制性强 | 需要编写代码 | 大型项目、团队开发 |
| IDEA Database Tools | 简单易用、内置工具 | 功能相对简单 | 快速生成实体类 |
| Easycode插件 | 模板丰富、图形化操作 | 需要安装插件 | 中小型项目 |

## 💡 使用建议

### 1. 项目初期
- 使用 **MyBatis Plus Generator** 生成完整的代码结构
- 一次性生成所有业务模块的基础代码

### 2. 开发过程中
- 使用 **IDEA Database Tools** 快速生成新表的实体类
- 手动编写业务相关的Mapper和Service

### 3. 代码规范
- 生成后检查代码格式和注释
- 根据项目规范调整包名和类名
- 添加业务相关的注解（如@DataSource）

## 🚀 快速开始

### 1. 准备数据库表
```sql
-- 示例：船员信息表
CREATE TABLE crew_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '姓名',
    position VARCHAR(50) NOT NULL COMMENT '职位',
    phone VARCHAR(20) COMMENT '电话',
    email VARCHAR(100) COMMENT '邮箱',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. 运行生成器
```bash
# 进入项目目录
cd /path/to/your/project

# 运行多数据源生成器
java -cp "target/test-classes:target/classes" com.example.multidatasource.generator.MultiDataSourceGenerator
```

### 3. 检查生成的代码
- 确认Entity类的字段映射正确
- 检查Mapper接口的方法定义
- 验证Service层的业务逻辑
- 测试Controller的API接口

## ⚠️ 注意事项

1. **备份现有代码**：生成前备份已有的代码文件
2. **检查覆盖设置**：确认是否允许覆盖现有文件
3. **数据库连接**：确保数据库连接信息正确
4. **表结构规范**：建议使用规范的表名和字段名
5. **后续调整**：生成后根据业务需求调整代码
