# 测试环境配置
server:
  port: 54321
  servlet:
    context-path: /multi/source/api

# 多MySQL数据源配置 - 测试环境
datasources:
  # 船管系统数据库 - 测试环境
  crew:
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: **************************************************************************************************************************************
    username: test_user
    password: test_password
    type: mysql
    
  # 航次动态管理数据库 - 测试环境
  voyage:
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: ****************************************************************************************************************************************
    username: test_user
    password: test_password
    type: mysql
    
  # 货物管理数据库 - 测试环境
  cargo:
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: ***************************************************************************************************************************************
    username: test_user
    password: test_password
    type: mysql

  # 财务管理数据库 - 测试环境
  finance:
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: *****************************************************************************************************************************************
    username: test_user
    password: test_password
    type: mysql

# HikariCP连接池配置 - 测试环境
hikari:
  maximum-pool-size: 20
  minimum-idle: 5
  connection-timeout: 30000
  idle-timeout: 600000
  max-lifetime: 1800000
  leak-detection-threshold: 60000

# 日志配置 - 测试环境
logging:
  level:
    root: INFO
    com.example.multidatasource: DEBUG
    org.springframework.jdbc: INFO
    org.apache.ibatis: INFO
    org.mybatis: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/multi-datasource-test.log
    max-size: 200MB
    max-history: 30

# 测试环境监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env  # 测试环境暴露更多端点用于调试
  endpoint:
    health:
      show-details: always

# 测试环境数据库初始化
spring:
  sql:
    init:
      mode: always  # 测试环境执行初始化脚本
      continue-on-error: true
  jpa:
    show-sql: true  # 测试环境显示SQL用于调试

# 测试环境特定配置
test:
  data:
    cleanup: true  # 测试后清理数据
    mock-external-services: true  # 模拟外部服务
