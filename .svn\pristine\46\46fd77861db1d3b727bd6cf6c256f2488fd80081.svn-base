package com.example.multidatasource.voyage.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 海事信息爬取请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "海事信息爬取请求")
public class MaritimeCrawlRequestDTO {
    
    @Schema(description = "海事局名称列表（可选，不传则爬取所有启用的海事局）")
    private List<String> bureauNames;
    
    @Schema(description = "信息类型列表（可选，不传则爬取两种类型）", example = "[\"ALARM\", \"NOTICE\"]")
    private List<String> infoTypes;
    
    @Schema(description = "爬取天数（可选，默认1天）", example = "1")
    @Builder.Default
    private Integer days = 1;
}
