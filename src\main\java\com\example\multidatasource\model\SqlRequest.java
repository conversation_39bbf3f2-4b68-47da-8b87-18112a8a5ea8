package com.example.multidatasource.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * SQL执行请求模型
 */
@Data
@Schema(description = "SQL执行请求")
public class SqlRequest {
    
    @NotBlank(message = "数据源名称不能为空")
    @Schema(description = "数据源名称", example = "mysql-example")
    private String dataSourceName;
    
    @NotBlank(message = "SQL语句不能为空")
    @Schema(description = "要执行的SQL语句", example = "SELECT * FROM users WHERE id = ?")
    private String sql;
    
    @Schema(description = "SQL参数列表", example = "[1, \"张三\"]")
    private List<Object> parameters;
    
    @Schema(description = "查询类型", allowableValues = {"SELECT", "INSERT", "UPDATE", "DELETE"}, example = "SELECT")
    private String queryType = "SELECT";
    
    @Schema(description = "分页参数 - 页码(从1开始)", example = "1")
    private Integer page;
    
    @Schema(description = "分页参数 - 每页大小", example = "10")
    private Integer size;
    
    @Schema(description = "是否返回总数(用于分页)", example = "true")
    private Boolean needCount = false;
}
