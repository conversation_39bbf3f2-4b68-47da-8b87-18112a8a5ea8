# 货主关联信息查询API文档

## 概述
新增的货主关联信息查询接口，用于查询货主公司的货物、船舶、装卸量等关联统计信息。

## API接口

### 查询货主关联信息统计
**接口地址：** `POST /voyage/cargo-company-stats`

**接口描述：** 查询货主公司的货物、船舶、装卸量等关联统计信息

**请求参数：**
```json
{
  "cargoCompanyName": "中远海运",  // 货主公司名称（支持模糊查询，可选）
  "startDate": "2024-01-01",      // 开始日期（可选，格式：yyyy-MM-dd）
  "endDate": "2024-12-31",        // 结束日期（可选，格式：yyyy-MM-dd）
  "page": 1,                      // 页码（默认：1）
  "size": 10                      // 每页大小（默认：10）
}
```

**响应结果：**
```json
{
  "code": 200,
  "message": "操作成功",
  "success": true,
  "timestamp": 1640995200000,
  "data": {
    "records": [
      {
        "cargoCompanyId": 1001,
        "cargoCompanyName": "中远海运集团",
        "cargoNames": "煤炭; 铁矿石; 原油",
        "shipNames": "远洋号; 海运星; 货运王",
        "loadUnitAmount": 15000.50,
        "unloadUnitAmount": 14800.25,
        "goodsLossAmount": 200.25,
        "goodsLossRate": 13.3500,
        "totalAnchorTime": 48.50,
        "voyageCount": 25
      }
    ],
    "total": 100,
    "current": 1,
    "size": 10,
    "pages": 10
  }
}
```

**字段说明：**
- `cargoCompanyId`: 货主公司ID
- `cargoCompanyName`: 货主公司名称
- `cargoNames`: 货物名称列表（用分号分隔）
- `shipNames`: 船舶名称列表（用分号分隔）
- `loadUnitAmount`: 装货总量
- `unloadUnitAmount`: 卸货总量
- `goodsLossAmount`: 货损量（装货量-卸货量）
- `goodsLossRate`: 货损率（千分比）
- `totalAnchorTime`: 总锚泊时间
- `voyageCount`: 航次数量

## 查询逻辑

### 时间范围
- 如果同时提供 `startDate` 和 `endDate`，查询指定时间范围内的数据
- 如果只提供 `startDate`，查询从该日期开始的数据
- 如果只提供 `endDate`，查询到该日期为止的数据
- 如果都不提供，默认查询当年的数据

### 数据聚合
- 按货主公司分组统计
- 货物名称和船舶名称去重后用分号连接
- 装货量和卸货量分别汇总
- 货损率计算：(装货量-卸货量)/装货量*1000
- 航次数量统计去重的航次ID数量

### 排序
- 按装货总量降序排列

## 数据库表关联
查询涉及以下数据库表：
- `t_vy_stat_cargo`: 货物统计表
- `t_us_company`: 公司信息表
- `t_vy_stat_voyage`: 航次统计表
- `t_vy_stat_line`: 航线统计表
- `t_cg_cargo`: 货物信息表
- `t_sh_ship`: 船舶信息表

## 使用示例

### 查询所有货主当年数据
```bash
curl -X POST "http://localhost:54321/multi/source/api/voyage/cargo-company-stats" \
  -H "Content-Type: application/json" \
  -d '{
    "page": 1,
    "size": 10
  }'
```

### 查询指定公司指定时间范围数据
```bash
curl -X POST "http://localhost:54321/multi/source/api/voyage/cargo-company-stats" \
  -H "Content-Type: application/json" \
  -d '{
    "cargoCompanyName": "中远海运",
    "startDate": "2024-01-01",
    "endDate": "2024-06-30",
    "page": 1,
    "size": 20
  }'
```

## 注意事项
1. 该接口使用voyage数据源
2. 查询结果按装货总量降序排列
3. 货损率以千分比表示
4. 时间查询基于 `voyage_over_date` 字段
5. 支持货主公司名称的模糊查询
