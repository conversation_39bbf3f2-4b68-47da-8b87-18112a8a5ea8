package com.example.multidatasource.auth.service.impl;

import com.example.multidatasource.auth.entity.User;
import com.example.multidatasource.auth.service.AuthService;
import com.example.multidatasource.auth.service.UserService;
import com.example.multidatasource.common.dto.LoginRequest;
import com.example.multidatasource.common.dto.LoginResponse;
import com.example.multidatasource.common.dto.RegisterRequest;
import com.example.multidatasource.common.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 认证服务实现
 */
@Slf4j
@Service
public class AuthServiceImpl implements AuthService {

    @Autowired
    private UserService userService;
    
    @Autowired
    private JwtUtil jwtUtil;

    @Override
    public LoginResponse login(LoginRequest loginRequest, String clientIp) {
        // 查询用户
        User user = userService.getUserByUsername(loginRequest.getUsername());
        if (user == null) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 检查用户状态
        if (!user.isEnabled()) {
            throw new RuntimeException("用户账户已被禁用");
        }
        
        if (!user.isAccountNonLocked()) {
            throw new RuntimeException("用户账户已被锁定");
        }
        
        // 验证密码
        if (!userService.validatePassword(loginRequest.getPassword(), user.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 更新最后登录信息
        userService.updateLastLoginInfo(user.getId(), clientIp);
        
        // 生成JWT令牌
        String token = jwtUtil.generateToken(user.getUsername(), user.getId(), user.getRole());
        
        // 构建用户信息
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        userInfo.setId(user.getId());
        userInfo.setUsername(user.getUsername());
        userInfo.setRealName(user.getRealName());
        userInfo.setEmail(user.getEmail());
        userInfo.setRole(user.getRole());
        userInfo.setStatus(user.getStatus());
        
        log.info("User {} logged in successfully from IP: {}", user.getUsername(), clientIp);
        
        return LoginResponse.success(token, jwtUtil.getExpiration(), userInfo);
    }

    @Override
    public boolean register(RegisterRequest registerRequest) {
        try {
            return userService.createUser(registerRequest);
        } catch (Exception e) {
            log.error("User registration failed: {}", e.getMessage());
            throw new RuntimeException("注册失败: " + e.getMessage());
        }
    }

    @Override
    public boolean logout(String token) {
        try {
            // 这里可以实现令牌黑名单机制
            // 目前简单返回true，实际项目中可以将token加入Redis黑名单
            String username = jwtUtil.getUsernameFromToken(token);
            log.info("User {} logged out", username);
            return true;
        } catch (Exception e) {
            log.error("Logout failed: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public LoginResponse refreshToken(String token) {
        try {
            String username = jwtUtil.getUsernameFromToken(token);
            User user = userService.getUserByUsername(username);
            
            if (user == null || !user.isEnabled()) {
                throw new RuntimeException("用户不存在或已被禁用");
            }
            
            // 生成新的JWT令牌
            String newToken = jwtUtil.generateToken(user.getUsername(), user.getId(), user.getRole());
            
            // 构建用户信息
            LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
            userInfo.setId(user.getId());
            userInfo.setUsername(user.getUsername());
            userInfo.setRealName(user.getRealName());
            userInfo.setEmail(user.getEmail());
            userInfo.setRole(user.getRole());
            userInfo.setStatus(user.getStatus());
            
            return LoginResponse.success(newToken, jwtUtil.getExpiration(), userInfo);
        } catch (Exception e) {
            log.error("Token refresh failed: {}", e.getMessage());
            throw new RuntimeException("令牌刷新失败");
        }
    }

    @Override
    public boolean validateToken(String token) {
        try {
            String username = jwtUtil.getUsernameFromToken(token);
            return jwtUtil.validateToken(token, username);
        } catch (Exception e) {
            log.debug("Token validation failed: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public String getUsernameFromToken(String token) {
        try {
            return jwtUtil.getUsernameFromToken(token);
        } catch (Exception e) {
            log.debug("Failed to get username from token: {}", e.getMessage());
            return null;
        }
    }

    @Override
    public Long getUserIdFromToken(String token) {
        try {
            return jwtUtil.getUserIdFromToken(token);
        } catch (Exception e) {
            log.debug("Failed to get user ID from token: {}", e.getMessage());
            return null;
        }
    }
}
