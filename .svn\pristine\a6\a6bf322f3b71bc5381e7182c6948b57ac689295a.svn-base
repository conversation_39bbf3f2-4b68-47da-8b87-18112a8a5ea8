package com.example.multidatasource.crew.service;

import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.crew.dto.SeafarerMatchRequestDTO;
import com.example.multidatasource.crew.dto.QualificationWithEvaluationDTO;
import com.example.multidatasource.crew.dto.HandoverPairDTO;
import com.example.multidatasource.crew.entity.CrewInfo;

import java.util.List;
import java.util.Map;

/**
 * 船员管理服务接口
 */
public interface SeafarerScheduleService {

    /**
     * 多条件查询船员（分页）
     */
    PageResult<CrewInfo> queryCrewsPage(Integer page, Integer size, String seafarerId, String seafarerName, String applyDutyId);

    /**
     * 查询船员证书到期列表
     * @param seafarerId 船员ID
     * @return 证书信息列表
     */
    List<Map<String, Object>> getSeafarerCertificateInfo(String seafarerId);

    /**
     * 查询船员服务资历
     * @param seafarerId 船员ID
     * @param applyDutyId 申请职务ID（可选）
     * @return 服务资历列表
     */
    QualificationWithEvaluationDTO getSeafarerQualificationInfo(String seafarerId, String applyDutyId);

    /**
     * 分页查询船员服务资历
     * @param seafarerId 船员ID
     * @param applyDutyId 申请职务ID（可选）
     * @param page 页码
     * @param size 每页大小
     * @return 分页服务资历列表
     */
    QualificationWithEvaluationDTO getSeafarerQualificationInfoPage(String seafarerId, String applyDutyId, Integer page, Integer size);

    /**
     * 匹配上下船船员数据（匹配所有候选人）
     * @param request 匹配请求参数
     * @return 匹配结果（在船船员 + 对应的候选船员列表）
     */
    List<Map<String, Object>> matchSeafarerForShiftChange(SeafarerMatchRequestDTO request);

    /**
     * 匹配上下船船员数据（每个在船船员只匹配一个最优候选人）
     * @param request 匹配请求参数
     * @return 匹配结果（在船船员 + 对应的单个最优候选船员）
     */
    List<Map<String, Object>> matchSingleSeafarerForShiftChange(SeafarerMatchRequestDTO request);


    /**
     * 生成交接班列表Excel文件
     * @param handoverPairs 交接班配对列表
     * @return Excel文件的字节数组
     * @throws Exception 生成文件过程中的异常
     */
    byte[] generateHandoverExcel(List<HandoverPairDTO> handoverPairs) throws Exception;
}
