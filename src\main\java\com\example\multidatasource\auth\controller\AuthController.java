package com.example.multidatasource.auth.controller;

import com.example.multidatasource.auth.service.AuthService;
import com.example.multidatasource.common.dto.LoginRequest;
import com.example.multidatasource.common.dto.LoginResponse;
import com.example.multidatasource.common.dto.RegisterRequest;
import com.example.multidatasource.model.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 认证控制器
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@Tag(name = "身份认证", description = "用户登录、注册、登出等认证相关API")
public class AuthController {

    @Autowired
    private AuthService authService;

    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户名密码登录，返回JWT令牌")
    public ApiResponse<LoginResponse> login(@Valid @RequestBody LoginRequest loginRequest,
                                          HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            LoginResponse response = authService.login(loginRequest, clientIp);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.warn("Login failed for user {}: {}", loginRequest.getUsername(), e.getMessage());
            return ApiResponse.error("登录失败: " + e.getMessage());
        }
    }

    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "注册新用户账户")
    public ApiResponse<String> register(@Valid @RequestBody RegisterRequest registerRequest) {
        try {
            boolean success = authService.register(registerRequest);
            if (success) {
                return ApiResponse.success("注册成功");
            } else {
                return ApiResponse.error("注册失败");
            }
        } catch (Exception e) {
            log.warn("Registration failed for user {}: {}", registerRequest.getUsername(), e.getMessage());
            return ApiResponse.error("注册失败: " + e.getMessage());
        }
    }

    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出，使令牌失效")
    public ApiResponse<String> logout(@Parameter(description = "JWT令牌") @RequestHeader("Authorization") String authorization) {
        try {
            String token = extractToken(authorization);
            if (token == null) {
                return ApiResponse.error("无效的令牌格式");
            }
            
            boolean success = authService.logout(token);
            if (success) {
                return ApiResponse.success("登出成功");
            } else {
                return ApiResponse.error("登出失败");
            }
        } catch (Exception e) {
            log.warn("Logout failed: {}", e.getMessage());
            return ApiResponse.error("登出失败: " + e.getMessage());
        }
    }

    @PostMapping("/refresh")
    @Operation(summary = "刷新令牌", description = "使用当前令牌获取新的令牌")
    public ApiResponse<LoginResponse> refreshToken(@Parameter(description = "JWT令牌") @RequestHeader("Authorization") String authorization) {
        try {
            String token = extractToken(authorization);
            if (token == null) {
                return ApiResponse.error("无效的令牌格式");
            }
            
            LoginResponse response = authService.refreshToken(token);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.warn("Token refresh failed: {}", e.getMessage());
            return ApiResponse.error("令牌刷新失败: " + e.getMessage());
        }
    }

    @GetMapping("/validate")
    @Operation(summary = "验证令牌", description = "验证JWT令牌是否有效")
    public ApiResponse<Boolean> validateToken(@Parameter(description = "JWT令牌") @RequestHeader("Authorization") String authorization) {
        try {
            String token = extractToken(authorization);
            if (token == null) {
                return ApiResponse.success(false);
            }
            
            boolean isValid = authService.validateToken(token);
            return ApiResponse.success(isValid);
        } catch (Exception e) {
            log.debug("Token validation failed: {}", e.getMessage());
            return ApiResponse.success(false);
        }
    }

    @GetMapping("/user-info")
    @Operation(summary = "获取当前用户信息", description = "根据令牌获取当前登录用户的基本信息")
    public ApiResponse<Object> getCurrentUserInfo(@Parameter(description = "JWT令牌") @RequestHeader("Authorization") String authorization) {
        try {
            String token = extractToken(authorization);
            if (token == null) {
                return ApiResponse.error("无效的令牌格式");
            }
            
            if (!authService.validateToken(token)) {
                return ApiResponse.error("令牌已失效");
            }
            
            String username = authService.getUsernameFromToken(token);
            Long userId = authService.getUserIdFromToken(token);
            
            return ApiResponse.success(new Object() {
                public String getUsername() { return username; }
                public Long getUserId() { return userId; }
            });
        } catch (Exception e) {
            log.warn("Get user info failed: {}", e.getMessage());
            return ApiResponse.error("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 从Authorization头中提取JWT令牌
     */
    private String extractToken(String authorization) {
        if (authorization != null && authorization.startsWith("Bearer ")) {
            return authorization.substring(7);
        }
        return null;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
