package com.example.multidatasource.crew.mapper;

import com.example.multidatasource.crew.entity.CrewInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 船员信息Mapper接口
 */
@Mapper
public interface CrewMapper {

    /**
     * 查询所有船员信息（分页）
     */
    List<CrewInfo> selectCrewList(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 根据ID查询船员信息
     */
    CrewInfo selectCrewById(@Param("id") String id);

    /**
     * 插入船员信息
     */
    int insertCrew(CrewInfo crewInfo);

    /**
     * 更新船员信息
     */
    int updateCrew(CrewInfo crewInfo);

    /**
     * 删除船员信息
     */
    int deleteCrewById(@Param("id") Long id);

    /**
     * 根据状态查询船员列表
     */
    List<CrewInfo> selectCrewByStatus(@Param("status") String status);

    /**
     * 根据职位查询船员列表
     */
    List<CrewInfo> selectCrewByPosition(@Param("position") String position);

    /**
     * 统计船员总数
     */
    int countCrew();

    /**
     * 根据姓名模糊查询船员
     */
    List<CrewInfo> selectCrewByName(@Param("name") String name);

    /**
     * 根据多个条件查询船员
     */
    List<CrewInfo> selectCrewByConditions(@Param("name") String name, 
                                         @Param("position") String position,
                                         @Param("status") String status,
                                         @Param("phone") String phone,
                                         @Param("offset") Integer offset,
                                         @Param("limit") Integer limit);

    /**
     * 统计符合条件的船员总数
     */
    int countCrewByConditions(@Param("name") String name, 
                             @Param("position") String position,
                             @Param("status") String status,
                             @Param("phone") String phone);

    /**
     * 获取所有职位列表
     */
    List<String> selectAllPositions();

    /**
     * 批量更新船员状态
     */
    int batchUpdateCrewStatus(@Param("ids") List<Long> ids, @Param("status") String status);

    /**
     * 执行自定义SQL查询
     */
    List<Map<String, Object>> executeCustomQuery(@Param("sql") String sql, @Param("params") Object[] params);

    /**
     * 执行自定义SQL更新
     */
    int executeCustomUpdate(@Param("sql") String sql, @Param("params") Object[] params);
}
