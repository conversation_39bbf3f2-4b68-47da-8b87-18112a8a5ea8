package com.example.multidatasource.config;

import com.example.multidatasource.model.DataSourceConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 数据源配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "")
public class DataSourceProperties {
    
    /**
     * 多数据源配置映射
     * key: 数据源名称
     * value: 数据源配置
     */
    private Map<String, DataSourceConfig> datasources;
    
    /**
     * HikariCP连接池配置
     */
    private HikariConfig hikari = new HikariConfig();
    
    @Data
    public static class HikariConfig {
        private Integer maximumPoolSize = 10;
        private Integer minimumIdle = 5;
        private Long connectionTimeout = 30000L;
        private Long idleTimeout = 600000L;
        private Long maxLifetime = 1800000L;
    }
}
