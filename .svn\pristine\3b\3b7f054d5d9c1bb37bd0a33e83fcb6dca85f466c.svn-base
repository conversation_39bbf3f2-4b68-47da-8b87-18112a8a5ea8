package com.example.multidatasource.crew.service.impl;

import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.common.config.DataSourceContextHolder;
import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.crew.dto.SeafarerMatchRequestDTO;
import com.example.multidatasource.crew.entity.CrewInfo;
import com.example.multidatasource.crew.mapper.SeafarerScheduleMapper;
import com.example.multidatasource.crew.service.SeafarerMatchingService;
import com.example.multidatasource.crew.service.SeafarerScheduleService;
import com.example.multidatasource.voyage.mapper.VoyageQualificationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 船员管理服务实现
 */
@Slf4j
@Service
@DataSource(DataSourceContextHolder.DataSourceType.CREW)
public class SeafarerScheduleServiceImpl implements SeafarerScheduleService {

    @Autowired
    private SeafarerScheduleMapper seafarerScheduleMapper;

    @Autowired
    private SeafarerMatchingService seafarerMatchingService;

    @Autowired
    private VoyageQualificationMapper voyageQualificationMapper;


    @Override
    public PageResult<CrewInfo> queryCrewsPage(Integer page, Integer size, String seafarerId, String seafarerName, String applyDutyId) {
        int offset = (page - 1) * size;
        List<CrewInfo> records = seafarerScheduleMapper.selectCrewList(offset, size, seafarerId, seafarerName, applyDutyId);
        int total = seafarerScheduleMapper.countCrew(seafarerId, seafarerName, applyDutyId);

        return PageResult.of(records, (long) total, page, size);
    }

    @Override
    public List<Map<String, Object>> getSeafarerCertificateInfo(String seafarerId) {
        log.info("查询船员证书到期列表, seafarerId: {}", seafarerId);
        try {
            List<Map<String, Object>> result = seafarerScheduleMapper.getSeafarerCertificateInfo(seafarerId);
            log.info("成功查询到{}条证书记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("查询船员证书信息失败, seafarerId: {}", seafarerId, e);
            throw new RuntimeException("查询船员证书信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getSeafarerQualificationInfo(String seafarerId, String applyDutyId) {
        log.info("查询船员服务资历（扩展版本）, seafarerId: {}, applyDutyId: {}", seafarerId, applyDutyId);
        try {
            // 步骤1: 查询crew数据源，获取基础服务资历信息（包含船员姓名）
            List<Map<String, Object>> crewQualifications = seafarerScheduleMapper.getSeafarerQualificationInfo(seafarerId, applyDutyId);
            log.info("从crew数据源查询到{}条服务资历记录", crewQualifications.size());

            // 步骤2: 为每条服务资历记录添加航次相关信息
            for (Map<String, Object> qualification : crewQualifications) {
                enrichWithVoyageInfo(qualification);
            }

            log.info("成功完成{}条服务资历记录的航次信息扩展", crewQualifications.size());
            return crewQualifications;
        } catch (Exception e) {
            log.error("查询船员服务资历失败, seafarerId: {}, applyDutyId: {}", seafarerId, applyDutyId, e);
            throw new RuntimeException("查询船员服务资历失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> matchSeafarerForShiftChange(SeafarerMatchRequestDTO request) {
        log.info("使用优化后的匹配服务 - 匹配所有候选人");
        return seafarerMatchingService.matchSeafarerForShiftChange(request);
    }

    @Override
    public List<Map<String, Object>> matchSingleSeafarerForShiftChange(SeafarerMatchRequestDTO request) {
        log.info("使用优化后的匹配服务 - 单个最优匹配");
        return seafarerMatchingService.matchSingleSeafarerForShiftChange(request);
    }

    /**
     * 为服务资历记录添加航次相关信息
     *
     * 功能说明：
     * 1. 根据船舶MMSI码和上下船时间查询voyage数据源
     * 2. 通过MMSI码实现跨数据源船舶关联
     * 3. 获取航次号、装卸货品、码头、主机/辅机信息
     * 4. 将查询结果添加到原始记录中
     *
     * 注意：数据源切换通过VoyageQualificationMapper上的@DataSource注解自动处理
     *
     * @param qualification 服务资历记录（会被直接修改）
     */
    private void enrichWithVoyageInfo(Map<String, Object> qualification) {
        try {
            // 获取查询voyage数据源所需的参数
            String mmsiCode = (String) qualification.get("mmsiCode");
            String onBoardDate = convertToDateString(qualification.get("onBoardDate"));
            String downBoardDate = convertToDateString(qualification.get("downBoardDate"));

            // 参数验证
            if (mmsiCode == null || mmsiCode.trim().isEmpty()) {
                log.warn("船舶MMSI码为空，跳过航次信息查询: seafarerId={}, vesselName={}",
                        qualification.get("seafarerId"), qualification.get("vesselName"));
                setDefaultVoyageInfo(qualification);
                return;
            }

            if (onBoardDate == null || onBoardDate.trim().isEmpty()) {
                log.warn("上船日期为空，跳过航次信息查询: seafarerId={}, mmsiCode={}",
                        qualification.get("seafarerId"), mmsiCode);
                setDefaultVoyageInfo(qualification);
                return;
            }

            // 查询voyage数据源航次信息（通过MMSI码关联，数据源切换由@DataSource注解自动处理）
            Map<String, Object> voyageInfo = voyageQualificationMapper.getVoyageQualificationInfo(
                    mmsiCode, onBoardDate, downBoardDate);

            // 将航次信息添加到原始记录中
            if (voyageInfo != null) {
                qualification.put("voyageNumbers", cleanString((String) voyageInfo.get("voyageNumbers")));
                qualification.put("cargos", cleanString((String) voyageInfo.get("cargos")));
                qualification.put("terminals", cleanString((String) voyageInfo.get("terminals")));
                qualification.put("mainEngineModel", cleanString((String) voyageInfo.get("mainEngineModel")));
                qualification.put("mainEngineManufacturer", cleanString((String) voyageInfo.get("mainEngineManufacturer")));
                qualification.put("auxiliaryEngineModel", cleanString((String) voyageInfo.get("auxiliaryEngineModel")));
                qualification.put("auxiliaryEngineManufacturer", cleanString((String) voyageInfo.get("auxiliaryEngineManufacturer")));

                log.debug("成功为船员{}添加航次信息", qualification.get("seafarerId"));
            } else {
                log.warn("未查询到航次信息: mmsiCode={}, onBoardDate={}, downBoardDate={}",
                        mmsiCode, onBoardDate, downBoardDate);
                setDefaultVoyageInfo(qualification);
            }

        } catch (Exception e) {
            log.error("查询航次信息失败: {}", qualification.get("seafarerId"), e);
            setDefaultVoyageInfo(qualification);
        }
    }

    /**
     * 设置默认的航次信息（空值）
     */
    private void setDefaultVoyageInfo(Map<String, Object> qualification) {
        qualification.put("voyageNumbers", "");
        qualification.put("cargos", "");
        qualification.put("terminals", "");
        qualification.put("mainEngineModel", "");
        qualification.put("mainEngineManufacturer", "");
        qualification.put("auxiliaryEngineModel", "");
        qualification.put("auxiliaryEngineManufacturer", "");
    }

    /**
     * 清理字符串，去除空值和null值
     *
     * @param str 原始字符串（可能包含空值）
     * @return 清理后的字符串
     */
    private String cleanString(String str) {
        if (str == null || str.trim().isEmpty()) {
            return "";
        }

        // 分割字符串，去除空值和null值，然后重新拼接
        String[] parts = str.split(",");
        StringBuilder result = new StringBuilder();

        for (String part : parts) {
            if (part != null && !part.trim().isEmpty() && !"null".equalsIgnoreCase(part.trim())) {
                if (result.length() > 0) {
                    result.append(",");
                }
                result.append(part.trim());
            }
        }

        return result.toString();
    }

    /**
     * 将日期对象转换为字符串格式
     *
     * 支持多种日期类型的转换：
     * - LocalDateTime -> "yyyy-MM-dd HH:mm:ss"
     * - LocalDate -> "yyyy-MM-dd"
     * - String -> 直接返回
     * - null -> null
     *
     * @param dateObj 日期对象
     * @return 格式化后的日期字符串
     */
    private String convertToDateString(Object dateObj) {
        if (dateObj == null) {
            return null;
        }

        if (dateObj instanceof String) {
            return (String) dateObj;
        }

        if (dateObj instanceof java.time.LocalDateTime) {
            java.time.LocalDateTime localDateTime = (java.time.LocalDateTime) dateObj;
            return localDateTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        if (dateObj instanceof java.time.LocalDate) {
            java.time.LocalDate localDate = (java.time.LocalDate) dateObj;
            return localDate.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }

        if (dateObj instanceof java.util.Date) {
            java.util.Date date = (java.util.Date) dateObj;
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(date);
        }

        if (dateObj instanceof java.sql.Timestamp) {
            java.sql.Timestamp timestamp = (java.sql.Timestamp) dateObj;
            return timestamp.toLocalDateTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        // 如果是其他类型，尝试转换为字符串
        return dateObj.toString();
    }

}
