package com.example.multidatasource.crew.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Map;

/**
 * 船员统计功能调试工具类
 * 
 * 用途：
 * 1. 快速验证统计计算的正确性
 * 2. 提供调试信息输出
 * 3. 协助问题排查和数据验证
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@Component
public class SeafarerStatsDebugUtil {

    /**
     * 验证个人统计字段计算是否正确
     * 
     * @param seafarerId 船员ID
     * @param enterCompanyDate 入司日期字符串
     * @param actualCompanyYears 实际计算的入司年数
     * @param actualDutyYears 实际计算的职务年数
     * @param actualVesselTypeYears 实际计算的船型年数
     */
    public void validatePersonalStats(String seafarerId, String enterCompanyDate, 
                                    Double actualCompanyYears, Double actualDutyYears, 
                                    Double actualVesselTypeYears) {
        log.info("=== 个人统计验证 ===");
        log.info("船员ID: {}", seafarerId);
        
        // 验证入司时间计算
        if (enterCompanyDate != null) {
            try {
                LocalDate enterDate = LocalDate.parse(enterCompanyDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                LocalDate currentDate = LocalDate.now();
                long daysBetween = ChronoUnit.DAYS.between(enterDate, currentDate);
                double expectedCompanyYears = Math.round(daysBetween / 360.0 * 100.0) / 100.0;
                
                log.info("入司日期: {}, 当前日期: {}, 相差天数: {}", enterDate, currentDate, daysBetween);
                log.info("预期入司年数: {}, 实际入司年数: {}, 匹配: {}", 
                        expectedCompanyYears, actualCompanyYears, 
                        Math.abs(expectedCompanyYears - (actualCompanyYears != null ? actualCompanyYears : 0.0)) < 0.01);
            } catch (Exception e) {
                log.warn("入司日期解析失败: {}", enterCompanyDate, e);
            }
        }
        
        log.info("职务年数: {}", actualDutyYears);
        log.info("船型年数: {}", actualVesselTypeYears);
        log.info("===================");
    }

    /**
     * 验证职务组合统计计算是否正确
     * 
     * @param candidateDutyName 候选人职务
     * @param partnerDutyName 搭档职务
     * @param candidateStats 候选人统计
     * @param partnerStats 搭档统计
     * @param groupStats 组合统计
     */
    public void validateGroupStats(String candidateDutyName, String partnerDutyName,
                                 Map<String, Double> candidateStats, Map<String, Object> partnerStats,
                                 Map<String, Double> groupStats) {
        log.info("=== 组合统计验证 ===");
        log.info("候选人职务: {}, 搭档职务: {}", candidateDutyName, partnerDutyName);
        
        if (candidateStats != null && partnerStats != null && groupStats != null) {
            // 验证入司时间组合
            Double candidateCompanyYears = candidateStats.get("companyYears");
            Double partnerCompanyDays = parseDouble(partnerStats.get("partnerCompanyDays"));
            Double partnerCompanyYears = partnerCompanyDays != null ? partnerCompanyDays / 360.0 : 0.0;
            Double expectedGroupCompanyYears = (candidateCompanyYears != null ? candidateCompanyYears : 0.0) + partnerCompanyYears;
            Double actualGroupCompanyYears = groupStats.get("groupCompanyYears");
            
            log.info("入司时间组合 - 候选人: {}年, 搭档: {}年, 预期组合: {}年, 实际组合: {}年", 
                    candidateCompanyYears, partnerCompanyYears, expectedGroupCompanyYears, actualGroupCompanyYears);
            
            // 验证职务年限组合
            Double candidateDutyYears = candidateStats.get("dutyYears");
            Double partnerDutyDays = parseDouble(partnerStats.get("partnerDutyDays"));
            Double partnerDutyYears = partnerDutyDays != null ? partnerDutyDays / 360.0 : 0.0;
            Double expectedGroupDutyYears = (candidateDutyYears != null ? candidateDutyYears : 0.0) + partnerDutyYears;
            Double actualGroupDutyYears = groupStats.get("groupDutyYears");
            
            log.info("职务年限组合 - 候选人: {}年, 搭档: {}年, 预期组合: {}年, 实际组合: {}年", 
                    candidateDutyYears, partnerDutyYears, expectedGroupDutyYears, actualGroupDutyYears);
            
            // 验证船型经验组合
            Double candidateVesselTypeYears = candidateStats.get("vesselTypeYears");
            Double partnerVesselTypeDays = parseDouble(partnerStats.get("partnerVesselTypeDays"));
            Double partnerVesselTypeYears = partnerVesselTypeDays != null ? partnerVesselTypeDays / 360.0 : 0.0;
            Double expectedGroupVesselTypeYears = (candidateVesselTypeYears != null ? candidateVesselTypeYears : 0.0) + partnerVesselTypeYears;
            Double actualGroupVesselTypeYears = groupStats.get("groupVesselTypeYears");
            
            log.info("船型经验组合 - 候选人: {}年, 搭档: {}年, 预期组合: {}年, 实际组合: {}年", 
                    candidateVesselTypeYears, partnerVesselTypeYears, expectedGroupVesselTypeYears, actualGroupVesselTypeYears);
        }
        
        log.info("===================");
    }

    /**
     * 输出搭档信息调试日志
     * 
     * @param candidateDutyName 候选人职务
     * @param partnerDutyName 搭档职务
     * @param partnerSeafarerName 搭档船员姓名
     * @param partnerStats 搭档统计信息
     */
    public void debugPartnerInfo(String candidateDutyName, String partnerDutyName, 
                               String partnerSeafarerName, Map<String, Object> partnerStats) {
        log.info("=== 搭档信息调试 ===");
        log.info("候选人职务: {}", candidateDutyName);
        log.info("搭档职务: {}", partnerDutyName);
        log.info("搭档船员姓名: {}", partnerSeafarerName);
        
        if (partnerStats != null) {
            log.info("搭档统计信息:");
            log.info("  - 搭档姓名列表: {}", partnerStats.get("partnerSeafarerNames"));
            log.info("  - 搭档人数: {}", partnerStats.get("partnerCount"));
            log.info("  - 搭档入司天数: {}", partnerStats.get("partnerCompanyDays"));
            log.info("  - 搭档职务天数: {}", partnerStats.get("partnerDutyDays"));
            log.info("  - 搭档船型天数: {}", partnerStats.get("partnerVesselTypeDays"));
        } else {
            log.info("搭档统计信息: null");
        }
        
        log.info("===================");
    }

    /**
     * 输出SQL查询调试信息
     * 
     * @param queryName 查询名称
     * @param parameters 查询参数
     * @param resultCount 结果数量
     * @param executionTime 执行时间（毫秒）
     */
    public void debugSqlQuery(String queryName, Map<String, Object> parameters, 
                            int resultCount, long executionTime) {
        log.info("=== SQL查询调试 ===");
        log.info("查询名称: {}", queryName);
        log.info("查询参数: {}", parameters);
        log.info("结果数量: {}", resultCount);
        log.info("执行时间: {}ms", executionTime);
        
        if (executionTime > 1000) {
            log.warn("查询执行时间过长，建议优化: {}ms", executionTime);
        }
        
        log.info("===================");
    }

    /**
     * 检查数据完整性
     * 
     * @param seafarerData 船员数据
     * @return 数据完整性检查结果
     */
    public boolean checkDataIntegrity(Map<String, Object> seafarerData) {
        log.info("=== 数据完整性检查 ===");
        
        boolean isValid = true;
        
        // 检查必要字段
        String[] requiredFields = {"seafarerId", "seafarerName", "applyDutyName"};
        for (String field : requiredFields) {
            if (seafarerData.get(field) == null) {
                log.warn("缺少必要字段: {}", field);
                isValid = false;
            }
        }
        
        // 检查日期字段
        String enterCompanyDate = (String) seafarerData.get("enterCompanyDate");
        if (enterCompanyDate != null) {
            try {
                LocalDate.parse(enterCompanyDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (Exception e) {
                log.warn("入司日期格式错误: {}", enterCompanyDate);
                isValid = false;
            }
        }
        
        log.info("数据完整性检查结果: {}", isValid ? "通过" : "失败");
        log.info("===================");
        
        return isValid;
    }

    /**
     * 解析Double值（复制自主类的方法）
     */
    private Double parseDouble(Object obj) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof Double) {
            return (Double) obj;
        }

        if (obj instanceof Number) {
            return ((Number) obj).doubleValue();
        }

        if (obj instanceof String) {
            String str = (String) obj;
            if (str != null && !str.trim().isEmpty()) {
                try {
                    return Double.parseDouble(str.trim());
                } catch (NumberFormatException e) {
                    log.warn("浮点数解析失败: {}", str, e);
                }
            }
        }

        return null;
    }
}
