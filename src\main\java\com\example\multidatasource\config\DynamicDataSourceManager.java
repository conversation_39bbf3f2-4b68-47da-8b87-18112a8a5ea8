package com.example.multidatasource.config;

import com.example.multidatasource.model.DataSourceConfig;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 动态数据源管理器
 */
@Slf4j
@Component
public class DynamicDataSourceManager {
    
    private final Map<String, DataSource> dataSourceCache = new ConcurrentHashMap<>();
    
    @Autowired
    private DataSourceProperties dataSourceProperties;
    
    /**
     * 获取数据源
     */
    public DataSource getDataSource(String dataSourceName) {
        return dataSourceCache.computeIfAbsent(dataSourceName, this::createDataSource);
    }
    
    /**
     * 创建数据源
     */
    private DataSource createDataSource(String dataSourceName) {
        DataSourceConfig config = getDataSourceConfig(dataSourceName);
        if (config == null) {
            throw new IllegalArgumentException("数据源配置不存在: " + dataSourceName);
        }
        
        try {
            HikariConfig hikariConfig = new HikariConfig();
            hikariConfig.setDriverClassName(config.getDriverClassName());
            hikariConfig.setJdbcUrl(config.getUrl());
            hikariConfig.setUsername(config.getUsername());
            hikariConfig.setPassword(config.getPassword());
            
            // 设置连接池参数
            DataSourceProperties.HikariConfig globalHikari = dataSourceProperties.getHikari();
            hikariConfig.setMaximumPoolSize(config.getMaximumPoolSize() != null ? 
                config.getMaximumPoolSize() : globalHikari.getMaximumPoolSize());
            hikariConfig.setMinimumIdle(config.getMinimumIdle() != null ? 
                config.getMinimumIdle() : globalHikari.getMinimumIdle());
            hikariConfig.setConnectionTimeout(config.getConnectionTimeout() != null ? 
                config.getConnectionTimeout() : globalHikari.getConnectionTimeout());
            hikariConfig.setIdleTimeout(config.getIdleTimeout() != null ? 
                config.getIdleTimeout() : globalHikari.getIdleTimeout());
            hikariConfig.setMaxLifetime(config.getMaxLifetime() != null ? 
                config.getMaxLifetime() : globalHikari.getMaxLifetime());
            
            // 设置连接池名称
            hikariConfig.setPoolName("HikariPool-" + dataSourceName);
            
            // 设置连接测试查询
            setTestQuery(hikariConfig, config.getType());
            
            HikariDataSource dataSource = new HikariDataSource(hikariConfig);
            
            // 测试连接
            testConnection(dataSource, dataSourceName);
            
            log.info("成功创建数据源: {}", dataSourceName);
            return dataSource;
            
        } catch (Exception e) {
            log.error("创建数据源失败: {}", dataSourceName, e);
            throw new RuntimeException("创建数据源失败: " + dataSourceName, e);
        }
    }
    
    /**
     * 获取数据源配置
     */
    private DataSourceConfig getDataSourceConfig(String dataSourceName) {
        Map<String, DataSourceConfig> datasources = dataSourceProperties.getDatasources();
        if (datasources == null) {
            return null;
        }
        
        DataSourceConfig config = datasources.get(dataSourceName);
        if (config != null) {
            config.setName(dataSourceName);
        }
        return config;
    }
    
    /**
     * 设置数据库测试查询
     */
    private void setTestQuery(HikariConfig hikariConfig, String dbType) {
        if (dbType == null) return;
        
        switch (dbType.toLowerCase()) {
            case "mysql":
                hikariConfig.setConnectionTestQuery("SELECT 1");
                break;
            case "postgresql":
                hikariConfig.setConnectionTestQuery("SELECT 1");
                break;
            case "oracle":
                hikariConfig.setConnectionTestQuery("SELECT 1 FROM DUAL");
                break;
            case "sqlserver":
                hikariConfig.setConnectionTestQuery("SELECT 1");
                break;
            default:
                hikariConfig.setConnectionTestQuery("SELECT 1");
        }
    }
    
    /**
     * 测试数据源连接
     */
    private void testConnection(DataSource dataSource, String dataSourceName) throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            if (connection.isValid(5)) {
                log.info("数据源连接测试成功: {}", dataSourceName);
            } else {
                throw new SQLException("数据源连接无效: " + dataSourceName);
            }
        }
    }
    
    /**
     * 获取所有数据源名称
     */
    public Map<String, DataSourceConfig> getAllDataSourceConfigs() {
        return dataSourceProperties.getDatasources();
    }
    
    /**
     * 移除数据源缓存
     */
    public void removeDataSource(String dataSourceName) {
        DataSource dataSource = dataSourceCache.remove(dataSourceName);
        if (dataSource instanceof HikariDataSource) {
            ((HikariDataSource) dataSource).close();
            log.info("已关闭数据源: {}", dataSourceName);
        }
    }
}
