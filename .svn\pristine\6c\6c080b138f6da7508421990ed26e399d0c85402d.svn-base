package com.example.multidatasource.crew;

import com.example.multidatasource.crew.util.CertificateLevelMatcher;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * 证书等级匹配测试
 */
public class CertificateLevelMatcherTest {

    @Test
    public void testCertificateLevelMatching() {
        System.out.println("=== 证书等级匹配测试 ===");

        // 测试用例1：甲一船员可以上所有甲类船舶
        testCase("甲一", "船长", createVesselInfo(2, 5000, 4000), true, "甲一船员可以上甲一船舶");
        testCase("甲一", "船长", createVesselInfo(2, 2000, 4000), true, "甲一船员可以上甲二船舶");

        // 测试用例2：甲二船员不能上甲一船舶
        testCase("甲二", "船长", createVesselInfo(2, 5000, 4000), false, "甲二船员不能上甲一船舶");
        testCase("甲二", "船长", createVesselInfo(2, 2000, 4000), true, "甲二船员可以上甲二船舶");

        // 测试用例3：丙一船员可以上所有丙类船舶
        testCase("丙一", "船长", createVesselInfo(1, 5000, 4000), true, "丙一船员可以上丙一船舶");
        testCase("丙一", "船长", createVesselInfo(1, 2000, 4000), true, "丙一船员可以上丙二船舶");

        // 测试用例4：丙二船员不能上丙一船舶
        testCase("丙二", "船长", createVesselInfo(1, 5000, 4000), false, "丙二船员不能上丙一船舶");
        testCase("丙二", "船长", createVesselInfo(1, 2000, 4000), true, "丙二船员可以上丙二船舶");

        // 测试用例5：证书类别优先级测试（甲 > 乙 > 丙 > 丁）
        testCase("甲类", "船长", createVesselInfo(1, 2000, 4000), true, "甲类船员可以上丙类船舶（优先级高）");
        testCase("甲一", "船长", createVesselInfo(1, 2000, 4000), true, "甲一船员可以上丙类船舶（优先级高）");
        testCase("乙一", "船长", createVesselInfo(1, 2000, 4000), true, "乙一船员可以上丙类船舶（优先级高）");
        testCase("丙类", "船长", createVesselInfo(2, 2000, 4000), false, "丙类船员不能上甲类船舶（优先级低）");
        testCase("丁类", "船长", createVesselInfo(1, 2000, 4000), false, "丁类船员不能上丙类船舶（优先级低）");

        // 测试用例6：轮机长、大管轮基于主机功率判断
        testCase("甲一", "轮机长", createVesselInfo(2, 2000, 5000), true, "甲一轮机长可以上甲一船舶（基于功率）");
        testCase("甲二", "轮机长", createVesselInfo(2, 2000, 5000), false, "甲二轮机长不能上甲一船舶（基于功率）");
        testCase("甲二", "轮机长", createVesselInfo(2, 2000, 2000), true, "甲二轮机长可以上甲二船舶（基于功率）");

        // 测试用例7：非四大头职务的首字精确匹配
        testCase("甲类", "二副", createVesselInfo(2, 5000, 4000), true, "甲类二副可以上甲类船舶");
        testCase("甲类", "二副", createVesselInfo(2, 2000, 4000), true, "甲类二副可以上甲类船舶（不区分等级）");
        testCase("甲类", "二副", createVesselInfo(1, 5000, 4000), false, "甲类二副不能上丙类船舶（首字不匹配）");
        testCase("乙一", "二副", createVesselInfo(1, 5000, 4000), false, "乙一二副不能上丙类船舶（首字不匹配）");
        testCase("丙类", "二副", createVesselInfo(1, 5000, 4000), true, "丙类二副可以上丙类船舶");
        testCase("丙类", "二副", createVesselInfo(2, 5000, 4000), false, "丙类二副不能上甲类船舶（首字不匹配）");
        testCase("丁类", "二副", createVesselInfo(1, 5000, 4000), false, "丁类二副不能上丙类船舶（首字不匹配）");

        System.out.println("=== 测试完成 ===");
    }

    private void testCase(String seafarerLevel, String dutyName, Map<String, Object> vesselInfo, 
                         boolean expected, String description) {
        boolean result = CertificateLevelMatcher.isLevelMatched(seafarerLevel, dutyName, vesselInfo);
        String status = result == expected ? "✓ PASS" : "✗ FAIL";
        System.out.printf("%s - %s: 船员证书=%s, 职务=%s, 船舶=%s, 期望=%s, 实际=%s%n", 
                status, description, seafarerLevel, dutyName, 
                formatVesselInfo(vesselInfo), expected, result);
        
        if (result != expected) {
            System.err.printf("测试失败: %s%n", description);
        }
    }

    private Map<String, Object> createVesselInfo(int navigationArea, double grossTonage, double mainEnginePowerKw) {
        Map<String, Object> vesselInfo = new HashMap<>();
        vesselInfo.put("navigationArea", navigationArea);
        vesselInfo.put("grossTonage", grossTonage);
        vesselInfo.put("mainEnginePowerKw", mainEnginePowerKw);
        return vesselInfo;
    }

    private String formatVesselInfo(Map<String, Object> vesselInfo) {
        if (vesselInfo == null) {
            return "null";
        }

        int navigationArea = (Integer) vesselInfo.get("navigationArea");
        double grossTonage = (Double) vesselInfo.get("grossTonage");
        double mainEnginePowerKw = (Double) vesselInfo.get("mainEnginePowerKw");

        String areaType = navigationArea == 1 ? "丙类" : "甲类";
        String tonnageLevel = grossTonage >= 3000 ? "一类" : "二类";
        String powerLevel = mainEnginePowerKw >= 3000 ? "一类" : "二类";

        return String.format("%s(航区=%d,吨位=%.0f(%s),功率=%.0f(%s))",
                areaType, navigationArea, grossTonage, tonnageLevel, mainEnginePowerKw, powerLevel);
    }

    @Test
    public void testEdgeCases() {
        System.out.println("\n=== 边界情况测试 ===");

        // 测试空值情况
        testCase(null, "船长", createVesselInfo(2, 5000, 4000), false, "船员证书等级为null");
        testCase("甲一", null, createVesselInfo(2, 5000, 4000), false, "职务名称为null");
        testCase("甲一", "船长", null, false, "船舶信息为null");

        // 测试未知证书等级
        testCase("未知等级", "船长", createVesselInfo(2, 5000, 4000), false, "未知证书等级");

        // 测试边界值
        testCase("甲一", "船长", createVesselInfo(2, 3000, 4000), true, "总吨位边界值3000");
        testCase("甲一", "轮机长", createVesselInfo(2, 5000, 3000), true, "主机功率边界值3000");

        System.out.println("=== 边界情况测试完成 ===");
    }

    @Test
    public void testCertificatePriorityMatrix() {
        System.out.println("\n=== 证书类别优先级矩阵测试 ===");
        System.out.println("优先级顺序：甲 > 乙 > 丙 > 丁");

        // 甲类证书测试
        testCase("甲一", "船长", createVesselInfo(2, 5000, 4000), true, "甲一 → 甲一船舶");
        testCase("甲一", "船长", createVesselInfo(1, 5000, 4000), true, "甲一 → 丙一船舶（跨类别向下兼容）");
        testCase("甲类", "二副", createVesselInfo(2, 2000, 2000), true, "甲类 → 甲类船舶（首字匹配）");

        // 乙类证书测试
        testCase("乙一", "船长", createVesselInfo(1, 5000, 4000), true, "乙一 → 丙一船舶（跨类别向下兼容）");
        testCase("乙一", "船长", createVesselInfo(2, 5000, 4000), false, "乙一 → 甲一船舶（不能向上兼容）");

        // 丙类证书测试
        testCase("丙一", "船长", createVesselInfo(1, 5000, 4000), true, "丙一 → 丙一船舶");
        testCase("丙一", "船长", createVesselInfo(2, 5000, 4000), false, "丙一 → 甲一船舶（不能向上兼容）");
        testCase("丙类", "二副", createVesselInfo(1, 2000, 2000), true, "丙类 → 丙类船舶（首字匹配）");

        // 丁类证书测试
        testCase("丁类", "船长", createVesselInfo(1, 2000, 2000), false, "丁类 → 丙二船舶（不能向上兼容）");
        testCase("丁类", "二副", createVesselInfo(1, 2000, 2000), false, "丁类 → 丙类船舶（首字不匹配）");

        // 同级别内部测试
        testCase("甲一", "船长", createVesselInfo(2, 2000, 4000), true, "甲一 → 甲二船舶（同类别向下兼容）");
        testCase("甲二", "船长", createVesselInfo(2, 5000, 4000), false, "甲二 → 甲一船舶（同类别不能向上兼容）");
        testCase("丙一", "船长", createVesselInfo(1, 2000, 4000), true, "丙一 → 丙二船舶（同类别向下兼容）");
        testCase("丙二", "船长", createVesselInfo(1, 5000, 4000), false, "丙二 → 丙一船舶（同类别不能向上兼容）");

        System.out.println("=== 证书类别优先级矩阵测试完成 ===");
    }
}
