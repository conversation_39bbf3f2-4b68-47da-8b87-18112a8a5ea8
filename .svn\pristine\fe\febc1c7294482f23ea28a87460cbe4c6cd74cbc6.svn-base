server:
  port: 54321
  servlet:
    context-path: /multi/source/api

spring:
  application:
    name: multi-datasource-api
  profiles:
    active: prod  # 默认激活开发环境

  # 禁用默认数据源自动配置，使用自定义多数据源配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration

  # Jackson配置 - 保留null字段，确保输出格式一致
  jackson:
    default-property-inclusion: always  # 包含所有属性，包括null值
    date-format: yyyy-MM-dd HH:mm:ss    # 默认日期时间格式
    time-zone: GMT+8                    # 时区设置
    serialization:
      write-null-map-values: true      # 序列化Map中的null值
      write-dates-as-timestamps: false # 不将日期序列化为时间戳
      indent-output: false             # 不缩进输出（生产环境）
    deserialization:
      fail-on-unknown-properties: false # 忽略未知属性

# 通用配置 - 数据源配置在各环境配置文件中

# HikariCP连接池配置
hikari:
  maximum-pool-size: 20
  minimum-idle: 5
  connection-timeout: 30000
  idle-timeout: 600000
  max-lifetime: 1800000

# MyBatis配置 - 支持模块化结构
mybatis:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.example.multidatasource.*.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: true
    auto-mapping-behavior: partial
    default-executor-type: simple
    default-statement-timeout: 25000

# API文档配置
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
    disable-swagger-default-url: true
    display-request-duration: true
    default-models-expand-depth: -1
    url: /v3/api-docs
  info:
    title: 多数据源SQL执行API
    description: 支持多种数据库的SQL执行和结果封装API
    version: 1.0.0
    contact:
      name: API Support
      email: <EMAIL>

# JWT配置
jwt:
  secret: mySecretKey123456789012345678901234567890  # 生产环境请使用更复杂的密钥
  expiration: 604800  # 令牌过期时间（秒,7天和一体化相同）
  issuer: multi-datasource-api  # 令牌发行者

# 日志配置
logging:
  level:
    com.example.multidatasource: DEBUG
    org.springframework.jdbc: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
