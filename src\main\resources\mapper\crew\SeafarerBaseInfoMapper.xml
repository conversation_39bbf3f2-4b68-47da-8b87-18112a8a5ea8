<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.multidatasource.crew.mapper.SeafarerBaseInfoMapper">

    <!-- 查询船舶列表 -->
    <select id="getVesselList" resultType="java.util.Map">
        SELECT 
            vessel_id AS vesselId,
            vessel_name AS vesselName
        FROM crew_seafarer_info 
        WHERE IFNULL(vessel_name,'') != '' 
        AND vessel_name NOT LIKE '%测试%' 
        GROUP BY vessel_id, vessel_name
        ORDER BY vessel_name
    </select>

    <!-- 查询职务列表 -->
    <select id="getCrtDutyList" resultType="java.util.Map">
        SELECT 
            apply_duty_id AS applyDutyId,
            apply_duty_name AS applyDutyName
        FROM crew_seafarer_info 
        WHERE IFNULL(apply_duty_id,'') != '' 
        GROUP BY apply_duty_id, apply_duty_name
        ORDER BY apply_duty_name
    </select>

    <!-- 查询证书等级列表 -->
    <select id="getCrtLevelList" resultType="java.util.Map">
        SELECT 
            crt_level_id AS crtLevelId,
            crt_level_name AS crtLevelName
        FROM crew_seafarer_info 
        WHERE IFNULL(crt_level_id,'') != '' 
        GROUP BY crt_level_id, crt_level_name
        ORDER BY crt_level_name
    </select>

</mapper>
