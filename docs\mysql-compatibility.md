# MySQL版本兼容性说明

## 问题描述

在MySQL 5.5.56版本中执行海事信息管理模块的数据库脚本时，可能会遇到以下错误：

```sql
ERROR: Invalid default value for 'create_time'
ERROR: Invalid default value for 'update_time'
```

## 原因分析

**MySQL版本差异：**

### MySQL 5.5.x 限制
- ❌ `DATETIME` 类型不支持 `DEFAULT CURRENT_TIMESTAMP`
- ❌ `DATETIME` 类型不支持 `ON UPDATE CURRENT_TIMESTAMP`
- ✅ 只有 `TIMESTAMP` 类型支持这些特性

### MySQL 5.6+ 支持
- ✅ `DATETIME` 类型完全支持 `DEFAULT CURRENT_TIMESTAMP`
- ✅ `DATETIME` 类型完全支持 `ON UPDATE CURRENT_TIMESTAMP`
- ✅ `TIMESTAMP` 类型继续支持

## 解决方案

我们已经修改了数据库脚本和实体类以兼容MySQL 5.5+：

### 1. 数据库脚本修改

**修改前（MySQL 5.6+）：**
```sql
CREATE TABLE `maritime_bureau_config` (
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**修改后（MySQL 5.5+兼容）：**
```sql
CREATE TABLE `maritime_bureau_config` (
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. Java实体类修改

**修改前：**
```java
@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
private LocalDateTime createTime;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
private LocalDateTime updateTime;
```

**修改后：**
```java
@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
private Timestamp createTime;

@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
private Timestamp updateTime;
```

## TIMESTAMP vs DATETIME 对比

| 特性 | TIMESTAMP | DATETIME |
|------|-----------|----------|
| **存储范围** | 1970-01-01 00:00:01 UTC ~ 2038-01-19 03:14:07 UTC | 1000-01-01 00:00:00 ~ 9999-12-31 23:59:59 |
| **存储空间** | 4字节 | 8字节 |
| **时区处理** | 自动转换时区 | 不处理时区 |
| **DEFAULT CURRENT_TIMESTAMP** | MySQL 5.5+ 支持 | MySQL 5.6+ 支持 |
| **ON UPDATE CURRENT_TIMESTAMP** | MySQL 5.5+ 支持 | MySQL 5.6+ 支持 |

## 使用建议

### 对于MySQL 5.5用户
- ✅ 使用修改后的脚本（TIMESTAMP类型）
- ✅ 功能完全正常，只是时间类型不同
- ⚠️ 注意TIMESTAMP的2038年问题

### 对于MySQL 5.6+用户
- ✅ 可以使用TIMESTAMP版本（推荐，兼容性好）
- ✅ 也可以改回DATETIME版本（如果需要更大的时间范围）

### 如果需要改回DATETIME（仅MySQL 5.6+）

**数据库脚本：**
```sql
-- 将 timestamp 改为 datetime
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
```

**Java实体类：**
```java
// 将 Timestamp 改为 LocalDateTime
import java.time.LocalDateTime;

private LocalDateTime createTime;
private LocalDateTime updateTime;
```

## 验证方法

### 1. 检查MySQL版本
```sql
SELECT VERSION();
```

### 2. 测试DATETIME支持
```sql
-- 在MySQL 5.5中会报错，5.6+中正常
CREATE TABLE test_datetime (
  id INT PRIMARY KEY,
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 测试TIMESTAMP支持
```sql
-- 在MySQL 5.5+中都正常
CREATE TABLE test_timestamp (
  id INT PRIMARY KEY,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 当前状态

✅ **已完成兼容性修改**
- 数据库脚本使用 `TIMESTAMP` 类型
- Java实体类使用 `java.sql.Timestamp` 类型
- 完全兼容 MySQL 5.5.56+
- JSON序列化格式保持不变
- API接口行为完全一致

## 注意事项

1. **时区处理**：TIMESTAMP会自动处理时区转换，在不同时区的服务器上可能显示不同的时间
2. **2038年问题**：TIMESTAMP类型在2038年会溢出，如果系统需要长期运行，建议升级到MySQL 5.6+并使用DATETIME
3. **数据迁移**：如果从TIMESTAMP改为DATETIME，需要注意数据迁移和时区转换

## 推荐配置

**生产环境推荐：**
- MySQL 5.6+ 使用 DATETIME
- MySQL 5.5.x 使用 TIMESTAMP（当前配置）

**开发环境：**
- 统一使用 TIMESTAMP 以确保兼容性
