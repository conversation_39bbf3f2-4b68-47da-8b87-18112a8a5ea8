package com.example.multidatasource.voyage.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 海事信息实体类
 */
@Data
public class MaritimeInfo {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 文章ID（用于去重）
     */
    private String articleId;
    
    /**
     * 海事局名称
     */
    private String bureauName;
    
    /**
     * 标题
     */
    private String title;
    
    /**
     * 信息类型：ALARM-航行警告，NOTICE-航行通告
     */
    private String infoType;
    
    /**
     * 发布日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate publishDate;
    
    /**
     * 详情链接
     */
    private String url;
    
    /**
     * 详细内容
     */
    private String detailContent;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    /**
     * 信息类型枚举
     */
    public static class InfoType {
        public static final String ALARM = "ALARM";
        public static final String NOTICE = "NOTICE";
    }
}
