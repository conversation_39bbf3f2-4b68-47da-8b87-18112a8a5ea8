package com.example.multidatasource.voyage.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 海事信息爬取结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "海事信息爬取结果")
public class MaritimeCrawlResultDTO {
    
    @Schema(description = "爬取总数")
    @Builder.Default
    private Integer totalCount = 0;

    @Schema(description = "成功数量")
    @Builder.Default
    private Integer successCount = 0;

    @Schema(description = "失败数量")
    @Builder.Default
    private Integer failureCount = 0;

    @Schema(description = "新增数量")
    @Builder.Default
    private Integer newCount = 0;

    @Schema(description = "更新数量")
    @Builder.Default
    private Integer updateCount = 0;

    @Schema(description = "是否成功")
    @Builder.Default
    private Boolean success = true;

    @Schema(description = "失败数量")
    @Builder.Default
    private Integer failCount = 0;

    @Schema(description = "消息")
    private String message;

    @Schema(description = "处理详情")
    private List<BureauCrawlDetail> details;
    
    @Data
    @Schema(description = "海事局爬取详情")
    public static class BureauCrawlDetail {
        
        @Schema(description = "海事局名称")
        private String bureauName;
        
        @Schema(description = "信息类型")
        private String infoType;
        
        @Schema(description = "爬取状态：SUCCESS-成功，FAILURE-失败")
        private String status;
        
        @Schema(description = "爬取数量")
        private Integer count = 0;
        
        @Schema(description = "错误信息（失败时）")
        private String errorMessage;
    }
}
