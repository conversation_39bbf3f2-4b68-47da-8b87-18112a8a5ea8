# Swagger API文档使用指南

## 🔗 访问地址
- **Swagger UI**: http://localhost:8080/api/swagger-ui.html
- **OpenAPI JSON**: http://localhost:8080/api/v3/api-docs

## 🔐 身份认证配置

### 1. 获取JWT令牌
首先通过登录接口获取JWT令牌：

1. 展开 **身份认证** 分组
2. 点击 **POST /auth/login** 接口
3. 点击 "Try it out" 按钮
4. 输入登录信息：
   ```json
   {
     "username": "admin",
     "password": "admin123",
     "rememberMe": false
   }
   ```
5. 点击 "Execute" 执行
6. 从响应中复制 `accessToken` 的值

### 2. 配置认证令牌
1. 点击页面右上角的 **🔒 Authorize** 按钮
2. 在弹出的对话框中输入：`Bearer <your-jwt-token>`
   - 注意：必须包含 "Bearer " 前缀
   - 例如：`Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
3. 点击 "Authorize" 按钮
4. 关闭对话框

### 3. 验证认证状态
配置完成后，需要认证的接口旁边会显示 🔒 图标，表示已经配置了认证信息。

## 📋 API分组说明

### 🔐 身份认证 (auth-controller)
- **公开接口**：无需认证即可访问
- **主要功能**：用户登录、注册、令牌验证
- **常用接口**：
  - `POST /auth/login` - 用户登录
  - `POST /auth/register` - 用户注册
  - `GET /auth/validate` - 验证令牌

### 👥 用户管理 (user-controller)
- **需要认证**：必须先登录获取令牌
- **权限要求**：部分接口需要管理员权限
- **主要功能**：用户信息管理、密码修改
- **常用接口**：
  - `GET /users/list` - 获取用户列表（管理员）
  - `PUT /users/{id}/password` - 修改密码

### 🚢 船管系统 (crew-controller)
- **需要认证**：必须先登录获取令牌
- **主要功能**：船员信息管理
- **常用接口**：
  - `GET /crew/crew-list` - 获取船员列表
  - `POST /crew/crew/search` - 多条件查询船员
  - `POST /crew/crew` - 添加船员

### 🛳️ 航次管理 (voyage-controller)
- **需要认证**：必须先登录获取令牌
- **主要功能**：航次计划管理
- **常用接口**：
  - `GET /voyage/voyage-list` - 获取航次列表
  - `POST /voyage/voyage/search` - 多条件查询航次
  - `POST /voyage/voyage` - 创建航次

### 💾 通用SQL (sql-executor-controller)
- **需要认证**：必须先登录获取令牌
- **主要功能**：动态SQL执行
- **常用接口**：
  - `POST /sql/execute` - 执行SQL语句

### 🏥 系统健康 (health-controller)
- **公开接口**：无需认证即可访问
- **主要功能**：系统状态检查
- **常用接口**：
  - `GET /health/status` - 系统状态
  - `GET /datasource/status` - 数据源状态

## 🎯 使用技巧

### 1. 快速测试流程
1. **登录获取令牌** → `POST /auth/login`
2. **配置认证** → 点击 Authorize 按钮
3. **测试业务接口** → 选择任意需要认证的接口

### 2. 常见错误处理
- **401 Unauthorized**：令牌无效或已过期，需要重新登录
- **403 Forbidden**：权限不足，检查用户角色
- **400 Bad Request**：请求参数错误，检查输入格式

### 3. 参数说明
- **路径参数**：直接在URL中的参数，如 `/users/{id}` 中的 `id`
- **查询参数**：URL后的参数，如 `?page=1&size=10`
- **请求体**：JSON格式的请求数据

### 4. 响应格式
所有接口都使用统一的响应格式：
```json
{
  "code": 200,           // 状态码：200-成功，其他-失败
  "message": "success",  // 响应消息
  "data": {}            // 响应数据
}
```

## 🔄 令牌管理

### 令牌过期处理
- **过期时间**：默认1小时
- **刷新令牌**：使用 `POST /auth/refresh` 接口
- **重新登录**：令牌无法刷新时需要重新登录

### 安全建议
1. **不要在生产环境中使用默认密码**
2. **定期更换密码**
3. **及时清理过期令牌**
4. **不要在URL中传递敏感信息**

## 📖 接口文档特性

### 自动生成
- **实时更新**：代码变更后文档自动更新
- **类型安全**：参数类型和格式自动验证
- **示例数据**：提供真实的请求和响应示例

### 交互功能
- **在线测试**：直接在文档中测试接口
- **参数验证**：自动验证必填参数和格式
- **响应预览**：实时查看接口响应结果

### 导出功能
- **OpenAPI规范**：支持导出标准的OpenAPI JSON
- **代码生成**：可用于生成客户端SDK
- **文档分享**：可导出为静态文档分享
