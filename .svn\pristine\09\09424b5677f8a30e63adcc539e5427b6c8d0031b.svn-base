package com.example.multidatasource.voyage;

import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 海事局爬虫测试类
 * 用于验证Java版本爬虫逻辑的正确性
 */
public class MaritimeCrawlerTest {

    private static final String BASE_URL = "https://www.msa.gov.cn";
    private static final String API_URL = "https://www.msa.gov.cn/page/openInfo/articleList.do";
    
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;

    public MaritimeCrawlerTest() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 主方法，用于直接运行测试
     */
    public static void main(String[] args) {
        MaritimeCrawlerTest test = new MaritimeCrawlerTest();
        test.testShanghaiAlarmCrawl();
        test.testShanghaiNoticeCrawl();
    }

    /**
     * 测试上海海事局航行警告爬取
     */
    @Test
    public void testShanghaiAlarmCrawl() {
        String bureau = "上海海事局";
        String channelId = "94DF14CE-1110-415D-A44E-67593E76619F";
        String infoType = "ALARM";

        System.out.println("=== 测试 " + bureau + " " + infoType + " ===");

        try {
            // 扩大到3天，确保能获取到数据
            List<Map<String, Object>> result = crawlMaritimeInfo(bureau, channelId, infoType, 3);

            System.out.println("爬取结果数量: " + result.size());
            for (Map<String, Object> item : result) {
                System.out.println("标题: " + item.get("title"));
                System.out.println("日期: " + item.get("date"));
                System.out.println("URL: " + item.get("url"));
                System.out.println("详细内容: " + item.get("detail_content"));
                System.out.println("---");
            }

        } catch (Exception e) {
            System.err.println("爬取失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试上海海事局航行通告爬取
     */
    @Test
    public void testShanghaiNoticeCrawl() {
        String bureau = "上海海事局";
        String channelId = "8DBDED82-F3E5-413B-824E-51445C79726C";
        String infoType = "NOTICE";

        System.out.println("=== 测试 " + bureau + " " + infoType + " ===");

        try {
            // 扩大到3天，确保能获取到数据
            List<Map<String, Object>> result = crawlMaritimeInfo(bureau, channelId, infoType, 3);

            System.out.println("爬取结果数量: " + result.size());
            for (Map<String, Object> item : result) {
                System.out.println("标题: " + item.get("title"));
                System.out.println("日期: " + item.get("date"));
                System.out.println("URL: " + item.get("url"));
                System.out.println("详细内容: " + item.get("detail_content"));
                System.out.println("---");
            }

        } catch (Exception e) {
            System.err.println("爬取失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 爬取海事信息的核心方法
     */
    public List<Map<String, Object>> crawlMaritimeInfo(String bureau, String channelId, String infoType, int days) throws IOException {
        List<Map<String, Object>> result = new ArrayList<>();
        
        // 构建请求URL
        String url = API_URL + "?channelId=" + channelId + "&pageSize=20&pageNo=1&isParent=0";
        
        // 创建HTTP请求
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Cache-Control", "max-age=0")
                .addHeader("Connection", "keep-alive")
                .addHeader("Referer", "https://www.bing.com/")
                .addHeader("Sec-Fetch-Dest", "document")
                .addHeader("Sec-Fetch-Mode", "navigate")
                .addHeader("Sec-Fetch-Site", "cross-site")
                .addHeader("Sec-Fetch-User", "?1")
                .addHeader("Upgrade-Insecure-Requests", "1")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
                .addHeader("sec-ch-ua", "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"")
                .addHeader("sec-ch-ua-mobile", "?0")
                .addHeader("sec-ch-ua-platform", "\"Windows\"")
                .build();

        // 执行请求
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("HTTP请求失败: " + response.code());
            }
            
            String html = response.body().string();
            System.out.println("获取到HTML内容，长度: " + html.length());
            
            // 解析HTML
            result = parseMaritimeList(html, bureau, infoType, days);
        }
        
        return result;
    }

    /**
     * 解析海事信息列表页面
     */
    private List<Map<String, Object>> parseMaritimeList(String html, String bureau, String infoType, int days) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            Document doc = Jsoup.parse(html);
            
            // 查找主要的ul列表容器
            Elements ulElements = doc.select("ul.main_list_ul");
            if (ulElements.isEmpty()) {
                System.out.println("未找到主列表容器");
                return result;
            }
            
            Element ulElement = ulElements.first();
            Elements liElements = ulElement.select("li.main_list_li");
            
            System.out.println("找到 " + liElements.size() + " 个列表项");
            
            for (Element li : liElements) {
                try {
                    Map<String, Object> item = parseListItem(li, bureau, infoType, days);
                    if (item != null) {
                        result.add(item);
                    }
                } catch (Exception e) {
                    System.err.println("解析列表项失败: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            System.err.println("解析HTML失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 解析单个列表项
     */
    private Map<String, Object> parseListItem(Element li, String bureau, String infoType, int days) {
        try {
            // 提取链接
            Elements linkElements = li.select("a[href]");
            if (linkElements.isEmpty()) {
                return null;
            }
            
            Element linkElement = linkElements.first();
            String href = linkElement.attr("href");
            
            // 构建完整URL
            String fullUrl;
            if (href.startsWith("/")) {
                fullUrl = BASE_URL + href;
            } else {
                fullUrl = href;
            }
            
            // 提取标题和日期
            Elements spanElements = linkElement.select("span");
            if (spanElements.size() < 2) {
                return null;
            }
            
            String title = cleanText(spanElements.get(0).text());
            String dateText = cleanText(spanElements.get(1).text());
            
            // 清理日期格式
            String cleanDate = dateText.replaceAll("[\\[\\]\\s]", "");
            
            // 检查日期是否在指定天数内
            if (!isWithinDays(cleanDate, days)) {
                return null;
            }
            
            // 获取详细内容
            String detailContent = fetchArticleDetail(fullUrl);
            
            // 构建结果对象
            Map<String, Object> item = new HashMap<>();
            item.put("bureau", bureau);
            item.put("title", title);
            item.put("date", cleanDate);
            item.put("url", fullUrl);
            item.put("type", infoType.equals("ALARM") ? "航行警告" : "航行通告");
            item.put("detail_content", detailContent);
            
            return item;
            
        } catch (Exception e) {
            System.err.println("解析列表项失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取文章详细内容
     */
    private String fetchArticleDetail(String url) {
        try {
            System.out.println("正在获取详情: " + url);
            
            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    return "获取详情失败: HTTP " + response.code();
                }
                
                String html = response.body().string();
                return extractContentFromHtml(html);
            }
            
        } catch (Exception e) {
            System.err.println("获取详情失败: " + e.getMessage());
            return "获取详情失败: " + e.getMessage();
        }
    }

    /**
     * 从HTML中提取内容
     */
    private String extractContentFromHtml(String html) {
        try {
            Document doc = Jsoup.parse(html);
            
            // 尝试多种内容选择器
            String[] contentSelectors = {
                "#ch_p",
                ".content",
                "#content", 
                ".main-content"
            };
            
            for (String selector : contentSelectors) {
                Elements elements = doc.select(selector);
                if (!elements.isEmpty()) {
                    String content = elements.first().text();
                    return cleanText(content);
                }
            }
            
            return "无法提取详细内容";
            
        } catch (Exception e) {
            return "内容解析失败: " + e.getMessage();
        }
    }

    /**
     * 清理文本内容
     */
    private String cleanText(String text) {
        if (text == null) {
            return "";
        }
        
        // 去除不需要的功能按钮文本
        String[] unwantedTexts = {"收藏", "打印本页", "关闭窗口", "下载PDF文件"};
        for (String unwanted : unwantedTexts) {
            text = text.replace(unwanted, "");
        }
        
        // 清理多余的空白字符
        text = text.replaceAll("\\s+", " ");
        
        return text.trim();
    }

    /**
     * 检查日期是否在指定天数内
     */
    private boolean isWithinDays(String dateStr, int days) {
        try {
            if (dateStr.length() != 10 || !dateStr.contains("-")) {
                return false;
            }
            
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDate today = LocalDate.now();
            LocalDate cutoffDate = today.minusDays(days - 1);
            
            return !date.isBefore(cutoffDate);
            
        } catch (DateTimeParseException e) {
            System.err.println("日期解析失败: " + dateStr);
            return false;
        }
    }
}
