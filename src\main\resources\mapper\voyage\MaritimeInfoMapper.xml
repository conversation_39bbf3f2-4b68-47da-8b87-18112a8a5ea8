<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.multidatasource.voyage.mapper.MaritimeInfoMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.multidatasource.voyage.entity.MaritimeInfo">
        <id column="id" property="id"/>
        <result column="article_id" property="articleId"/>
        <result column="bureau_name" property="bureauName"/>
        <result column="title" property="title"/>
        <result column="info_type" property="infoType"/>
        <result column="publish_date" property="publishDate"/>
        <result column="url" property="url"/>
        <result column="detail_content" property="detailContent"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, article_id, bureau_name, title, info_type, publish_date, url, detail_content, create_time, update_time
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Where_Clause">
        <where>
            <if test="query.bureauName != null and query.bureauName != ''">
                AND bureau_name = #{query.bureauName}
            </if>
            <if test="query.infoType != null and query.infoType != ''">
                AND info_type = #{query.infoType}
            </if>
            <if test="query.startDate != null">
                AND publish_date &gt;= #{query.startDate}
            </if>
            <if test="query.endDate != null">
                AND publish_date &lt;= #{query.endDate}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                <![CDATA[
                AND (title LIKE CONCAT('%', #{query.keyword}, '%')
                     OR detail_content LIKE CONCAT('%', #{query.keyword}, '%'))
                ]]>
            </if>
        </where>
    </sql>

    <!-- 分页查询海事信息 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM maritime_info
        <include refid="Query_Where_Clause"/>
        ORDER BY publish_date DESC, create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计查询结果总数 -->
    <select id="countByQuery" resultType="int">
        SELECT COUNT(1)
        FROM maritime_info
        <include refid="Query_Where_Clause"/>
    </select>

    <!-- 根据文章ID查询 -->
    <select id="selectByArticleId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM maritime_info
        WHERE article_id = #{articleId}
    </select>

    <!-- 根据ID查询 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM maritime_info
        WHERE id = #{id}
    </select>

    <!-- 插入海事信息 -->
    <insert id="insert" parameterType="com.example.multidatasource.voyage.entity.MaritimeInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO maritime_info (
            article_id, bureau_name, title, info_type, publish_date, url, detail_content, create_time, update_time
        ) VALUES (
            #{articleId}, #{bureauName}, #{title}, #{infoType}, #{publishDate}, #{url}, #{detailContent}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 更新海事信息 -->
    <update id="updateById" parameterType="com.example.multidatasource.voyage.entity.MaritimeInfo">
        UPDATE maritime_info
        SET bureau_name = #{bureauName},
            title = #{title},
            info_type = #{infoType},
            publish_date = #{publishDate},
            url = #{url},
            detail_content = #{detailContent},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 根据文章ID更新 -->
    <update id="updateByArticleId" parameterType="com.example.multidatasource.voyage.entity.MaritimeInfo">
        UPDATE maritime_info
        SET bureau_name = #{bureauName},
            title = #{title},
            info_type = #{infoType},
            publish_date = #{publishDate},
            url = #{url},
            detail_content = #{detailContent},
            update_time = #{updateTime}
        WHERE article_id = #{articleId}
    </update>

    <!-- 删除海事信息 -->
    <delete id="deleteById">
        DELETE FROM maritime_info WHERE id = #{id}
    </delete>

    <!-- 批量插入海事信息 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO maritime_info (
            article_id, bureau_name, title, info_type, publish_date, url, detail_content, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.articleId}, #{item.bureauName}, #{item.title}, #{item.infoType}, 
             #{item.publishDate}, #{item.url}, #{item.detailContent}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

</mapper>
