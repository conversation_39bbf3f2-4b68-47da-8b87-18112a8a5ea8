package com.example.multidatasource.voyage.controller;

import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.common.config.DataSourceContextHolder;
import com.example.multidatasource.common.dto.CargoCompanyStatsQueryDTO;
import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.common.dto.VoyageQueryDTO;
import com.example.multidatasource.model.ApiResponse;
import com.example.multidatasource.model.SqlRequest;
import com.example.multidatasource.model.SqlResponse;
import com.example.multidatasource.service.SqlExecutorService;
import com.example.multidatasource.voyage.entity.CargoCompanyStats;
import com.example.multidatasource.voyage.entity.VoyageInfo;
import com.example.multidatasource.voyage.service.VoyageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 航次动态管理Controller
 * 专门处理航次管理相关的数据库操作
 */
@RestController
@RequestMapping("/voyage")
@Tag(name = "航次动态管理", description = "航次管理相关API")
@DataSource(DataSourceContextHolder.DataSourceType.VOYAGE)
public class VoyageController {

    @Autowired
    private VoyageService voyageService;
    
    @Autowired
    private SqlExecutorService sqlExecutorService;

    @PostMapping("/execute")
    @Operation(summary = "执行航次管理SQL", description = "在航次管理数据库中执行SQL语句")
    public ApiResponse<Object> executeSql(@Valid @RequestBody SqlRequest request) {
        try {
            // 确保使用voyage数据源
            request.setDataSourceName("voyage");
            SqlResponse result = sqlExecutorService.executeQuery(request);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("SQL执行失败: " + e.getMessage());
        }
    }

    @GetMapping("/voyage-list")
    @Operation(summary = "分页获取航次列表", description = "分页查询所有航次信息")
    public ApiResponse<PageResult<VoyageInfo>> getVoyageList(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") int size) {
        try {
            PageResult<VoyageInfo> result = voyageService.getVoyageList(page, size);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("查询航次列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/voyage/{id}")
    @Operation(summary = "获取航次详情", description = "根据ID查询航次详细信息")
    public ApiResponse<VoyageInfo> getVoyageById(
            @Parameter(description = "航次ID") @PathVariable String id) {
        try {
            VoyageInfo voyage = voyageService.getVoyageById(id);
            if (voyage != null) {
                return ApiResponse.success(voyage);
            } else {
                return ApiResponse.error("航次不存在");
            }
        } catch (Exception e) {
            return ApiResponse.error("查询航次详情失败: " + e.getMessage());
        }
    }

    @PostMapping("/voyage")
    @Operation(summary = "创建航次", description = "创建新的航次计划")
    public ApiResponse<String> createVoyage(@RequestBody VoyageInfo voyageInfo) {
        try {
            boolean success = voyageService.createVoyage(voyageInfo);
            if (success) {
                return ApiResponse.success("航次创建成功");
            } else {
                return ApiResponse.error("创建航次失败");
            }
        } catch (Exception e) {
            return ApiResponse.error("创建航次失败: " + e.getMessage());
        }
    }

    @PutMapping("/voyage/{id}")
    @Operation(summary = "更新航次信息", description = "根据ID更新航次信息")
    public ApiResponse<String> updateVoyage(
            @Parameter(description = "航次ID") @PathVariable Long id,
            @RequestBody VoyageInfo voyageInfo) {
        try {
            voyageInfo.setId(id);
            boolean success = voyageService.updateVoyage(voyageInfo);
            if (success) {
                return ApiResponse.success("航次信息更新成功");
            } else {
                return ApiResponse.error("更新航次信息失败，航次不存在");
            }
        } catch (Exception e) {
            return ApiResponse.error("更新航次信息失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/voyage/{id}")
    @Operation(summary = "删除航次", description = "根据ID删除航次信息")
    public ApiResponse<String> deleteVoyage(@Parameter(description = "航次ID") @PathVariable Long id) {
        try {
            boolean success = voyageService.deleteVoyage(id);
            if (success) {
                return ApiResponse.success("航次删除成功");
            } else {
                return ApiResponse.error("删除航次失败，航次不存在");
            }
        } catch (Exception e) {
            return ApiResponse.error("删除航次失败: " + e.getMessage());
        }
    }

    @PostMapping("/voyage/search")
    @Operation(summary = "多条件查询航次", description = "根据多个条件查询航次信息")
    public ApiResponse<PageResult<VoyageInfo>> searchVoyage(@RequestBody VoyageQueryDTO queryDTO) {
        try {
            PageResult<VoyageInfo> result = voyageService.searchVoyage(queryDTO);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("查询航次失败: " + e.getMessage());
        }
    }

    @GetMapping("/voyage/status/{status}")
    @Operation(summary = "按状态查询航次", description = "根据状态查询航次列表")
    public ApiResponse<List<VoyageInfo>> getVoyagesByStatus(
            @Parameter(description = "航次状态") @PathVariable String status) {
        try {
            List<VoyageInfo> result = voyageService.getVoyageByStatus(status);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("查询航次失败: " + e.getMessage());
        }
    }

    @PutMapping("/voyage/{id}/status")
    @Operation(summary = "更新航次状态", description = "更新指定航次的状态")
    public ApiResponse<String> updateVoyageStatus(
            @Parameter(description = "航次ID") @PathVariable Long id,
            @Parameter(description = "新状态") @RequestParam String status) {
        try {
            boolean success = voyageService.updateVoyageStatus(id, status);
            if (success) {
                return ApiResponse.success("航次状态更新成功");
            } else {
                return ApiResponse.error("更新航次状态失败");
            }
        } catch (Exception e) {
            return ApiResponse.error("更新航次状态失败: " + e.getMessage());
        }
    }

    @GetMapping("/voyage/ships")
    @Operation(summary = "获取所有船舶名称", description = "获取系统中所有的船舶名称列表")
    public ApiResponse<List<String>> getAllShipNames() {
        try {
            List<String> ships = voyageService.getAllShipNames();
            return ApiResponse.success(ships);
        } catch (Exception e) {
            return ApiResponse.error("获取船舶列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/voyage/ports")
    @Operation(summary = "获取所有港口列表", description = "获取系统中所有的港口列表")
    public ApiResponse<List<String>> getAllPorts() {
        try {
            List<String> ports = voyageService.getAllPorts();
            return ApiResponse.success(ports);
        } catch (Exception e) {
            return ApiResponse.error("获取港口列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/voyage/count")
    @Operation(summary = "获取航次总数", description = "统计系统中的航次总数")
    public ApiResponse<Integer> getVoyageCount() {
        try {
            int count = voyageService.getVoyageCount();
            return ApiResponse.success(count);
        } catch (Exception e) {
            return ApiResponse.error("获取航次总数失败: " + e.getMessage());
        }
    }

    @PostMapping("/cargo-company-stats")
    @Operation(summary = "查询货主关联信息统计", description = "查询货主公司的货物、船舶、装卸量等关联统计信息")
    public ApiResponse<PageResult<CargoCompanyStats>> getCargoCompanyStats(@RequestBody CargoCompanyStatsQueryDTO queryDTO) {
        try {
            PageResult<CargoCompanyStats> result = voyageService.getCargoCompanyStats(queryDTO);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("查询货主关联信息失败: " + e.getMessage());
        }
    }
}
