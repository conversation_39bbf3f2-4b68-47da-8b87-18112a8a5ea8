package com.example.multidatasource.crew.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 航次油耗汇总DTO
 */
@Data
@Schema(description = "航次油耗汇总信息")
public class VoyageConsumptionSummaryDTO {

    @Schema(description = "船舶ID")
    private String vesselId;

    @Schema(description = "船舶名称")
    private String vesselName;

    @Schema(description = "制单日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate fillDate;

    @Schema(description = "记录ID")
    private String consumId;

    @Schema(description = "mmsi编码")
    private String mmsiCode;

    @Schema(description = "油耗明细列表")
    private List<OilConsumptionDetailDTO> detailList;

    /**
     * 油耗明细DTO
     */
    @Data
    @Schema(description = "油耗明细信息")
    public static class OilConsumptionDetailDTO {

        @Schema(description = "油品ID")
        private String oilId;

        @Schema(description = "油品中文名称")
        private String oilNameCn;

        @Schema(description = "油品英文名称")
        private String oilNameEn;

        @Schema(description = "油品牌号")
        private String oilMark;

        @Schema(description = "油品类型")
        private String oilTypeId;

        @Schema(description = "燃油类型ID（1.重油 2.轻油 3.其他）")
        private String fuelTypeId;

        @Schema(description = "加装单位")
        private String oilUnitId;

        @Schema(description = "加装单位中文名")
        private String oilUnitName;

        @Schema(description = "上航次结存")
        private String thisVoyageInventory;
    }
}
