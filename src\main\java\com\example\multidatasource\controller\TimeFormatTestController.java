package com.example.multidatasource.controller;

import com.example.multidatasource.model.ApiResponse;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;

/**
 * 时间格式测试Controller
 */
@RestController
@RequestMapping("/test")
@Tag(name = "时间格式测试", description = "测试不同时间类型的格式化输出")
public class TimeFormatTestController {

    @GetMapping("/time-format")
    @Operation(summary = "测试时间格式", description = "测试默认时间格式和自定义时间格式")
    public ApiResponse<TimeFormatTestDTO> testTimeFormat() {
        TimeFormatTestDTO dto = new TimeFormatTestDTO();
        
        // 设置当前时间
        dto.setCurrentDateTime(LocalDateTime.now());
        dto.setCurrentDate(LocalDate.now());
        dto.setCurrentTime(LocalTime.now());
        dto.setUtilDate(new Date());
        
        // 设置自定义格式的时间
        dto.setCustomFormatDate(LocalDate.now());
        dto.setCustomFormatDateTime(LocalDateTime.now());
        
        return ApiResponse.success(dto);
    }

    /**
     * 时间格式测试DTO
     */
    @Data
    @Schema(description = "时间格式测试数据")
    @JsonInclude(JsonInclude.Include.ALWAYS)
    public static class TimeFormatTestDTO {
        
        @Schema(description = "默认格式的LocalDateTime")
        private LocalDateTime currentDateTime;
        
        @Schema(description = "默认格式的LocalDate")
        private LocalDate currentDate;
        
        @Schema(description = "默认格式的LocalTime")
        private LocalTime currentTime;
        
        @Schema(description = "默认格式的java.util.Date")
        private Date utilDate;
        
        @Schema(description = "自定义格式的日期（只显示年月日）")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private LocalDate customFormatDate;
        
        @Schema(description = "自定义格式的日期时间（ISO格式）")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime customFormatDateTime;
        
        @Schema(description = "null值测试")
        private LocalDateTime nullDateTime;
    }
}
