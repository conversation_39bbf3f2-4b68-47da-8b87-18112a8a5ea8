package com.example.multidatasource.voyage.service.impl;

import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.common.config.DataSourceContextHolder;
import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.voyage.dto.MaritimeCrawlRequestDTO;
import com.example.multidatasource.voyage.dto.MaritimeCrawlResultDTO;
import com.example.multidatasource.voyage.dto.MaritimeQueryDTO;
import com.example.multidatasource.voyage.entity.MaritimeBureauConfig;
import com.example.multidatasource.voyage.entity.MaritimeInfo;
import com.example.multidatasource.voyage.mapper.MaritimeBureauConfigMapper;
import com.example.multidatasource.voyage.mapper.MaritimeInfoMapper;
import com.example.multidatasource.voyage.service.MaritimeCrawlerService;
import com.example.multidatasource.voyage.service.MaritimeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;


import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 海事信息服务实现类
 */
@Slf4j
@Service
@DataSource(DataSourceContextHolder.DataSourceType.VOYAGE)
public class MaritimeServiceImpl implements MaritimeService {

    @Autowired
    private MaritimeBureauConfigMapper bureauConfigMapper;
    
    @Autowired
    private MaritimeInfoMapper maritimeInfoMapper;
    
    @Autowired
    private MaritimeCrawlerService crawlerService;

    @Override
    @Transactional
    public MaritimeCrawlResultDTO crawlMaritimeInfo(MaritimeCrawlRequestDTO request) {
        log.info("开始爬取海事信息，请求参数: {}", request);
        
        MaritimeCrawlResultDTO result = new MaritimeCrawlResultDTO();
        List<MaritimeCrawlResultDTO.BureauCrawlDetail> details = new ArrayList<>();
        result.setDetails(details);
        
        try {
            // 获取要爬取的海事局配置
            List<MaritimeBureauConfig> configs = getBureauConfigsForCrawl(request);
            
            // 获取要爬取的信息类型
            List<String> infoTypes = getInfoTypesForCrawl(request);
            
            // 获取爬取天数
            int days = request.getDays() != null ? request.getDays() : 1;
            
            log.info("准备爬取 {} 个海事局，{} 种信息类型，最近 {} 天的数据", 
                    configs.size(), infoTypes.size(), days);
            
            // 遍历海事局和信息类型进行爬取
            for (MaritimeBureauConfig config : configs) {
                for (String infoType : infoTypes) {
                    MaritimeCrawlResultDTO.BureauCrawlDetail detail = 
                            crawlSingleBureauInfo(config, infoType, days);
                    details.add(detail);
                    
                    // 更新统计信息
                    result.setTotalCount(result.getTotalCount() + detail.getCount());
                    if ("SUCCESS".equals(detail.getStatus())) {
                        result.setSuccessCount(result.getSuccessCount() + detail.getCount());
                    } else {
                        result.setFailureCount(result.getFailureCount() + detail.getCount());
                    }
                }
            }
            
            log.info("爬取完成，总计: {}, 成功: {}, 失败: {}", 
                    result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());
            
        } catch (Exception e) {
            log.error("爬取海事信息失败", e);
            throw new RuntimeException("爬取海事信息失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 爬取单个海事局的信息
     */
    private MaritimeCrawlResultDTO.BureauCrawlDetail crawlSingleBureauInfo(
            MaritimeBureauConfig config, String infoType, int days) {
        
        MaritimeCrawlResultDTO.BureauCrawlDetail detail = new MaritimeCrawlResultDTO.BureauCrawlDetail();
        detail.setBureauName(config.getBureauName());
        detail.setInfoType(infoType);
        detail.setStatus("SUCCESS");
        detail.setCount(0);
        
        try {
            // 获取对应的channelId
            String channelId = getChannelId(config, infoType);
            if (channelId == null) {
                detail.setStatus("FAILURE");
                detail.setErrorMessage("该海事局没有配置" + infoType + "类型的channelId");
                log.warn("海事局 {} 没有配置 {} 类型的channelId", config.getBureauName(), infoType);
                return detail;
            }
            
            // 执行爬取
            List<MaritimeInfo> crawledData = crawlerService.crawlMaritimeInfo(
                    config.getBureauName(), channelId, infoType, days);
            
            log.info("海事局 {} {} 类型爬取到 {} 条数据", 
                    config.getBureauName(), infoType, crawledData.size());
            
            // 保存数据
            int newCount = 0;
            int updateCount = 0;
            
            for (MaritimeInfo info : crawledData) {
                MaritimeInfo existing = maritimeInfoMapper.selectByArticleId(info.getArticleId());
                if (existing == null) {
                    // 新增
                    info.setCreateTime(LocalDateTime.now());
                    info.setUpdateTime(LocalDateTime.now());
                    maritimeInfoMapper.insert(info);
                    newCount++;
                } else {
                    // 更新
                    info.setId(existing.getId());
                    info.setCreateTime(existing.getCreateTime()); // 保持原创建时间
                    info.setUpdateTime(LocalDateTime.now());
                    maritimeInfoMapper.updateByArticleId(info);
                    updateCount++;
                }
            }
            
            detail.setCount(crawledData.size());
            log.info("海事局 {} {} 类型保存完成，新增: {}, 更新: {}", 
                    config.getBureauName(), infoType, newCount, updateCount);
            
        } catch (Exception e) {
            detail.setStatus("FAILURE");
            detail.setErrorMessage(e.getMessage());
            log.error("爬取海事局 {} {} 类型数据失败", config.getBureauName(), infoType, e);
        }
        
        return detail;
    }

    /**
     * 获取channelId
     */
    private String getChannelId(MaritimeBureauConfig config, String infoType) {
        if (MaritimeInfo.InfoType.ALARM.equals(infoType)) {
            return config.getAlarmChannelId();
        } else if (MaritimeInfo.InfoType.NOTICE.equals(infoType)) {
            return config.getNoticeChannelId();
        }
        return null;
    }

    /**
     * 获取要爬取的海事局配置
     */
    private List<MaritimeBureauConfig> getBureauConfigsForCrawl(MaritimeCrawlRequestDTO request) {
        if (CollectionUtils.isEmpty(request.getBureauNames())) {
            // 获取所有启用的海事局
            return bureauConfigMapper.selectEnabledConfigs();
        } else {
            // 获取指定的海事局
            return bureauConfigMapper.selectByBureauNames(request.getBureauNames());
        }
    }

    /**
     * 获取要爬取的信息类型
     */
    private List<String> getInfoTypesForCrawl(MaritimeCrawlRequestDTO request) {
        if (CollectionUtils.isEmpty(request.getInfoTypes())) {
            // 默认爬取两种类型
            return Arrays.asList(MaritimeInfo.InfoType.ALARM, MaritimeInfo.InfoType.NOTICE);
        } else {
            return request.getInfoTypes();
        }
    }

    @Override
    public PageResult<MaritimeInfo> queryMaritimeInfo(MaritimeQueryDTO query) {
        log.info("分页查询海事信息，查询条件: {}", query);
        
        try {
            // 计算分页参数
            int offset = (query.getPage() - 1) * query.getSize();
            
            // 查询数据
            List<MaritimeInfo> records = maritimeInfoMapper.selectPage(query, offset, query.getSize());
            int total = maritimeInfoMapper.countByQuery(query);
            
            // 构建分页结果
            PageResult<MaritimeInfo> result = new PageResult<>();
            result.setRecords(records);
            result.setTotal((long) total);
            result.setCurrent(query.getPage());
            result.setSize(query.getSize());
            result.setPages((int) Math.ceil((double) total / query.getSize()));
            
            log.info("查询完成，总计: {} 条，当前页: {} 条", total, records.size());
            return result;
            
        } catch (Exception e) {
            log.error("查询海事信息失败", e);
            throw new RuntimeException("查询海事信息失败: " + e.getMessage());
        }
    }

    @Override
    public MaritimeInfo getMaritimeInfoById(Long id) {
        log.info("根据ID查询海事信息详情: {}", id);
        
        try {
            MaritimeInfo result = maritimeInfoMapper.selectById(id);
            if (result == null) {
                throw new RuntimeException("海事信息不存在，ID: " + id);
            }
            return result;
        } catch (Exception e) {
            log.error("查询海事信息详情失败，ID: {}", id, e);
            throw new RuntimeException("查询海事信息详情失败: " + e.getMessage());
        }
    }

    @Override
    public List<MaritimeBureauConfig> getAllBureauConfigs() {
        log.info("查询所有海事局配置");
        
        try {
            List<MaritimeBureauConfig> result = bureauConfigMapper.selectAll();
            log.info("查询到 {} 个海事局配置", result.size());
            return result;
        } catch (Exception e) {
            log.error("查询海事局配置失败", e);
            throw new RuntimeException("查询海事局配置失败: " + e.getMessage());
        }
    }

    @Override
    public MaritimeBureauConfig getBureauConfigById(Long id) {
        log.info("根据ID查询海事局配置: {}", id);
        
        try {
            MaritimeBureauConfig result = bureauConfigMapper.selectById(id);
            if (result == null) {
                throw new RuntimeException("海事局配置不存在，ID: " + id);
            }
            return result;
        } catch (Exception e) {
            log.error("查询海事局配置失败，ID: {}", id, e);
            throw new RuntimeException("查询海事局配置失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public MaritimeBureauConfig saveBureauConfig(MaritimeBureauConfig config) {
        log.info("保存海事局配置: {}", config.getBureauName());
        
        try {
            if (config.getEnabled() == null) {
                config.setEnabled(true);
            }

            config.setCreateTime(LocalDateTime.now());
            config.setUpdateTime(LocalDateTime.now());
            bureauConfigMapper.insert(config);
            log.info("保存海事局配置成功，ID: {}", config.getId());
            return config;
        } catch (Exception e) {
            log.error("保存海事局配置失败", e);
            throw new RuntimeException("保存海事局配置失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public MaritimeBureauConfig updateBureauConfig(Long id, MaritimeBureauConfig config) {
        log.info("更新海事局配置，ID: {}", id);
        
        try {
            MaritimeBureauConfig existing = bureauConfigMapper.selectById(id);
            if (existing == null) {
                throw new RuntimeException("海事局配置不存在，ID: " + id);
            }
            
            config.setId(id);
            config.setCreateTime(existing.getCreateTime()); // 保持原创建时间
            config.setUpdateTime(LocalDateTime.now());
            bureauConfigMapper.updateById(config);
            log.info("更新海事局配置成功，ID: {}", id);
            return config;
        } catch (Exception e) {
            log.error("更新海事局配置失败，ID: {}", id, e);
            throw new RuntimeException("更新海事局配置失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void deleteBureauConfig(Long id) {
        log.info("删除海事局配置，ID: {}", id);
        
        try {
            MaritimeBureauConfig existing = bureauConfigMapper.selectById(id);
            if (existing == null) {
                throw new RuntimeException("海事局配置不存在，ID: " + id);
            }
            
            bureauConfigMapper.deleteById(id);
            log.info("删除海事局配置成功，ID: {}", id);
        } catch (Exception e) {
            log.error("删除海事局配置失败，ID: {}", id, e);
            throw new RuntimeException("删除海事局配置失败: " + e.getMessage());
        }
    }
}
