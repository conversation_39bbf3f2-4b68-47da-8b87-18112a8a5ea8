package com.example.multidatasource.crew.service.impl;

import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.common.config.DataSourceContextHolder;
import com.example.multidatasource.common.dto.CrewQueryDTO;
import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.crew.entity.CrewInfo;
import com.example.multidatasource.crew.mapper.CrewMapper;
import com.example.multidatasource.crew.service.CrewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 船员管理服务实现
 */
@Slf4j
@Service
@DataSource(DataSourceContextHolder.DataSourceType.CREW)
public class CrewServiceImpl implements CrewService {

    @Autowired
    private CrewMapper crewMapper;

    @Override
    public PageResult<CrewInfo> getCrewList(Integer page, Integer size) {
        int offset = (page - 1) * size;
        List<CrewInfo> records = crewMapper.selectCrewList(offset, size);
        int total = crewMapper.countCrew();
        
        return PageResult.of(records, (long) total, page, size);
    }

    @Override
    public CrewInfo getCrewById(String id) {
        return crewMapper.selectCrewById(id);
    }

    @Override
    public boolean addCrew(CrewInfo crewInfo) {
        int result = crewMapper.insertCrew(crewInfo);
        return result > 0;
    }

    @Override
    public boolean updateCrew(CrewInfo crewInfo) {
        int result = crewMapper.updateCrew(crewInfo);
        return result > 0;
    }

    @Override
    public boolean deleteCrew(Long id) {
        int result = crewMapper.deleteCrewById(id);
        return result > 0;
    }

    @Override
    public List<CrewInfo> getCrewByStatus(String status) {
        return crewMapper.selectCrewByStatus(status);
    }

    @Override
    public List<CrewInfo> getCrewByPosition(String position) {
        return crewMapper.selectCrewByPosition(position);
    }

    @Override
    public List<CrewInfo> searchCrewByName(String name) {
        return crewMapper.selectCrewByName(name);
    }

    @Override
    public PageResult<CrewInfo> searchCrew(CrewQueryDTO queryDTO) {
        List<CrewInfo> records = crewMapper.selectCrewByConditions(
            queryDTO.getName(),
            queryDTO.getPosition(),
            queryDTO.getStatus(),
            queryDTO.getPhone(),
            queryDTO.getOffset(),
            queryDTO.getLimit()
        );
        
        int total = crewMapper.countCrewByConditions(
            queryDTO.getName(),
            queryDTO.getPosition(),
            queryDTO.getStatus(),
            queryDTO.getPhone()
        );
        
        return PageResult.of(records, (long) total, queryDTO.getPage(), queryDTO.getSize());
    }

    @Override
    public List<String> getAllPositions() {
        return crewMapper.selectAllPositions();
    }

    @Override
    public boolean batchUpdateCrewStatus(List<Long> ids, String status) {
        int result = crewMapper.batchUpdateCrewStatus(ids, status);
        return result > 0;
    }

    @Override
    public int getCrewCount() {
        return crewMapper.countCrew();
    }
}
