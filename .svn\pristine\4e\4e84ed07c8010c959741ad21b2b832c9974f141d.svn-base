package com.example.multidatasource.crew.service;

import com.example.multidatasource.common.dto.CrewQueryDTO;
import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.crew.entity.CrewInfo;

import java.util.List;

/**
 * 船员管理服务接口
 */
public interface CrewService {

    /**
     * 分页查询船员列表
     */
    PageResult<CrewInfo> getCrewList(Integer page, Integer size);

    /**
     * 根据ID查询船员详情
     */
    CrewInfo getCrewById(String id);

    /**
     * 添加船员
     */
    boolean addCrew(CrewInfo crewInfo);

    /**
     * 更新船员信息
     */
    boolean updateCrew(CrewInfo crewInfo);

    /**
     * 删除船员
     */
    boolean deleteCrew(Long id);

    /**
     * 根据状态查询船员列表
     */
    List<CrewInfo> getCrewByStatus(String status);

    /**
     * 根据职位查询船员列表
     */
    List<CrewInfo> getCrewByPosition(String position);

    /**
     * 根据姓名模糊查询船员
     */
    List<CrewInfo> searchCrewByName(String name);

    /**
     * 多条件查询船员（分页）
     */
    PageResult<CrewInfo> searchCrew(CrewQueryDTO queryDTO);

    /**
     * 获取所有职位列表
     */
    List<String> getAllPositions();

    /**
     * 批量更新船员状态
     */
    boolean batchUpdateCrewStatus(List<Long> ids, String status);

    /**
     * 统计船员总数
     */
    int getCrewCount();
}
