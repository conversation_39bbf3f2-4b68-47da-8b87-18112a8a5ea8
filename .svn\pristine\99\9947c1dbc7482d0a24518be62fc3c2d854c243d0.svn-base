2025-08-07 10:42:09 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 21832 (D:\augmentSpace\target\classes started by <PERSON><PERSON><PERSON><PERSON> in D:\augmentSpace)
2025-08-07 10:42:09 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-07 10:42:09 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-08-07 10:42:11 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-07 10:42:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-08-07 10:42:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-08-07 10:42:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-08-07 10:42:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerBaseInfoMapper.class]
2025-08-07 10:42:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerScheduleMapper.class]
2025-08-07 10:42:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-08-07 10:42:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-08-07 10:42:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-08-07 10:42:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-08-07 10:42:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerBaseInfoMapper' and 'com.example.multidatasource.crew.mapper.SeafarerBaseInfoMapper' mapperInterface
2025-08-07 10:42:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerScheduleMapper' and 'com.example.multidatasource.crew.mapper.SeafarerScheduleMapper' mapperInterface
2025-08-07 10:42:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-08-07 10:42:13 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-08-07 10:42:13 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-07 10:42:13 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-07 10:42:13 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-08-07 10:42:13 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4058 ms
2025-08-07 10:42:13 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-07 10:42:14 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-08-07 10:42:14 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-08-07 10:42:14 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-08-07 10:42:14 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-08-07 10:42:14 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerBaseInfoMapper.xml]'
2025-08-07 10:42:14 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerScheduleMapper.xml]'
2025-08-07 10:42:14 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-08-07 10:42:15 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 7099d7b9-e60a-434b-a6f7-83d5d5950836

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-07 10:42:15 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2489e84a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2a39aa2b, org.springframework.security.web.context.SecurityContextPersistenceFilter@4f4c88f9, org.springframework.security.web.header.HeaderWriterFilter@eb6ec6, org.springframework.web.filter.CorsFilter@62b93086, org.springframework.security.web.authentication.logout.LogoutFilter@480b57e2, com.example.multidatasource.config.JwtAuthenticationFilter@37ed010a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@40a72ecd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6ee99964, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@254449bb, org.springframework.security.web.session.SessionManagementFilter@2806d6da, org.springframework.security.web.access.ExceptionTranslationFilter@df921b1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@56f730b2]
2025-08-07 10:42:16 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 54321 (http) with context path '/multi/source/api'
2025-08-07 10:42:16 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 8.021 seconds (JVM running for 8.923)
2025-08-07 10:44:29 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 27116 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-08-07 10:44:29 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-07 10:44:29 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-08-07 10:44:30 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-07 10:44:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-08-07 10:44:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-08-07 10:44:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-08-07 10:44:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerBaseInfoMapper.class]
2025-08-07 10:44:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerScheduleMapper.class]
2025-08-07 10:44:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-08-07 10:44:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-08-07 10:44:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-08-07 10:44:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-08-07 10:44:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerBaseInfoMapper' and 'com.example.multidatasource.crew.mapper.SeafarerBaseInfoMapper' mapperInterface
2025-08-07 10:44:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerScheduleMapper' and 'com.example.multidatasource.crew.mapper.SeafarerScheduleMapper' mapperInterface
2025-08-07 10:44:30 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-08-07 10:44:31 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-08-07 10:44:31 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-07 10:44:31 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-07 10:44:31 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-08-07 10:44:31 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2181 ms
2025-08-07 10:44:31 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-07 10:44:32 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-08-07 10:44:32 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-08-07 10:44:32 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-08-07 10:44:32 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-08-07 10:44:32 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerBaseInfoMapper.xml]'
2025-08-07 10:44:32 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerScheduleMapper.xml]'
2025-08-07 10:44:32 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-08-07 10:44:32 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 20652ae0-da99-4856-b12a-11c07cd143b2

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-07 10:44:32 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5b7aa898, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@41b13f3d, org.springframework.security.web.context.SecurityContextPersistenceFilter@1db7157f, org.springframework.security.web.header.HeaderWriterFilter@2152ab30, org.springframework.web.filter.CorsFilter@7c0777b5, org.springframework.security.web.authentication.logout.LogoutFilter@3f36e8d1, com.example.multidatasource.config.JwtAuthenticationFilter@6ae3fb94, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@ae73c80, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@73971965, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2489e84a, org.springframework.security.web.session.SessionManagementFilter@62b790a5, org.springframework.security.web.access.ExceptionTranslationFilter@44a085e5, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@53a84ff4]
2025-08-07 10:44:33 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 54321 (http) with context path '/multi/source/api'
2025-08-07 10:44:33 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 4.695 seconds (JVM running for 5.348)
2025-08-07 10:53:09 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 21808 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-08-07 10:53:09 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-07 10:53:09 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-08-07 10:53:10 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-07 10:53:10 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-08-07 10:53:10 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-08-07 10:53:10 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-08-07 10:53:10 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerBaseInfoMapper.class]
2025-08-07 10:53:10 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerScheduleMapper.class]
2025-08-07 10:53:10 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-08-07 10:53:10 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-08-07 10:53:10 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-08-07 10:53:10 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-08-07 10:53:10 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerBaseInfoMapper' and 'com.example.multidatasource.crew.mapper.SeafarerBaseInfoMapper' mapperInterface
2025-08-07 10:53:10 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerScheduleMapper' and 'com.example.multidatasource.crew.mapper.SeafarerScheduleMapper' mapperInterface
2025-08-07 10:53:10 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-08-07 10:53:11 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-08-07 10:53:11 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-07 10:53:11 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-07 10:53:12 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-08-07 10:53:12 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2430 ms
2025-08-07 10:53:12 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-07 10:53:12 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-08-07 10:53:12 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-08-07 10:53:12 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-08-07 10:53:12 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-08-07 10:53:12 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerBaseInfoMapper.xml]'
2025-08-07 10:53:12 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerScheduleMapper.xml]'
2025-08-07 10:53:12 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-08-07 10:53:13 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: d73f2230-bfc4-42b1-ad80-879afdc9212c

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-07 10:53:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@dd2856e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5a49af50, org.springframework.security.web.context.SecurityContextPersistenceFilter@361abd01, org.springframework.security.web.header.HeaderWriterFilter@6b63e6ad, org.springframework.web.filter.CorsFilter@3b1dc579, org.springframework.security.web.authentication.logout.LogoutFilter@10a98392, com.example.multidatasource.config.JwtAuthenticationFilter@96a75da, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2eed37f4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5562c2c9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@793d163b, org.springframework.security.web.session.SessionManagementFilter@6ee99964, org.springframework.security.web.access.ExceptionTranslationFilter@4f4c88f9, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7e9f2c32]
2025-08-07 10:53:13 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 54321 is already in use
2025-08-07 10:53:13 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-07 10:53:13 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-07 10:53:14 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 54321 was already in use.

Action:

Identify and stop the process that's listening on port 54321 or configure this application to listen on another port.

2025-08-07 10:53:45 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 29120 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-08-07 10:53:45 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-07 10:53:45 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-08-07 10:53:46 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-07 10:53:46 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-08-07 10:53:46 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-08-07 10:53:46 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-08-07 10:53:46 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerBaseInfoMapper.class]
2025-08-07 10:53:46 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerScheduleMapper.class]
2025-08-07 10:53:46 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-08-07 10:53:46 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-08-07 10:53:46 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-08-07 10:53:46 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-08-07 10:53:46 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerBaseInfoMapper' and 'com.example.multidatasource.crew.mapper.SeafarerBaseInfoMapper' mapperInterface
2025-08-07 10:53:46 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerScheduleMapper' and 'com.example.multidatasource.crew.mapper.SeafarerScheduleMapper' mapperInterface
2025-08-07 10:53:46 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-08-07 10:53:48 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-08-07 10:53:48 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-07 10:53:48 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-07 10:53:48 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-08-07 10:53:48 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2480 ms
2025-08-07 10:53:48 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-07 10:53:48 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-08-07 10:53:48 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-08-07 10:53:48 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-08-07 10:53:48 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-08-07 10:53:48 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerBaseInfoMapper.xml]'
2025-08-07 10:53:48 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerScheduleMapper.xml]'
2025-08-07 10:53:48 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-08-07 10:53:49 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 10a58573-39e9-4c3c-affe-5549f8599435

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-07 10:53:49 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@254449bb, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4e642ee1, org.springframework.security.web.context.SecurityContextPersistenceFilter@1f3b992, org.springframework.security.web.header.HeaderWriterFilter@727320fa, org.springframework.web.filter.CorsFilter@29ebbdf4, org.springframework.security.web.authentication.logout.LogoutFilter@561953e3, com.example.multidatasource.config.JwtAuthenticationFilter@7c129ef6, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@17410c07, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@46f902e0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2fd954f, org.springframework.security.web.session.SessionManagementFilter@75e27856, org.springframework.security.web.access.ExceptionTranslationFilter@3902bd2c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1e60b459]
2025-08-07 10:53:50 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 54321 is already in use
2025-08-07 10:53:50 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-07 10:53:50 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-07 10:53:50 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 54321 was already in use.

Action:

Identify and stop the process that's listening on port 54321 or configure this application to listen on another port.

2025-08-07 10:59:34 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 31064 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-08-07 10:59:34 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-07 10:59:34 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-08-07 10:59:34 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-07 10:59:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-08-07 10:59:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-08-07 10:59:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-08-07 10:59:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerBaseInfoMapper.class]
2025-08-07 10:59:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerScheduleMapper.class]
2025-08-07 10:59:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-08-07 10:59:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-08-07 10:59:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-08-07 10:59:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-08-07 10:59:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerBaseInfoMapper' and 'com.example.multidatasource.crew.mapper.SeafarerBaseInfoMapper' mapperInterface
2025-08-07 10:59:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerScheduleMapper' and 'com.example.multidatasource.crew.mapper.SeafarerScheduleMapper' mapperInterface
2025-08-07 10:59:34 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-08-07 10:59:35 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-08-07 10:59:35 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-07 10:59:35 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-07 10:59:35 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-08-07 10:59:35 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1724 ms
2025-08-07 10:59:35 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-07 10:59:36 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-08-07 10:59:36 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-08-07 10:59:36 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-08-07 10:59:36 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-08-07 10:59:36 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerBaseInfoMapper.xml]'
2025-08-07 10:59:36 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerScheduleMapper.xml]'
2025-08-07 10:59:36 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-08-07 10:59:36 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 98057f14-4636-413c-a76b-0e68f9b0ef7c

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-07 10:59:36 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2e7157c7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2a43e0ac, org.springframework.security.web.context.SecurityContextPersistenceFilter@77bc2e16, org.springframework.security.web.header.HeaderWriterFilter@2f894ad9, org.springframework.web.filter.CorsFilter@22d9bc14, org.springframework.security.web.authentication.logout.LogoutFilter@3e0fbeb5, com.example.multidatasource.config.JwtAuthenticationFilter@1d0a61c8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5e5aafc6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@53a84ff4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@346f41a9, org.springframework.security.web.session.SessionManagementFilter@56382bc9, org.springframework.security.web.access.ExceptionTranslationFilter@1db87583, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@31b7d869]
2025-08-07 10:59:37 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 54321 is already in use
2025-08-07 10:59:37 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-07 10:59:37 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-07 10:59:37 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 54321 was already in use.

Action:

Identify and stop the process that's listening on port 54321 or configure this application to listen on another port.

2025-08-07 11:01:10 [main] INFO  c.e.m.MultiDataSourceApplication - Starting MultiDataSourceApplication using Java 1.8.0_202 on DESKTOP-L6NJM1O with PID 28860 (D:\augmentSpace\target\classes started by chiqiyun in D:\augmentSpace)
2025-08-07 11:01:10 [main] DEBUG c.e.m.MultiDataSourceApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-07 11:01:10 [main] INFO  c.e.m.MultiDataSourceApplication - The following 1 profile is active: "dev"
2025-08-07 11:01:11 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-07 11:01:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\auth\mapper\UserMapper.class]
2025-08-07 11:01:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\CrewMapper.class]
2025-08-07 11:01:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\OilVoyageConsumptionMapper.class]
2025-08-07 11:01:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerBaseInfoMapper.class]
2025-08-07 11:01:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\crew\mapper\SeafarerScheduleMapper.class]
2025-08-07 11:01:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\augmentSpace\target\classes\com\example\multidatasource\voyage\mapper\VoyageMapper.class]
2025-08-07 11:01:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.example.multidatasource.auth.mapper.UserMapper' mapperInterface
2025-08-07 11:01:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'crewMapper' and 'com.example.multidatasource.crew.mapper.CrewMapper' mapperInterface
2025-08-07 11:01:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'oilVoyageConsumptionMapper' and 'com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper' mapperInterface
2025-08-07 11:01:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerBaseInfoMapper' and 'com.example.multidatasource.crew.mapper.SeafarerBaseInfoMapper' mapperInterface
2025-08-07 11:01:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'seafarerScheduleMapper' and 'com.example.multidatasource.crew.mapper.SeafarerScheduleMapper' mapperInterface
2025-08-07 11:01:11 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'voyageMapper' and 'com.example.multidatasource.voyage.mapper.VoyageMapper' mapperInterface
2025-08-07 11:01:12 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 54321 (http)
2025-08-07 11:01:12 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-07 11:01:12 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-07 11:01:13 [main] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring embedded WebApplicationContext
2025-08-07 11:01:13 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2043 ms
2025-08-07 11:01:13 [main] DEBUG c.e.m.config.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-07 11:01:13 [main] INFO  c.e.m.config.DynamicDataSource - Dynamic DataSource initialized with target data sources: [cargo, finance, crew, voyage]
2025-08-07 11:01:13 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\auth\UserMapper.xml]'
2025-08-07 11:01:13 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\CrewMapper.xml]'
2025-08-07 11:01:13 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\OilVoyageConsumptionMapper.xml]'
2025-08-07 11:01:13 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerBaseInfoMapper.xml]'
2025-08-07 11:01:13 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\crew\SeafarerScheduleMapper.xml]'
2025-08-07 11:01:13 [main] DEBUG o.m.spring.SqlSessionFactoryBean - Parsed mapper file: 'file [D:\augmentSpace\target\classes\mapper\voyage\VoyageMapper.xml]'
2025-08-07 11:01:14 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 33b1b89c-7994-408b-ad2e-c042a5e2f6d2

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-07 11:01:14 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@31120021, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3df1a1ac, org.springframework.security.web.context.SecurityContextPersistenceFilter@47311277, org.springframework.security.web.header.HeaderWriterFilter@411a5965, org.springframework.web.filter.CorsFilter@58606c91, org.springframework.security.web.authentication.logout.LogoutFilter@1192b58e, com.example.multidatasource.config.JwtAuthenticationFilter@46731692, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4e642ee1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6a0f2853, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6a9cd0f8, org.springframework.security.web.session.SessionManagementFilter@5807efad, org.springframework.security.web.access.ExceptionTranslationFilter@7c51782d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2e26173]
2025-08-07 11:01:15 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 54321 (http) with context path '/multi/source/api'
2025-08-07 11:01:15 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 4.777 seconds (JVM running for 6.63)
2025-08-07 11:02:16 [http-nio-54321-exec-1] INFO  o.a.c.c.C.[.[.[/multi/source/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-07 11:02:16 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-07 11:02:16 [http-nio-54321-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-07 11:02:18 [http-nio-54321-exec-9] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1178 ms
