package com.example.multidatasource.voyage;

import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.voyage.dto.MaritimeCrawlRequestDTO;
import com.example.multidatasource.voyage.dto.MaritimeCrawlResultDTO;
import com.example.multidatasource.voyage.dto.MaritimeQueryDTO;
import com.example.multidatasource.voyage.entity.MaritimeBureauConfig;
import com.example.multidatasource.voyage.entity.MaritimeInfo;
import com.example.multidatasource.voyage.service.MaritimeService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

/**
 * 海事信息管理集成测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class MaritimeIntegrationTest {

    @Autowired(required = false)
    private MaritimeService maritimeService;

    /**
     * 测试海事局配置查询
     */
    @Test
    public void testGetAllBureauConfigs() {
        if (maritimeService == null) {
            System.out.println("MaritimeService not available, skipping test");
            return;
        }
        
        try {
            List<MaritimeBureauConfig> configs = maritimeService.getAllBureauConfigs();
            System.out.println("=== 海事局配置列表 ===");
            System.out.println("总数: " + configs.size());
            
            for (MaritimeBureauConfig config : configs) {
                System.out.println("海事局: " + config.getBureauName());
                System.out.println("告警channelId: " + config.getAlarmChannelId());
                System.out.println("通告channelId: " + config.getNoticeChannelId());
                System.out.println("启用状态: " + config.getEnabled());
                System.out.println("---");
            }
        } catch (Exception e) {
            System.out.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试海事信息爬取（模拟）
     */
    @Test
    public void testCrawlMaritimeInfo() {
        if (maritimeService == null) {
            System.out.println("MaritimeService not available, skipping test");
            return;
        }
        
        try {
            MaritimeCrawlRequestDTO request = new MaritimeCrawlRequestDTO();
            request.setBureauNames(Arrays.asList("上海海事局"));
            request.setInfoTypes(Arrays.asList("ALARM", "NOTICE"));
            request.setDays(1);
            
            System.out.println("=== 开始爬取海事信息 ===");
            System.out.println("请求参数: " + request);
            
            MaritimeCrawlResultDTO result = maritimeService.crawlMaritimeInfo(request);
            
            System.out.println("=== 爬取结果 ===");
            System.out.println("总数: " + result.getTotalCount());
            System.out.println("成功: " + result.getSuccessCount());
            System.out.println("失败: " + result.getFailureCount());
            System.out.println("新增: " + result.getNewCount());
            System.out.println("更新: " + result.getUpdateCount());
            
            if (result.getDetails() != null) {
                System.out.println("=== 详细信息 ===");
                for (MaritimeCrawlResultDTO.BureauCrawlDetail detail : result.getDetails()) {
                    System.out.println("海事局: " + detail.getBureauName());
                    System.out.println("类型: " + detail.getInfoType());
                    System.out.println("状态: " + detail.getStatus());
                    System.out.println("数量: " + detail.getCount());
                    if (detail.getErrorMessage() != null) {
                        System.out.println("错误: " + detail.getErrorMessage());
                    }
                    System.out.println("---");
                }
            }
        } catch (Exception e) {
            System.out.println("爬取测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试海事信息查询
     */
    @Test
    public void testQueryMaritimeInfo() {
        if (maritimeService == null) {
            System.out.println("MaritimeService not available, skipping test");
            return;
        }
        
        try {
            MaritimeQueryDTO query = new MaritimeQueryDTO();
            query.setPage(1);
            query.setSize(5);
            query.setBureauName("上海海事局");
            query.setStartDate(LocalDate.now().minusDays(7));
            query.setEndDate(LocalDate.now());
            
            System.out.println("=== 查询海事信息 ===");
            System.out.println("查询条件: " + query);
            
            PageResult<MaritimeInfo> result = maritimeService.queryMaritimeInfo(query);
            
            System.out.println("=== 查询结果 ===");
            System.out.println("总数: " + result.getTotal());
            System.out.println("当前页: " + result.getCurrent());
            System.out.println("每页大小: " + result.getSize());
            System.out.println("总页数: " + result.getPages());
            System.out.println("当前页记录数: " + result.getRecords().size());
            
            if (!result.getRecords().isEmpty()) {
                System.out.println("=== 前几条记录 ===");
                for (MaritimeInfo info : result.getRecords()) {
                    System.out.println("ID: " + info.getId());
                    System.out.println("标题: " + info.getTitle());
                    System.out.println("海事局: " + info.getBureauName());
                    System.out.println("类型: " + info.getInfoType());
                    System.out.println("发布日期: " + info.getPublishDate());
                    System.out.println("---");
                }
            }
        } catch (Exception e) {
            System.out.println("查询测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试海事局配置管理
     */
    @Test
    public void testBureauConfigManagement() {
        if (maritimeService == null) {
            System.out.println("MaritimeService not available, skipping test");
            return;
        }
        
        try {
            // 创建测试配置
            MaritimeBureauConfig testConfig = new MaritimeBureauConfig();
            testConfig.setBureauName("测试海事局");
            testConfig.setAlarmChannelId("TEST-ALARM-CHANNEL");
            testConfig.setNoticeChannelId("TEST-NOTICE-CHANNEL");
            testConfig.setEnabled(true);
            
            System.out.println("=== 创建测试配置 ===");
            MaritimeBureauConfig saved = maritimeService.saveBureauConfig(testConfig);
            System.out.println("创建成功，ID: " + saved.getId());
            
            // 查询配置
            System.out.println("=== 查询配置 ===");
            MaritimeBureauConfig found = maritimeService.getBureauConfigById(saved.getId());
            System.out.println("查询结果: " + found.getBureauName());
            
            // 更新配置
            System.out.println("=== 更新配置 ===");
            found.setBureauName("更新后的测试海事局");
            found.setEnabled(false);
            MaritimeBureauConfig updated = maritimeService.updateBureauConfig(found.getId(), found);
            System.out.println("更新成功: " + updated.getBureauName() + ", 启用状态: " + updated.getEnabled());
            
            // 删除配置
            System.out.println("=== 删除配置 ===");
            maritimeService.deleteBureauConfig(saved.getId());
            System.out.println("删除成功");
            
        } catch (Exception e) {
            System.out.println("配置管理测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
