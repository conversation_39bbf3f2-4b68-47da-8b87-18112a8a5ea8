package com.example.multidatasource.voyage.controller;

import com.example.multidatasource.common.dto.ApiResponse;
import com.example.multidatasource.voyage.schedule.MaritimeScheduleTask;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 海事信息定时任务管理接口
 */
@Slf4j
@RestController
@RequestMapping("/voyage/maritime/schedule")
@Tag(name = "海事信息定时任务管理", description = "海事信息定时任务的手动触发和管理")
public class MaritimeScheduleController {

    @Autowired
    private MaritimeScheduleTask maritimeScheduleTask;

    /**
     * 手动触发爬取任务
     */
    @PostMapping("/trigger")
    @Operation(summary = "手动触发爬取任务", description = "立即执行一次海事信息爬取任务")
    public ApiResponse<String> triggerCrawl() {
        try {
            log.info("手动触发海事信息爬取任务");
            maritimeScheduleTask.manualCrawl();
            return ApiResponse.success("海事信息爬取任务已触发");
        } catch (Exception e) {
            log.error("手动触发海事信息爬取任务失败", e);
            return ApiResponse.error("触发失败: " + e.getMessage());
        }
    }
}
