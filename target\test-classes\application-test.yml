# 测试环境配置
server:
  port: 0  # 随机端口，避免端口冲突

# 测试环境使用内存数据库或禁用数据源
spring:
  profiles:
    active: test
  
  # 禁用数据源自动配置，避免测试时连接真实数据库
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibariDataSourceAutoConfiguration

# 测试环境日志配置（简化日志输出）
logging:
  level:
    root: WARN
    com.example.multidatasource: INFO
    org.springframework: WARN
    org.apache.ibatis: WARN
    org.mybatis: WARN
  pattern:
    console: "%d{HH:mm:ss} %-5level %logger{36} - %msg%n"

# 禁用管理端点
management:
  endpoints:
    enabled-by-default: false
  endpoint:
    health:
      enabled: false
