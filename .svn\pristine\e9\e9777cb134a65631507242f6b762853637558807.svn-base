<component name="libraryTable">
  <library name="Maven: org.apache.velocity:velocity-engine-core:2.3">
    <CLASSES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/org/apache/velocity/velocity-engine-core/2.3/velocity-engine-core-2.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>