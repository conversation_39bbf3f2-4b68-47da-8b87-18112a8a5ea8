package com.example.multidatasource.voyage.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 海事局配置实体类
 */
@Data
public class MaritimeBureauConfig {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 海事局名称
     */
    private String bureauName;
    
    /**
     * 告警channelId
     */
    private String alarmChannelId;
    
    /**
     * 通告channelId
     */
    private String noticeChannelId;
    
    /**
     * 是否启用：1-启用，0-禁用
     */
    private Boolean enabled;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
}
