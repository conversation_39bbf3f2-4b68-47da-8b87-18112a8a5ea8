# 启动问题排查指南

## 🔧 循环依赖问题解决

### 问题描述
```
Error creating bean with name 'securityConfig': Requested bean is currently in creation: 
Is there an unresolvable circular reference?

┌─────┐
|  securityConfig
↑     ↓
|  authServiceImpl
↑     ↓
|  userServiceImpl
└─────┘
```

### 解决方案
我们已经通过以下方式解决了循环依赖问题：

#### 1. 独立的PasswordEncoder配置
- 创建了 `PasswordEncoderConfig.java`
- 将PasswordEncoder Bean从SecurityConfig中分离

#### 2. 简化JWT过滤器
- 移除了JwtAuthenticationFilter对AuthService的依赖
- 直接使用JwtUtil进行令牌验证

#### 3. 使用@Lazy注解
- 在UserServiceImpl中对PasswordEncoder使用@Lazy注解

## 🚀 启动前检查清单

### 1. 数据库连接
确保数据库服务已启动并且连接配置正确：
```yaml
# application-dev.yml
datasources:
  crew:
    url: ***********************************************
    username: root
    password: your_password
```

### 2. 创建必要的数据库和表
```sql
-- 创建数据库
CREATE DATABASE crew_management_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE voyage_management_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用crew数据库创建用户表
USE crew_management_dev;
-- 运行 src/main/resources/sql/users.sql 中的脚本
```

### 3. 检查端口占用
确保8080端口未被占用：
```bash
# Windows
netstat -ano | findstr :8080

# Linux/Mac
lsof -i :8080
```

## 🐛 常见启动错误及解决方案

### 错误1：数据库连接失败
```
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure
```
**解决方案**：
1. 检查MySQL服务是否启动
2. 检查数据库连接配置
3. 确认数据库存在

### 错误2：表不存在
```
Table 'crew_management_dev.users' doesn't exist
```
**解决方案**：
1. 运行用户表创建脚本
2. 检查数据库名称是否正确

### 错误3：端口被占用
```
Port 8080 was already in use
```
**解决方案**：
1. 修改application.yml中的端口：
```yaml
server:
  port: 8081
```
2. 或者停止占用8080端口的进程

### 错误4：Bean创建失败
```
Error creating bean with name 'xxx'
```
**解决方案**：
1. 检查@Autowired的依赖是否正确
2. 确认所有必要的@Component、@Service、@Repository注解
3. 检查包扫描路径

## 📋 启动成功标志

看到以下日志表示启动成功：
```
2025-06-25 16:43:06 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http)
2025-06-25 16:43:06 [main] INFO  c.e.m.MultiDataSourceApplication - Started MultiDataSourceApplication in 3.456 seconds
```

## 🔍 启动后验证

### 1. 健康检查
```bash
curl http://localhost:8080/api/health/status
```

### 2. Swagger文档
访问：http://localhost:8080/api/swagger-ui.html

### 3. 登录测试
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

## 🛠️ 调试技巧

### 1. 启用调试日志
在application.yml中添加：
```yaml
logging:
  level:
    com.example.multidatasource: DEBUG
    org.springframework.security: DEBUG
```

### 2. 查看Bean创建过程
启动时添加参数：
```bash
java -jar app.jar --debug
```

### 3. 检查数据源状态
```bash
curl http://localhost:8080/api/datasource/status
```

## 📞 获取帮助

如果问题仍然存在：
1. 检查完整的错误堆栈信息
2. 确认Java版本（需要Java 8+）
3. 确认Maven版本（需要Maven 3.6+）
4. 查看IDE的错误提示
5. 检查防火墙和网络设置
