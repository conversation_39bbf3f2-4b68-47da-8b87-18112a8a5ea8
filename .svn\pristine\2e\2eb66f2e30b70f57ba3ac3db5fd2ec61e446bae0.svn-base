<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.multidatasource.crew.mapper.SeafarerScheduleMapper">

    <!-- 结果映射 -->
    <resultMap id="CrewSeafarerInfoResultMap" type="com.example.multidatasource.crew.entity.CrewSeafarerInfo">
        <!-- 主键映射 -->
        <id column="seafarer_id" property="seafarerId" jdbcType="VARCHAR"/>

        <!-- 基本信息映射 -->
        <result column="comp_id" property="compId" jdbcType="VARCHAR"/>
        <result column="seafarer_name" property="seafarerName" jdbcType="VARCHAR" />
        <result column="birthday" property="birthday" jdbcType="TIMESTAMP"/>
        <result column="enter_company_date" property="enterCompanyDate" jdbcType="TIMESTAMP"/>
        <result column="crt_duty_id" property="crtDutyId" jdbcType="VARCHAR"/>
        <result column="crt_duty_name" property="crtDutyName" jdbcType="VARCHAR"/>
        <result column="crt_level_id" property="crtLevelId" jdbcType="VARCHAR"/>
        <result column="crt_level_name" property="crtLevelName" jdbcType="VARCHAR"/>
        <result column="nationality_key" property="nationalityKey" jdbcType="VARCHAR"/>
        <result column="nationality_value" property="nationalityValue" jdbcType="VARCHAR"/>

        <!-- 出生地信息映射 -->
        <result column="brithplace_prov_key" property="brithplaceProvKey" jdbcType="INTEGER"/>
        <result column="brithplace_prov_value" property="brithplaceProvValue" jdbcType="VARCHAR"/>
        <result column="brithplace_city_key" property="brithplaceCityKey" jdbcType="INTEGER"/>
        <result column="brithplace_city_value" property="brithplaceCityValue" jdbcType="VARCHAR"/>

        <!-- 个人信息映射 -->
        <result column="nation" property="nation" jdbcType="VARCHAR"/>
        <result column="political_key" property="politicalKey" jdbcType="VARCHAR"/>
        <result column="political_value" property="politicalValue" jdbcType="VARCHAR"/>
        <result column="place_of_origin_prov_key" property="placeOfOriginProvKey" jdbcType="INTEGER"/>
        <result column="place_of_origin_prov_value" property="placeOfOriginProvValue" jdbcType="VARCHAR"/>
        <result column="place_of_origin_city_key" property="placeOfOriginCityKey" jdbcType="INTEGER"/>
        <result column="place_of_origin_city_value" property="placeOfOriginCityValue" jdbcType="VARCHAR"/>
        <result column="seafarer_id_no" property="seafarerIdNo" jdbcType="VARCHAR"/>
        <result column="marriage_status_key" property="marriageStatusKey" jdbcType="VARCHAR"/>
        <result column="marriage_status_value" property="marriageStatusValue" jdbcType="VARCHAR"/>
        <result column="healthy" property="healthy" jdbcType="VARCHAR"/>
        <result column="seafarer_sex_key" property="seafarerSexKey" jdbcType="VARCHAR"/>
        <result column="seafarer_sex_value" property="seafarerSexValue" jdbcType="VARCHAR"/>
        <result column="waistline" property="waistline" jdbcType="VARCHAR"/>
        <result column="height" property="height" jdbcType="VARCHAR"/>
        <result column="blood_key" property="bloodKey" jdbcType="VARCHAR"/>
        <result column="blood_value" property="bloodValue" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="suit_size" property="suitSize" jdbcType="VARCHAR"/>
        <result column="shoe_size" property="shoeSize" jdbcType="VARCHAR"/>

        <!-- 职业信息映射 -->
        <result column="seafarer_type_key" property="seafarerTypeKey" jdbcType="VARCHAR"/>
        <result column="seafarer_type_value" property="seafarerTypeValue" jdbcType="VARCHAR"/>
        <result column="employment_id" property="employmentId" jdbcType="VARCHAR"/>
        <result column="employment_name" property="employmentName" jdbcType="VARCHAR"/>
        <result column="maritime_experience_key" property="maritimeExperienceKey" jdbcType="VARCHAR"/>
        <result column="maritime_experience_value" property="maritimeExperienceValue" jdbcType="VARCHAR"/>
        <result column="repatriate_city" property="repatriateCity" jdbcType="VARCHAR"/>
        <result column="begin_work_date" property="beginWorkDate" jdbcType="TIMESTAMP"/>

        <!-- 联系方式映射 -->
        <result column="other_tel" property="otherTel" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="qq_wechat" property="qqWechat" jdbcType="VARCHAR"/>
        <result column="post_code" property="postCode" jdbcType="VARCHAR"/>
        <result column="current_address" property="currentAddress" jdbcType="VARCHAR"/>
        <result column="home_address_en" property="homeAddressEn" jdbcType="VARCHAR"/>
        <result column="census_register" property="censusRegister" jdbcType="VARCHAR"/>
        <result column="liaison_remark" property="liaisonRemark" jdbcType="VARCHAR"/>

        <!-- 公司与船舶信息映射 -->
        <result column="owned_out_comp_id" property="ownedOutCompId" jdbcType="VARCHAR"/>
        <result column="owned_out_comp_name" property="ownedOutCompName" jdbcType="VARCHAR"/>
        <result column="vessel_id" property="vesselId" jdbcType="VARCHAR"/>
        <result column="vessel_name" property="vesselName" jdbcType="VARCHAR"/>
        <result column="on_duty_id" property="onDutyId" jdbcType="VARCHAR"/>
        <result column="on_duty_name" property="onDutyName" jdbcType="VARCHAR"/>
        <result column="home_address" property="homeAddress" jdbcType="VARCHAR"/>
        <result column="mobile_phone" property="mobilePhone" jdbcType="VARCHAR"/>

        <!-- 备注信息映射 -->
        <result column="remark_id" property="remarkId" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>

        <!-- 教育背景映射 -->
        <result column="school" property="school" jdbcType="VARCHAR"/>
        <result column="school_place" property="schoolPlace" jdbcType="VARCHAR"/>
        <result column="school_nature_key" property="schoolNatureKey" jdbcType="VARCHAR"/>
        <result column="school_nature_value" property="schoolNatureValue" jdbcType="VARCHAR"/>
        <result column="education_key" property="educationKey" jdbcType="VARCHAR" />
        <result column="education_value" property="educationValue" jdbcType="VARCHAR"/>
        <result column="education_cert_no" property="educationCertNo" jdbcType="VARCHAR"/>
        <result column="enrollment_date" property="enrollmentDate" jdbcType="TIMESTAMP"/>
        <result column="graduation_date" property="graduationDate" jdbcType="TIMESTAMP"/>
        <result column="major" property="major" jdbcType="VARCHAR"/>
        <result column="job_title" property="jobTitle" jdbcType="VARCHAR"/>
        <result column="second_education_key" property="secondEducationKey" jdbcType="VARCHAR"/>
        <result column="second_education_value" property="secondEducationValue" jdbcType="VARCHAR"/>
        <result column="second_education_cert_no" property="secondEducationCertNo" jdbcType="VARCHAR"/>

        <!-- 任职要求映射 -->
        <result column="english_level" property="englishLevel" jdbcType="VARCHAR"/>
        <result column="vessel_key" property="vesselKey" jdbcType="VARCHAR"/>
        <result column="vessel_value" property="vesselValue" jdbcType="VARCHAR"/>
        <result column="salary" property="salary" jdbcType="VARCHAR"/>
        <result column="currency_key" property="currencyKey" jdbcType="VARCHAR"/>
        <result column="currency_value" property="currencyValue" jdbcType="VARCHAR"/>
        <result column="ton_key" property="tonKey" jdbcType="VARCHAR"/>
        <result column="ton_value" property="tonValue" jdbcType="VARCHAR"/>
        <result column="navigating_area_key" property="navigatingAreaKey" jdbcType="VARCHAR"/>
        <result column="navigating_area_value" property="navigatingAreaValue" jdbcType="VARCHAR"/>
        <result column="plan_date" property="planDate" jdbcType="TIMESTAMP"/>
        <result column="other_remark" property="otherRemark" jdbcType="VARCHAR"/>

        <!-- 状态信息映射 -->
        <result column="status_key" property="statusKey" jdbcType="VARCHAR"/>
        <result column="status_value" property="statusValue" jdbcType="VARCHAR"/>
        <result column="on_status_key" property="onStatusKey" jdbcType="VARCHAR"/>
        <result column="on_status_value" property="onStatusValue" jdbcType="VARCHAR"/>

        <!-- 在船信息映射 -->
        <result column="service_id" property="serviceId" jdbcType="VARCHAR"/>
        <result column="on_board_date" property="onBoardDate" jdbcType="TIMESTAMP"/>
        <result column="on_board_time" property="onBoardTime" jdbcType="VARCHAR" />
        <result column="alert_days" property="alertDays" jdbcType="VARCHAR"/>
        <result column="down_board_date" property="downBoardDate" jdbcType="TIMESTAMP"/>

        <!-- 附加信息映射 -->
        <result column="seafarer_headimg_address" property="seafarerHeadimgAddress" jdbcType="VARCHAR"/>
        <result column="seafarer_name_en" property="seafarerNameEn" jdbcType="VARCHAR"/>
        <result column="seafarer_number" property="seafarerNumber" jdbcType="VARCHAR"/>
        <result column="apply_duty_id" property="applyDutyId" jdbcType="VARCHAR"/>
        <result column="apply_duty_name" property="applyDutyName" jdbcType="VARCHAR"/>
        <result column="social_security_no" property="socialSecurityNo" jdbcType="VARCHAR"/>
        <result column="autograph_url" property="autographUrl" jdbcType="VARCHAR"/>
        <result column="current_vessel_id" property="currentVesselId" jdbcType="VARCHAR"/>
        <result column="current_vessel_name" property="currentVesselName" jdbcType="VARCHAR"/>
        <result column="ism_training" property="ismTraining" jdbcType="VARCHAR" />
        <result column="with_medical_social" property="withMedicalSocial" jdbcType="VARCHAR"/>
        <result column="security_background" property="securityBackground" jdbcType="VARCHAR"/>

        <!-- 系统字段映射 -->
        <result column="delete_flag" property="deleteFlag" jdbcType="CHAR"/>
        <result column="update_count" property="updateCount" jdbcType="BIGINT"/>
        <result column="create_date" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="update_date" property="updateDate" jdbcType="TIMESTAMP"/>
        <result column="update_user_id" property="updateUserId" jdbcType="VARCHAR"/>
        <result column="update_flag" property="updateFlag" jdbcType="CHAR"/>
        <result column="now_version_no" property="nowVersionNo" jdbcType="BIGINT"/>
        <result column="prev_version_no" property="prevVersionNo" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 查询所有船员信息（分页） -->
    <select id="selectCrewList" resultMap="CrewSeafarerInfoResultMap">
        SELECT
        seafarer_id,
        comp_id,
        seafarer_name,
        birthday,
        enter_company_date,
        crt_duty_id,
        crt_duty_name,
        crt_level_id,
        crt_level_name,
        nationality_key,
        nationality_value,
        brithplace_prov_key,
        brithplace_prov_value,
        brithplace_city_key,
        brithplace_city_value,
        nation,
        political_key,
        political_value,
        place_of_origin_prov_key,
        place_of_origin_prov_value,
        place_of_origin_city_key,
        place_of_origin_city_value,
        seafarer_id_no,
        marriage_status_key,
        marriage_status_value,
        healthy,
        seafarer_sex_key,
        seafarer_sex_value,
        waistline,
        height,
        blood_key,
        blood_value,
        weight,
        suit_size,
        shoe_size,
        seafarer_type_key,
        seafarer_type_value,
        employment_id,
        employment_name,
        maritime_experience_key,
        maritime_experience_value,
        repatriate_city,
        begin_work_date,
        other_tel,
        email,
        qq_wechat,
        post_code,
        current_address,
        home_address_en,
        census_register,
        liaison_remark,
        owned_out_comp_id,
        owned_out_comp_name,
        vessel_id,
        vessel_name,
        on_duty_id,
        on_duty_name,
        home_address,
        mobile_phone,
        remark_id,
        remark,
        school,
        school_place,
        school_nature_key,
        school_nature_value,
        education_key,
        education_value,
        education_cert_no,
        enrollment_date,
        graduation_date,
        major,
        job_title,
        second_education_key,
        second_education_value,
        second_education_cert_no,
        english_level,
        vessel_key,
        vessel_value,
        salary,
        currency_key,
        currency_value,
        ton_key,
        ton_value,
        navigating_area_key,
        navigating_area_value,
        plan_date,
        other_remark,
        status_key,
        status_value,
        on_status_key,
        on_status_value,
        service_id,
        on_board_date,
        on_board_time,
        alert_days,
        down_board_date,
        seafarer_headimg_address,
        seafarer_name_en,
        seafarer_number,
        apply_duty_id,
        apply_duty_name,
        social_security_no,
        autograph_url,
        current_vessel_id,
        current_vessel_name,
        ism_training,
        with_medical_social,
        security_background,
        delete_flag,
        update_count,
        create_date,
        create_user_id,
        update_date,
        update_user_id,
        update_flag,
        now_version_no,
        prev_version_no
        FROM crew_seafarer_info
        WHERE delete_flag = '0'
        <!-- 船员ID精确查询 -->
        <if test="seafarerId != null and seafarerId != ''">
            AND seafarer_id = #{seafarerId}
        </if>
        <!-- 船员姓名模糊查询 -->
        <if test="seafarerName != null and seafarerName != ''">
            AND seafarer_name LIKE CONCAT('%', #{seafarerName,}, '%')
        </if>
        <!-- 申请职务ID精确查询 -->
        <if test="applyDutyId != null and applyDutyId != ''">
            AND apply_duty_id = #{applyDutyId}
        </if>
        <!-- 排除已删除的数据 -->
        ORDER BY create_date DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计船员总数 -->
    <select id="countCrew" resultType="int">
        SELECT COUNT(*) FROM crew_seafarer_info
        WHERE delete_flag = '0'
        <!-- 船员ID精确查询 -->
        <if test="seafarerId != null and seafarerId != ''">
            AND seafarer_id = #{seafarerId}
        </if>
        <!-- 船员姓名模糊查询 -->
        <if test="seafarerName != null and seafarerName != ''">
            AND seafarer_name LIKE CONCAT('%', #{seafarerName,}, '%')
        </if>
        <!-- 申请职务ID精确查询 -->
        <if test="applyDutyId != null and applyDutyId != ''">
            AND apply_duty_id = #{applyDutyId}
        </if>
    </select>

</mapper>
