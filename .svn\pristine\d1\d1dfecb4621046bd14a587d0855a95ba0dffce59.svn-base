package com.example.multidatasource.service;

import com.example.multidatasource.config.DynamicDataSourceManager;
import com.example.multidatasource.model.SqlRequest;
import com.example.multidatasource.model.SqlResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;
import java.util.Arrays;

/**
 * SQL执行服务
 */
@Slf4j
@Service
public class SqlExecutorService {
    
    @Autowired
    private DynamicDataSourceManager dataSourceManager;
    
    /**
     * 执行SQL查询
     */
    public SqlResponse executeQuery(SqlRequest request) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 参数验证
            validateRequest(request);
            
            DataSource dataSource = dataSourceManager.getDataSource(request.getDataSourceName());
            
            String queryType = request.getQueryType().toUpperCase();
            SqlResponse response;
            
            switch (queryType) {
                case "SELECT":
                    response = executeSelectQuery(dataSource, request);
                    break;
                case "INSERT":
                case "UPDATE":
                case "DELETE":
                    response = executeUpdateQuery(dataSource, request);
                    break;
                default:
                    throw new IllegalArgumentException("不支持的查询类型: " + queryType);
            }
            
            long executionTime = System.currentTimeMillis() - startTime;
            response.setExecutionTime(executionTime);
            
            log.info("SQL执行完成 - 数据源: {}, 类型: {}, 耗时: {}ms", 
                request.getDataSourceName(), queryType, executionTime);
            
            return response;
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("SQL执行失败 - 数据源: {}, SQL: {}", 
                request.getDataSourceName(), request.getSql(), e);
            
            SqlResponse response = SqlResponse.error("SQL执行失败", e.getMessage());
            response.setExecutionTime(executionTime);
            return response;
        }
    }
    
    /**
     * 执行SELECT查询
     */
    private SqlResponse executeSelectQuery(DataSource dataSource, SqlRequest request) throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            String sql = request.getSql();
            List<Object> parameters = request.getParameters();
            
            // 处理分页
            if (request.getPage() != null && request.getSize() != null) {
                return executePagedQuery(connection, sql, parameters, request);
            } else {
                return executeSimpleQuery(connection, sql, parameters);
            }
        }
    }
    
    /**
     * 执行简单查询
     */
    private SqlResponse executeSimpleQuery(Connection connection, String sql, List<Object> parameters) 
            throws SQLException {
        
        try (PreparedStatement statement = connection.prepareStatement(sql)) {
            setParameters(statement, parameters);
            
            try (ResultSet resultSet = statement.executeQuery()) {
                List<Map<String, Object>> data = convertResultSetToList(resultSet);
                return SqlResponse.success(data, "查询成功");
            }
        }
    }
    
    /**
     * 执行分页查询
     */
    private SqlResponse executePagedQuery(Connection connection, String sql, List<Object> parameters, 
                                        SqlRequest request) throws SQLException {
        
        int page = request.getPage();
        int size = request.getSize();
        int offset = (page - 1) * size;
        
        // 构建分页SQL
        String pagedSql = buildPagedSql(sql, offset, size);
        
        SqlResponse response;
        
        // 执行分页查询
        try (PreparedStatement statement = connection.prepareStatement(pagedSql)) {
            setParameters(statement, parameters);
            
            try (ResultSet resultSet = statement.executeQuery()) {
                List<Map<String, Object>> data = convertResultSetToList(resultSet);
                response = SqlResponse.success(data, "分页查询成功");
            }
        }
        
        // 如果需要总数，执行count查询
        if (Boolean.TRUE.equals(request.getNeedCount())) {
            String countSql = buildCountSql(sql);
            try (PreparedStatement countStatement = connection.prepareStatement(countSql)) {
                setParameters(countStatement, parameters);
                
                try (ResultSet countResultSet = countStatement.executeQuery()) {
                    if (countResultSet.next()) {
                        long totalCount = countResultSet.getLong(1);
                        response.setTotalCount(totalCount);
                        response.setCurrentPage(page);
                        response.setPageSize(size);
                        response.setTotalPages((int) Math.ceil((double) totalCount / size));
                    }
                }
            }
        }
        
        return response;
    }
    
    /**
     * 执行UPDATE/INSERT/DELETE查询
     */
    private SqlResponse executeUpdateQuery(DataSource dataSource, SqlRequest request) throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            String sql = request.getSql();
            List<Object> parameters = request.getParameters();
            
            try (PreparedStatement statement = connection.prepareStatement(sql)) {
                setParameters(statement, parameters);
                
                int affectedRows = statement.executeUpdate();
                return SqlResponse.success(affectedRows, "执行成功，影响 " + affectedRows + " 行");
            }
        }
    }
    
    /**
     * 设置PreparedStatement参数
     */
    private void setParameters(PreparedStatement statement, List<Object> parameters) throws SQLException {
        if (parameters != null && !parameters.isEmpty()) {
            for (int i = 0; i < parameters.size(); i++) {
                statement.setObject(i + 1, parameters.get(i));
            }
        }
    }
    
    /**
     * 将ResultSet转换为List<Map<String, Object>>
     */
    private List<Map<String, Object>> convertResultSetToList(ResultSet resultSet) throws SQLException {
        List<Map<String, Object>> result = new ArrayList<>();
        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();
        
        while (resultSet.next()) {
            Map<String, Object> row = new LinkedHashMap<>();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnLabel(i);
                Object value = resultSet.getObject(i);
                row.put(columnName, value);
            }
            result.add(row);
        }
        
        return result;
    }
    
    /**
     * 构建分页SQL
     */
    private String buildPagedSql(String sql, int offset, int limit) {
        // 简单的LIMIT分页实现，适用于MySQL、PostgreSQL
        // 对于Oracle和SQL Server需要不同的分页语法
        return sql + " LIMIT " + limit + " OFFSET " + offset;
    }
    
    /**
     * 构建COUNT查询SQL
     */
    private String buildCountSql(String sql) {
        // 简单的count包装，实际项目中可能需要更复杂的SQL解析
        return "SELECT COUNT(*) FROM (" + sql + ") AS count_query";
    }
    
    /**
     * 简化的SQL执行方法 - 兼容新的Controller
     */
    public Object executeSql(String dataSourceName, String sql, Object[] parameters) {
        SqlRequest request = new SqlRequest();
        request.setDataSourceName(dataSourceName);
        request.setSql(sql);

        // 转换参数格式
        if (parameters != null) {
            List<Object> paramList = Arrays.asList(parameters);
            request.setParameters(paramList);
        }

        // 根据SQL类型设置查询类型
        String trimmedSql = sql.trim().toLowerCase();
        if (trimmedSql.startsWith("select")) {
            request.setQueryType("SELECT");
        } else if (trimmedSql.startsWith("insert")) {
            request.setQueryType("INSERT");
        } else if (trimmedSql.startsWith("update")) {
            request.setQueryType("UPDATE");
        } else if (trimmedSql.startsWith("delete")) {
            request.setQueryType("DELETE");
        } else {
            request.setQueryType("SELECT"); // 默认为查询
        }

        SqlResponse response = executeQuery(request);

        // 返回数据部分
        if (response.getSuccess()) {
            return response.getData();
        } else {
            throw new RuntimeException(response.getError());
        }
    }

    /**
     * 重载方法 - 支持List参数
     */
    public Object executeSql(String dataSourceName, String sql, List<Object> parameters) {
        Object[] paramArray = parameters != null ? parameters.toArray() : null;
        return executeSql(dataSourceName, sql, paramArray);
    }

    /**
     * 验证请求参数
     */
    private void validateRequest(SqlRequest request) {
        if (StringUtils.isBlank(request.getDataSourceName())) {
            throw new IllegalArgumentException("数据源名称不能为空");
        }

        if (StringUtils.isBlank(request.getSql())) {
            throw new IllegalArgumentException("SQL语句不能为空");
        }

        // 基本的SQL注入防护检查
        String sql = request.getSql().toLowerCase().trim();
        if (sql.contains(";") && !sql.endsWith(";")) {
            throw new IllegalArgumentException("SQL语句包含多条语句，存在安全风险");
        }
    }
}
