<component name="libraryTable">
  <library name="Maven: org.springdoc:springdoc-openapi-webmvc-core:1.7.0">
    <CLASSES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/org/springdoc/springdoc-openapi-webmvc-core/1.7.0/springdoc-openapi-webmvc-core-1.7.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/org/springdoc/springdoc-openapi-webmvc-core/1.7.0/springdoc-openapi-webmvc-core-1.7.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/org/springdoc/springdoc-openapi-webmvc-core/1.7.0/springdoc-openapi-webmvc-core-1.7.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>