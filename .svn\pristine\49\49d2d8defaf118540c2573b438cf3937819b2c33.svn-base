package com.example.multidatasource.crew.dto;

import com.example.multidatasource.common.dto.PageResult;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 封装服务资历和最新考评信息的DTO
 */
@Data
@Schema(description = "服务资历及最新考评信息")
@JsonInclude(JsonInclude.Include.ALWAYS)
public class QualificationWithEvaluationDTO {

    @Schema(description = "服务资历列表")
    private List<Map<String, Object>> qualifications;

    @Schema(description = "分页的服务资历列表")
    private PageResult<Map<String, Object>> pagedQualifications;

    @Schema(description = "最新考评信息")
    private EvaluationInfoDTO latestEvaluation;

    public static QualificationWithEvaluationDTO of(List<Map<String, Object>> qualifications, EvaluationInfoDTO latestEvaluation) {
        QualificationWithEvaluationDTO dto = new QualificationWithEvaluationDTO();
        dto.setQualifications(qualifications);
        dto.setLatestEvaluation(latestEvaluation);
        return dto;
    }

    public static QualificationWithEvaluationDTO of(PageResult<Map<String, Object>> pagedQualifications, EvaluationInfoDTO latestEvaluation) {
        QualificationWithEvaluationDTO dto = new QualificationWithEvaluationDTO();
        dto.setPagedQualifications(pagedQualifications);
        dto.setLatestEvaluation(latestEvaluation);
        return dto;
    }
}

