<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.multidatasource.voyage.mapper.MaritimeBureauConfigMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.multidatasource.voyage.entity.MaritimeBureauConfig">
        <id column="id" property="id"/>
        <result column="bureau_name" property="bureauName"/>
        <result column="alarm_channel_id" property="alarmChannelId"/>
        <result column="notice_channel_id" property="noticeChannelId"/>
        <result column="enabled" property="enabled"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, bureau_name, alarm_channel_id, notice_channel_id, enabled, create_time, update_time
    </sql>

    <!-- 查询所有启用的海事局配置 -->
    <select id="selectEnabledConfigs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM maritime_bureau_config
        WHERE enabled = 1
        ORDER BY bureau_name
    </select>

    <!-- 根据海事局名称查询配置 -->
    <select id="selectByBureauNames" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM maritime_bureau_config
        WHERE bureau_name IN
        <foreach collection="bureauNames" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>
        AND enabled = 1
        ORDER BY bureau_name
    </select>

    <!-- 查询所有海事局配置 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM maritime_bureau_config
        ORDER BY bureau_name
    </select>

    <!-- 根据ID查询 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM maritime_bureau_config
        WHERE id = #{id}
    </select>

    <!-- 插入配置 -->
    <insert id="insert" parameterType="com.example.multidatasource.voyage.entity.MaritimeBureauConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO maritime_bureau_config (
            bureau_name, alarm_channel_id, notice_channel_id, enabled
        ) VALUES (
            #{bureauName}, #{alarmChannelId}, #{noticeChannelId}, #{enabled}
        )
    </insert>

    <!-- 更新配置 -->
    <update id="updateById" parameterType="com.example.multidatasource.voyage.entity.MaritimeBureauConfig">
        UPDATE maritime_bureau_config
        SET bureau_name = #{bureauName},
            alarm_channel_id = #{alarmChannelId},
            notice_channel_id = #{noticeChannelId},
            enabled = #{enabled}
        WHERE id = #{id}
    </update>

    <!-- 删除配置 -->
    <delete id="deleteById">
        DELETE FROM maritime_bureau_config WHERE id = #{id}
    </delete>

</mapper>
