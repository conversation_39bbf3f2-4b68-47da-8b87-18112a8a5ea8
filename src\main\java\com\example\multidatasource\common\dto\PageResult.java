package com.example.multidatasource.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 分页结果DTO
 */
@Data
@Schema(description = "分页查询结果")
@JsonInclude(JsonInclude.Include.ALWAYS)
public class PageResult<T> {
    
    @Schema(description = "数据列表")
    private List<T> records;
    
    @Schema(description = "总记录数", example = "100")
    private Long total;
    
    @Schema(description = "当前页码", example = "1")
    private Integer current;
    
    @Schema(description = "每页大小", example = "10")
    private Integer size;
    
    @Schema(description = "总页数", example = "10")
    private Integer pages;
    
    public PageResult() {}
    
    public PageResult(List<T> records, Long total, Integer current, Integer size) {
        this.records = records;
        this.total = total;
        this.current = current;
        this.size = size;
        this.pages = (int) Math.ceil((double) total / size);
    }
    
    /**
     * 创建分页结果
     */
    public static <T> PageResult<T> of(List<T> records, Long total, Integer current, Integer size) {
        return new PageResult<>(records, total, current, size);
    }
    
    /**
     * 创建空的分页结果
     */
    public static <T> PageResult<T> empty(Integer current, Integer size) {
        return new PageResult<>(new java.util.ArrayList<>(), 0L, current, size);
    }
}
