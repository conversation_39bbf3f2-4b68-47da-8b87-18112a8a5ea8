package com.example.multidatasource.voyage.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 海事信息查询DTO
 */
@Data
@Schema(description = "海事信息查询条件")
public class MaritimeQueryDTO {
    
    @Schema(description = "页码", example = "1")
    private Integer page = 1;
    
    @Schema(description = "每页大小", example = "10")
    private Integer size = 10;
    
    @Schema(description = "海事局名称（可选）")
    private String bureauName;
    
    @Schema(description = "信息类型（可选）：ALARM-航行警告，NOTICE-航行通告")
    private String infoType;
    
    @Schema(description = "开始日期（可选）", example = "2025-08-01")
    private LocalDate startDate;
    
    @Schema(description = "结束日期（可选）", example = "2025-08-13")
    private LocalDate endDate;
    
    @Schema(description = "关键词搜索（可选，搜索标题和内容）")
    private String keyword;
}
