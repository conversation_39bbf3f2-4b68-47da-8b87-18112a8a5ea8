# 海事信息管理API文档

## 概述

海事信息管理模块提供海事局航行警告和通告的爬取、存储、查询和管理功能。支持多个海事局的数据源配置，自动去重，并提供完整的CRUD操作。

## 基础信息

- **模块路径**: `/voyage/maritime`
- **数据源**: voyage数据库
- **支持格式**: JSON
- **认证要求**: 需要身份认证

## 核心功能

### 1. 海事信息爬取
- 支持指定海事局和信息类型的爬取
- 自动去重（基于articleId）
- 容错处理（单个海事局失败不影响其他）
- 返回详细的爬取统计信息

### 2. 海事信息查询
- 分页查询支持
- 多条件筛选（海事局、类型、日期、关键词）
- 详情查询

### 3. 海事局配置管理
- 海事局配置的增删改查
- 支持启用/禁用状态管理

## API接口列表

### 海事信息爬取

#### 1. 爬取海事信息
**接口：POST /voyage/maritime/crawl**

- **入参：**
```json
{
  "bureauNames": ["上海海事局", "天津海事局"],  // 可选，不传则爬取所有启用的海事局
  "infoTypes": ["ALARM", "NOTICE"],           // 可选，不传则爬取两种类型
  "days": 1                                   // 可选，爬取天数，默认1天
}
```

- **出参：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalCount": 25,
    "successCount": 23,
    "failureCount": 2,
    "newCount": 15,
    "updateCount": 8,
    "details": [
      {
        "bureauName": "上海海事局",
        "infoType": "ALARM",
        "status": "SUCCESS",
        "count": 12,
        "errorMessage": null
      },
      {
        "bureauName": "上海海事局",
        "infoType": "NOTICE",
        "status": "SUCCESS", 
        "count": 6,
        "errorMessage": null
      }
    ]
  },
  "success": true,
  "timestamp": 1755055200000
}
```

**说明：**
- 支持批量爬取多个海事局的数据
- 自动根据articleId去重，相同文章ID会覆盖更新
- 单个海事局失败不影响其他海事局的爬取
- 返回详细的成功/失败统计信息

### 海事信息查询

#### 2. 分页查询海事信息
**接口：GET /voyage/maritime/list**

- **入参：**
  - **page**: 页码（默认1）
  - **size**: 每页大小（默认10）
  - **bureauName**: 海事局名称（可选）
  - **infoType**: 信息类型（可选）：ALARM-航行警告，NOTICE-航行通告
  - **startDate**: 开始日期（可选），格式：yyyy-MM-dd
  - **endDate**: 结束日期（可选），格式：yyyy-MM-dd
  - **keyword**: 关键词搜索（可选，搜索标题和内容）

- **出参：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "articleId": "42df53bf-69ae-4f32-9bda-0cae85925a5c",
        "bureauName": "上海海事局",
        "title": "长江口8月13日试航船沪中1878轮长399.9米宽61.5米南槽航道上行—沪航警559/25",
        "infoType": "ALARM",
        "publishDate": "2025-08-12",
        "url": "https://www.msa.gov.cn/page/article.do?articleId=42df53bf-69ae-4f32-9bda-0cae85925a5c",
        "detailContent": "沪航警559/25，长江口8月13日试航船沪中1878轮长399.9米宽61.5米南槽航道上行1100时过南槽灯船1400时过九段灯船1600时过圆圆沙灯船1700时抵长兴沪东船厂请各航船注意",
        "createTime": "2025-08-13 15:20:00",
        "updateTime": "2025-08-13 15:20:00"
      }
    ],
    "total": 100,
    "current": 1,
    "size": 10,
    "pages": 10
  },
  "success": true,
  "timestamp": 1755055200000
}
```

#### 3. 获取海事信息详情
**接口：GET /voyage/maritime/{id}**

- **入参：**
  - **id**: 海事信息ID（路径参数）

- **出参：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "articleId": "42df53bf-69ae-4f32-9bda-0cae85925a5c",
    "bureauName": "上海海事局",
    "title": "长江口8月13日试航船沪中1878轮长399.9米宽61.5米南槽航道上行—沪航警559/25",
    "infoType": "ALARM",
    "publishDate": "2025-08-12",
    "url": "https://www.msa.gov.cn/page/article.do?articleId=42df53bf-69ae-4f32-9bda-0cae85925a5c",
    "detailContent": "沪航警559/25，长江口8月13日试航船沪中1878轮长399.9米宽61.5米南槽航道上行1100时过南槽灯船1400时过九段灯船1600时过圆圆沙灯船1700时抵长兴沪东船厂请各航船注意",
    "createTime": "2025-08-13 15:20:00",
    "updateTime": "2025-08-13 15:20:00"
  },
  "success": true,
  "timestamp": 1755055200000
}
```

### 海事局配置管理

#### 4. 查询所有海事局配置
**接口：GET /voyage/maritime/bureau/list**

- **入参：** 无

- **出参：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "bureauName": "上海海事局",
      "alarmChannelId": "94DF14CE-1110-415D-A44E-67593E76619F",
      "noticeChannelId": "8DBDED82-F3E5-413B-824E-51445C79726C",
      "enabled": true,
      "createTime": "2025-08-13 15:00:00",
      "updateTime": "2025-08-13 15:00:00"
    }
  ],
  "success": true,
  "timestamp": 1755055200000
}
```

#### 5. 获取海事局配置详情
**接口：GET /voyage/maritime/bureau/{id}**

- **入参：**
  - **id**: 海事局配置ID（路径参数）

- **出参：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "bureauName": "上海海事局",
    "alarmChannelId": "94DF14CE-1110-415D-A44E-67593E76619F",
    "noticeChannelId": "8DBDED82-F3E5-413B-824E-51445C79726C",
    "enabled": true,
    "createTime": "2025-08-13 15:00:00",
    "updateTime": "2025-08-13 15:00:00"
  },
  "success": true,
  "timestamp": 1755055200000
}
```

#### 6. 新增海事局配置
**接口：POST /voyage/maritime/bureau**

- **入参：**
```json
{
  "bureauName": "新海事局",
  "alarmChannelId": "ALARM-CHANNEL-ID",
  "noticeChannelId": "NOTICE-CHANNEL-ID",
  "enabled": true
}
```

- **出参：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 21,
    "bureauName": "新海事局",
    "alarmChannelId": "ALARM-CHANNEL-ID",
    "noticeChannelId": "NOTICE-CHANNEL-ID",
    "enabled": true,
    "createTime": "2025-08-13 15:30:00",
    "updateTime": "2025-08-13 15:30:00"
  },
  "success": true,
  "timestamp": 1755055200000
}
```

#### 7. 更新海事局配置
**接口：PUT /voyage/maritime/bureau/{id}**

- **入参：**
  - **id**: 海事局配置ID（路径参数）
  - **请求体：**
```json
{
  "bureauName": "更新后的海事局名称",
  "alarmChannelId": "NEW-ALARM-CHANNEL-ID",
  "noticeChannelId": "NEW-NOTICE-CHANNEL-ID",
  "enabled": false
}
```

- **出参：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "bureauName": "更新后的海事局名称",
    "alarmChannelId": "NEW-ALARM-CHANNEL-ID",
    "noticeChannelId": "NEW-NOTICE-CHANNEL-ID",
    "enabled": false,
    "createTime": "2025-08-13 15:00:00",
    "updateTime": "2025-08-13 15:35:00"
  },
  "success": true,
  "timestamp": 1755055200000
}
```

#### 8. 删除海事局配置
**接口：DELETE /voyage/maritime/bureau/{id}**

- **入参：**
  - **id**: 海事局配置ID（路径参数）

- **出参：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": null,
  "success": true,
  "timestamp": 1755055200000
}
```

## 数据字典

### 信息类型 (infoType)
- **ALARM**: 航行警告
- **NOTICE**: 航行通告

### 爬取状态 (status)
- **SUCCESS**: 成功
- **FAILURE**: 失败

### 海事局列表
系统预置了20个海事局的配置，包括：
- 上海海事局
- 天津海事局
- 辽宁海事局
- 河北海事局
- 山东海事局
- 浙江海事局
- 福建海事局
- 广东海事局
- 广西海事局
- 海南海事局
- 长江海事局
- 江苏海事局
- 深圳海事局
- 连云港海事局
- 江苏省地方海事局
- 江西省地方海事局
- 黑龙江海事局
- 重庆地方海事局
- 浙江省地方海事局
- 湖北省地方海事局

## 使用示例

### 爬取上海海事局最近3天的数据
```bash
curl -X POST "http://localhost:8080/voyage/maritime/crawl" \
  -H "Content-Type: application/json" \
  -d '{
    "bureauNames": ["上海海事局"],
    "infoTypes": ["ALARM", "NOTICE"],
    "days": 3
  }'
```

### 查询包含"船舶"关键词的航行警告
```bash
curl "http://localhost:8080/voyage/maritime/list?page=1&size=10&infoType=ALARM&keyword=船舶"
```

### 查询指定日期范围的海事信息
```bash
curl "http://localhost:8080/voyage/maritime/list?startDate=2025-08-01&endDate=2025-08-13&bureauName=上海海事局"
```

## 注意事项

1. **爬取频率控制**: 建议在夜间进行爬取，避免对海事局网站造成过大压力
2. **数据去重**: 系统会根据articleId自动去重，相同文章会覆盖更新
3. **错误处理**: 单个海事局爬取失败不会影响其他海事局的爬取
4. **日志记录**: 生产环境只输出ERROR级别日志，其他环境输出INFO级别
5. **数据源**: 所有操作都在voyage数据库中进行

## 技术实现

- **爬虫引擎**: 基于OkHttp + Jsoup的Java爬虫
- **数据存储**: MySQL数据库，支持事务
- **去重策略**: 基于articleId的唯一约束
- **容错机制**: 单点失败不影响整体爬取
- **分页查询**: 支持多条件组合查询和分页
