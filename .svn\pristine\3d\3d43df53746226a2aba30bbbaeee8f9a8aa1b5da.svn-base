package com.example.multidatasource.voyage.schedule;

import com.example.multidatasource.voyage.dto.MaritimeCrawlRequestDTO;
import com.example.multidatasource.voyage.dto.MaritimeCrawlResultDTO;
import com.example.multidatasource.voyage.service.MaritimeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * 海事信息定时爬取任务
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "maritime.schedule.enabled", havingValue = "true", matchIfMissing = false)
public class MaritimeScheduleTask {

    @Autowired
    private MaritimeService maritimeService;

    /**
     * 每小时执行海事信息爬取任务
     * cron表达式：秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void hourlyCrawl() {
        log.info("开始执行每小时海事信息爬取任务");

        try {
            // 不指定bureauNames，让Service从数据库获取所有启用的海事局
            MaritimeCrawlRequestDTO request = MaritimeCrawlRequestDTO.builder()
                    .infoTypes(Arrays.asList("ALARM", "NOTICE"))
                    .days(1) // 爬取最近1天的数据
                    .build();

            MaritimeCrawlResultDTO result = maritimeService.crawlMaritimeInfo(request);

            log.info("每小时海事信息爬取任务完成，结果: 成功={}, 新增={}, 更新={}, 失败={}, 消息={}",
                    result.getSuccess(), result.getNewCount(), result.getUpdateCount(),
                    result.getFailCount(), result.getMessage());

        } catch (Exception e) {
            log.error("每小时海事信息爬取任务执行失败", e);
        }
    }

    /**
     * 手动触发爬取（用于测试）
     */
    public void manualCrawl() {
        log.info("手动触发爬取任务");
        hourlyCrawl();
    }
}
