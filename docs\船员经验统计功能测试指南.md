# 船员经验统计功能测试指南

## 测试概述

本文档提供船员经验统计功能的详细测试指南，包括测试用例、预期结果和问题排查方法。

## 功能范围

### 涉及接口
1. **单个换班匹配**: `POST /api/crew/match-single-shift-change`
2. **全部候选人匹配**: `POST /api/crew/match-all-candidates-v2`

### 新增字段
1. **个人统计字段（3个）**: `companyYears`, `dutyYears`, `vesselTypeYears`
2. **组合统计字段（3个）**: `groupCompanyYears`, `groupDutyYears`, `groupVesselTypeYears`
3. **搭档信息字段（2个）**: `partnerDutyName`, `partnerSeafarerName`

## 测试用例

### 测试用例1：基础个人统计验证
**目的**: 验证候选人个人统计字段计算正确性

**测试步骤**:
1. 选择一个有明确入司日期的候选人
2. 查询该候选人的服务履历记录
3. 调用匹配接口
4. 验证返回的个人统计字段

**验证点**:
- `companyYears` = (当前日期 - 入司日期) / 360，保留2位小数
- `dutyYears` = 该职务所有服务记录天数总和 / 360，保留2位小数
- `vesselTypeYears` = 该船型所有服务记录天数总和 / 360，保留2位小数

### 测试用例2：职务组合统计验证
**目的**: 验证4个职务组合的统计计算

**测试数据**:
```
职务组合1: 轮机长 ↔ 大管轮
职务组合2: 船长 ↔ 大副
职务组合3: 二管轮 ↔ 三管轮
职务组合4: 二副 ↔ 三副
```

**测试步骤**:
1. 为每个职务组合准备测试数据
2. 确保有在船的搭档船员
3. 调用匹配接口
4. 验证组合统计字段

**验证点**:
- `groupCompanyYears` = 候选人入司年数 + 搭档入司年数
- `groupDutyYears` = 候选人职务年数 + 搭档职务年数
- `groupVesselTypeYears` = 候选人船型年数 + 搭档船型年数

### 测试用例3：搭档信息验证
**目的**: 验证搭档信息字段的正确性

**测试场景**:
1. **有搭档**: 存在对应职务的在船船员
2. **无搭档**: 不存在对应职务的在船船员
3. **多个搭档**: 存在多个相同职务的在船船员
4. **非组合职务**: 不在4个组合范围内的职务

**验证点**:
- `partnerDutyName` 正确映射搭档职务
- `partnerSeafarerName` 正确显示搭档姓名和人数

### 测试用例4：边界情况测试
**目的**: 验证各种边界情况的处理

**测试场景**:
1. **新入司船员**: 入司时间很短的候选人
2. **无服务记录**: 没有服务履历的候选人
3. **数据异常**: 日期为空或异常的情况
4. **跨数据源**: 验证voyage库船舶信息查询

## 测试数据准备

### 数据库表检查
```sql
-- 检查候选人基础信息
SELECT seafarer_id, seafarer_name, apply_duty_name, enter_company_date, status_key
FROM crew_seafarer_info 
WHERE delete_flag = '0' AND status_key != 'ONBOARD'
LIMIT 10;

-- 检查在船船员信息
SELECT seafarer_id, seafarer_name, apply_duty_name, vessel_id, status_key
FROM crew_seafarer_info 
WHERE delete_flag = '0' AND status_key = 'ONBOARD'
ORDER BY apply_duty_name;

-- 检查服务履历记录
SELECT seafarer_id, vessel_id, duty_id, on_board_date, down_board_date
FROM crew_seafarer_service_qualification_info 
WHERE delete_flag = '0'
ORDER BY seafarer_id, on_board_date DESC;

-- 检查船舶类型信息（跨数据源）
SELECT vessel_id, vessel_name, vessel_type_flag
FROM common_vessel 
LIMIT 10;
```

## 问题排查指南

### 常见问题1：搭档船员姓名过多（已修复）
**问题描述**: `partnerSeafarerName`字段返回了所有船舶的搭档，而不是当前船舶的搭档

**错误示例**:
```
"partnerSeafarerName": "严明勇, 付永胜, 冯海波, 刘义山, 刘孙瑾, 刘明强, 刘晋杰, 吕月锋, 周世珊, 朱立杰, 梁先兵, 王鼎, 盛波, 祝存记, 肖志峰, 肖良凰, 肖阿平, 肖雄鹏, 莫观仲, 董自健, 詹国斌, 郑居高, 郑江山, 陈丁来, 陈子斌, 陈毅兴, 陈翔（27人）"
```

**根本原因**: `getPartnerStats`查询缺少按`vesselId`过滤的条件

**修复方案**:
1. 修改Mapper接口，添加`vesselId`参数
2. 修改SQL查询，添加`AND partner.vessel_id = #{vesselId}`条件
3. 修改Service层调用，传递当前在船船员的`vesselId`

**修复后效果**: 只返回当前船舶（如"兴通56"）的搭档船员

### 常见问题2：个人统计字段为0或null
**可能原因**:
- 入司日期为空
- 服务履历记录缺失
- 船舶类型信息查询失败

**排查步骤**:
1. 检查`crew_seafarer_info.enter_company_date`
2. 检查`crew_seafarer_service_qualification_info`记录
3. 检查跨数据源查询是否正常

### 常见问题3：组合统计异常
**可能原因**:
- 搭档查询失败
- 职务映射关系错误
- 数据类型转换异常

**排查步骤**:
1. 检查日志中的搭档查询异常
2. 验证`DUTY_PARTNER_MAP`映射关系
3. 检查数据库连接状态

### 常见问题4：搭档信息显示异常
**可能原因**:
- SQL查询语法错误
- 在船船员数据异常
- 字符串拼接问题

**排查步骤**:
1. 直接执行`getPartnerStats`SQL
2. 检查在船船员的`status_key`字段
3. 验证`GROUP_CONCAT`函数执行结果

## 日志监控

### 关键日志位置
```
获取搭档统计信息失败: partnerDutyName={}, targetVesselTypeFlag={}
```

### 性能监控
- SQL查询执行时间
- 接口响应时间
- 数据库连接池状态

## 回滚方案

如果发现严重问题，可以通过以下方式回滚：

1. **SVN回滚**: 回滚到功能开发前的版本
2. **配置开关**: 通过配置文件禁用统计功能（如需要）
3. **数据修复**: 修复异常的统计数据

## 验收标准

### 功能验收
- ✅ 个人统计字段计算准确
- ✅ 组合统计字段计算准确
- ✅ 搭档信息显示正确
- ✅ 边界情况处理正常

### 性能验收
- ✅ 接口响应时间 < 3秒
- ✅ SQL查询时间 < 1秒
- ✅ 无内存泄漏

### 稳定性验收
- ✅ 异常情况优雅降级
- ✅ 日志记录完整
- ✅ 数据一致性保证
