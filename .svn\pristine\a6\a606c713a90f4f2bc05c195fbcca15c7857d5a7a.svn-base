<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.multidatasource.crew.mapper.SeafarerScheduleMapper">

    <!-- 结果映射 -->
    <resultMap id="CrewSeafarerInfoResultMap" type="com.example.multidatasource.crew.entity.CrewSeafarerInfo">
        <!-- 主键映射 -->
        <id column="seafarer_id" property="seafarerId" jdbcType="VARCHAR"/>

        <!-- 基本信息映射 -->
        <result column="comp_id" property="compId" jdbcType="VARCHAR"/>
        <result column="seafarer_name" property="seafarerName" jdbcType="VARCHAR" />
        <result column="birthday" property="birthday" jdbcType="TIMESTAMP"/>
        <result column="enter_company_date" property="enterCompanyDate" jdbcType="TIMESTAMP"/>
        <result column="crt_duty_id" property="crtDutyId" jdbcType="VARCHAR"/>
        <result column="crt_duty_name" property="crtDutyName" jdbcType="VARCHAR"/>
        <result column="crt_level_id" property="crtLevelId" jdbcType="VARCHAR"/>
        <result column="crt_level_name" property="crtLevelName" jdbcType="VARCHAR"/>
        <result column="nationality_key" property="nationalityKey" jdbcType="VARCHAR"/>
        <result column="nationality_value" property="nationalityValue" jdbcType="VARCHAR"/>

        <!-- 出生地信息映射 -->
        <result column="brithplace_prov_key" property="brithplaceProvKey" jdbcType="INTEGER"/>
        <result column="brithplace_prov_value" property="brithplaceProvValue" jdbcType="VARCHAR"/>
        <result column="brithplace_city_key" property="brithplaceCityKey" jdbcType="INTEGER"/>
        <result column="brithplace_city_value" property="brithplaceCityValue" jdbcType="VARCHAR"/>

        <!-- 个人信息映射 -->
        <result column="nation" property="nation" jdbcType="VARCHAR"/>
        <result column="political_key" property="politicalKey" jdbcType="VARCHAR"/>
        <result column="political_value" property="politicalValue" jdbcType="VARCHAR"/>
        <result column="place_of_origin_prov_key" property="placeOfOriginProvKey" jdbcType="INTEGER"/>
        <result column="place_of_origin_prov_value" property="placeOfOriginProvValue" jdbcType="VARCHAR"/>
        <result column="place_of_origin_city_key" property="placeOfOriginCityKey" jdbcType="INTEGER"/>
        <result column="place_of_origin_city_value" property="placeOfOriginCityValue" jdbcType="VARCHAR"/>
        <result column="seafarer_id_no" property="seafarerIdNo" jdbcType="VARCHAR"/>
        <result column="marriage_status_key" property="marriageStatusKey" jdbcType="VARCHAR"/>
        <result column="marriage_status_value" property="marriageStatusValue" jdbcType="VARCHAR"/>
        <result column="healthy" property="healthy" jdbcType="VARCHAR"/>
        <result column="seafarer_sex_key" property="seafarerSexKey" jdbcType="VARCHAR"/>
        <result column="seafarer_sex_value" property="seafarerSexValue" jdbcType="VARCHAR"/>
        <result column="waistline" property="waistline" jdbcType="VARCHAR"/>
        <result column="height" property="height" jdbcType="VARCHAR"/>
        <result column="blood_key" property="bloodKey" jdbcType="VARCHAR"/>
        <result column="blood_value" property="bloodValue" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="suit_size" property="suitSize" jdbcType="VARCHAR"/>
        <result column="shoe_size" property="shoeSize" jdbcType="VARCHAR"/>

        <!-- 职业信息映射 -->
        <result column="seafarer_type_key" property="seafarerTypeKey" jdbcType="VARCHAR"/>
        <result column="seafarer_type_value" property="seafarerTypeValue" jdbcType="VARCHAR"/>
        <result column="employment_id" property="employmentId" jdbcType="VARCHAR"/>
        <result column="employment_name" property="employmentName" jdbcType="VARCHAR"/>
        <result column="maritime_experience_key" property="maritimeExperienceKey" jdbcType="VARCHAR"/>
        <result column="maritime_experience_value" property="maritimeExperienceValue" jdbcType="VARCHAR"/>
        <result column="repatriate_city" property="repatriateCity" jdbcType="VARCHAR"/>
        <result column="begin_work_date" property="beginWorkDate" jdbcType="TIMESTAMP"/>

        <!-- 联系方式映射 -->
        <result column="other_tel" property="otherTel" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="qq_wechat" property="qqWechat" jdbcType="VARCHAR"/>
        <result column="post_code" property="postCode" jdbcType="VARCHAR"/>
        <result column="current_address" property="currentAddress" jdbcType="VARCHAR"/>
        <result column="home_address_en" property="homeAddressEn" jdbcType="VARCHAR"/>
        <result column="census_register" property="censusRegister" jdbcType="VARCHAR"/>
        <result column="liaison_remark" property="liaisonRemark" jdbcType="VARCHAR"/>

        <!-- 公司与船舶信息映射 -->
        <result column="owned_out_comp_id" property="ownedOutCompId" jdbcType="VARCHAR"/>
        <result column="owned_out_comp_name" property="ownedOutCompName" jdbcType="VARCHAR"/>
        <result column="vessel_id" property="vesselId" jdbcType="VARCHAR"/>
        <result column="vessel_name" property="vesselName" jdbcType="VARCHAR"/>
        <result column="on_duty_id" property="onDutyId" jdbcType="VARCHAR"/>
        <result column="on_duty_name" property="onDutyName" jdbcType="VARCHAR"/>
        <result column="home_address" property="homeAddress" jdbcType="VARCHAR"/>
        <result column="mobile_phone" property="mobilePhone" jdbcType="VARCHAR"/>

        <!-- 备注信息映射 -->
        <result column="remark_id" property="remarkId" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>

        <!-- 教育背景映射 -->
        <result column="school" property="school" jdbcType="VARCHAR"/>
        <result column="school_place" property="schoolPlace" jdbcType="VARCHAR"/>
        <result column="school_nature_key" property="schoolNatureKey" jdbcType="VARCHAR"/>
        <result column="school_nature_value" property="schoolNatureValue" jdbcType="VARCHAR"/>
        <result column="education_key" property="educationKey" jdbcType="VARCHAR" />
        <result column="education_value" property="educationValue" jdbcType="VARCHAR"/>
        <result column="education_cert_no" property="educationCertNo" jdbcType="VARCHAR"/>
        <result column="enrollment_date" property="enrollmentDate" jdbcType="TIMESTAMP"/>
        <result column="graduation_date" property="graduationDate" jdbcType="TIMESTAMP"/>
        <result column="major" property="major" jdbcType="VARCHAR"/>
        <result column="job_title" property="jobTitle" jdbcType="VARCHAR"/>
        <result column="second_education_key" property="secondEducationKey" jdbcType="VARCHAR"/>
        <result column="second_education_value" property="secondEducationValue" jdbcType="VARCHAR"/>
        <result column="second_education_cert_no" property="secondEducationCertNo" jdbcType="VARCHAR"/>

        <!-- 任职要求映射 -->
        <result column="english_level" property="englishLevel" jdbcType="VARCHAR"/>
        <result column="vessel_key" property="vesselKey" jdbcType="VARCHAR"/>
        <result column="vessel_value" property="vesselValue" jdbcType="VARCHAR"/>
        <result column="salary" property="salary" jdbcType="VARCHAR"/>
        <result column="currency_key" property="currencyKey" jdbcType="VARCHAR"/>
        <result column="currency_value" property="currencyValue" jdbcType="VARCHAR"/>
        <result column="ton_key" property="tonKey" jdbcType="VARCHAR"/>
        <result column="ton_value" property="tonValue" jdbcType="VARCHAR"/>
        <result column="navigating_area_key" property="navigatingAreaKey" jdbcType="VARCHAR"/>
        <result column="navigating_area_value" property="navigatingAreaValue" jdbcType="VARCHAR"/>
        <result column="plan_date" property="planDate" jdbcType="TIMESTAMP"/>
        <result column="other_remark" property="otherRemark" jdbcType="VARCHAR"/>

        <!-- 状态信息映射 -->
        <result column="status_key" property="statusKey" jdbcType="VARCHAR"/>
        <result column="status_value" property="statusValue" jdbcType="VARCHAR"/>
        <result column="on_status_key" property="onStatusKey" jdbcType="VARCHAR"/>
        <result column="on_status_value" property="onStatusValue" jdbcType="VARCHAR"/>

        <!-- 在船信息映射 -->
        <result column="service_id" property="serviceId" jdbcType="VARCHAR"/>
        <result column="on_board_date" property="onBoardDate" jdbcType="TIMESTAMP"/>
        <result column="on_board_time" property="onBoardTime" jdbcType="VARCHAR" />
        <result column="alert_days" property="alertDays" jdbcType="VARCHAR"/>
        <result column="down_board_date" property="downBoardDate" jdbcType="TIMESTAMP"/>

        <!-- 附加信息映射 -->
        <result column="seafarer_headimg_address" property="seafarerHeadimgAddress" jdbcType="VARCHAR"/>
        <result column="seafarer_name_en" property="seafarerNameEn" jdbcType="VARCHAR"/>
        <result column="seafarer_number" property="seafarerNumber" jdbcType="VARCHAR"/>
        <result column="apply_duty_id" property="applyDutyId" jdbcType="VARCHAR"/>
        <result column="apply_duty_name" property="applyDutyName" jdbcType="VARCHAR"/>
        <result column="social_security_no" property="socialSecurityNo" jdbcType="VARCHAR"/>
        <result column="autograph_url" property="autographUrl" jdbcType="VARCHAR"/>
        <result column="current_vessel_id" property="currentVesselId" jdbcType="VARCHAR"/>
        <result column="current_vessel_name" property="currentVesselName" jdbcType="VARCHAR"/>
        <result column="ism_training" property="ismTraining" jdbcType="VARCHAR" />
        <result column="with_medical_social" property="withMedicalSocial" jdbcType="VARCHAR"/>
        <result column="security_background" property="securityBackground" jdbcType="VARCHAR"/>

        <!-- 系统字段映射 -->
        <result column="delete_flag" property="deleteFlag" jdbcType="CHAR"/>
        <result column="update_count" property="updateCount" jdbcType="BIGINT"/>
        <result column="create_date" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="update_date" property="updateDate" jdbcType="TIMESTAMP"/>
        <result column="update_user_id" property="updateUserId" jdbcType="VARCHAR"/>
        <result column="update_flag" property="updateFlag" jdbcType="CHAR"/>
        <result column="now_version_no" property="nowVersionNo" jdbcType="BIGINT"/>
        <result column="prev_version_no" property="prevVersionNo" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 查询所有船员信息（分页） -->
    <select id="selectCrewList" resultMap="CrewSeafarerInfoResultMap">
        SELECT
        seafarer_id,
        comp_id,
        seafarer_name,
        birthday,
        enter_company_date,
        crt_duty_id,
        crt_duty_name,
        crt_level_id,
        crt_level_name,
        nationality_key,
        nationality_value,
        brithplace_prov_key,
        brithplace_prov_value,
        brithplace_city_key,
        brithplace_city_value,
        nation,
        political_key,
        political_value,
        place_of_origin_prov_key,
        place_of_origin_prov_value,
        place_of_origin_city_key,
        place_of_origin_city_value,
        seafarer_id_no,
        marriage_status_key,
        marriage_status_value,
        healthy,
        seafarer_sex_key,
        seafarer_sex_value,
        waistline,
        height,
        blood_key,
        blood_value,
        weight,
        suit_size,
        shoe_size,
        seafarer_type_key,
        seafarer_type_value,
        employment_id,
        employment_name,
        maritime_experience_key,
        maritime_experience_value,
        repatriate_city,
        begin_work_date,
        other_tel,
        email,
        qq_wechat,
        post_code,
        current_address,
        home_address_en,
        census_register,
        liaison_remark,
        owned_out_comp_id,
        owned_out_comp_name,
        vessel_id,
        vessel_name,
        on_duty_id,
        on_duty_name,
        home_address,
        mobile_phone,
        remark_id,
        remark,
        school,
        school_place,
        school_nature_key,
        school_nature_value,
        education_key,
        education_value,
        education_cert_no,
        enrollment_date,
        graduation_date,
        major,
        job_title,
        second_education_key,
        second_education_value,
        second_education_cert_no,
        english_level,
        vessel_key,
        vessel_value,
        salary,
        currency_key,
        currency_value,
        ton_key,
        ton_value,
        navigating_area_key,
        navigating_area_value,
        plan_date,
        other_remark,
        status_key,
        status_value,
        on_status_key,
        on_status_value,
        service_id,
        on_board_date,
        on_board_time,
        alert_days,
        down_board_date,
        seafarer_headimg_address,
        seafarer_name_en,
        seafarer_number,
        apply_duty_id,
        apply_duty_name,
        social_security_no,
        autograph_url,
        current_vessel_id,
        current_vessel_name,
        ism_training,
        with_medical_social,
        security_background,
        delete_flag,
        update_count,
        create_date,
        create_user_id,
        update_date,
        update_user_id,
        update_flag,
        now_version_no,
        prev_version_no
        FROM crew_seafarer_info
        WHERE delete_flag = '0'
        <!-- 船员ID精确查询 -->
        <if test="seafarerId != null and seafarerId != ''">
            AND seafarer_id = #{seafarerId}
        </if>
        <!-- 船员姓名模糊查询 -->
        <if test="seafarerName != null and seafarerName != ''">
            AND seafarer_name LIKE CONCAT('%', #{seafarerName,}, '%')
        </if>
        <!-- 申请职务ID精确查询 -->
        <if test="applyDutyId != null and applyDutyId != ''">
            AND apply_duty_id = #{applyDutyId}
        </if>
        <!-- 排除已删除的数据 -->
        ORDER BY create_date DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计船员总数 -->
    <select id="countCrew" resultType="int">
        SELECT COUNT(*) FROM crew_seafarer_info
        WHERE delete_flag = '0'
        <!-- 船员ID精确查询 -->
        <if test="seafarerId != null and seafarerId != ''">
            AND seafarer_id = #{seafarerId}
        </if>
        <!-- 船员姓名模糊查询 -->
        <if test="seafarerName != null and seafarerName != ''">
            AND seafarer_name LIKE CONCAT('%', #{seafarerName,}, '%')
        </if>
        <!-- 申请职务ID精确查询 -->
        <if test="applyDutyId != null and applyDutyId != ''">
            AND apply_duty_id = #{applyDutyId}
        </if>
    </select>

    <!-- 查询船员证书到期列表 -->
    <select id="getSeafarerCertificateInfo" resultType="java.util.Map">
        SELECT
            a.seafarer_id AS seafarerId,
            a.seafarer_name AS seafarerName,
            a.certificate_id AS certificateId,
            a.certificate_no AS certificateNo,
            a.issue_address AS issueAddress,
            a.issue_date AS issueDate,
            a.expire_date AS expireDate,
            b.crt_id AS crtId,
            b.chinese_name AS chineseName,
            b.english_name AS englishName,
            DATEDIFF(a.expire_date, CURDATE()) AS certificateExpireLeftDays,
            (CASE WHEN DATEDIFF(a.expire_date, CURDATE()) &lt; 240 THEN 1 ELSE 0 END) AS hasExpiringCertificate
        FROM crew_seafarer_certificate_info a
        JOIN crew_main_certificate_parameter b ON a.certificate_id = b.crt_id
        WHERE a.delete_flag = '0'
        AND a.seafarer_id = #{seafarerId}
        ORDER BY a.expire_date ASC
    </select>

    <!-- 查询船员服务资历（扩展版本，包含船员姓名和MMSI码用于跨数据源关联） -->
    <select id="getSeafarerQualificationInfo" resultType="java.util.Map">
        SELECT
            a.seafarer_id AS seafarerId,
            b.seafarer_name AS seafarerName,
            a.comp_name AS compName,
            a.vessel_name AS vesselName,
            a.vessel_type AS vesselType,
            a.vessel_flag AS vesselFlag,
            a.duty_id AS dutyId,
            a.duty_name AS dutyName,
            a.navigating_area AS navigatingArea,
            a.on_board_date AS onBoardDate,
            a.down_board_date AS downBoardDate,
            a.on_board_place AS onBoardPlace,
            a.down_board_place AS downBoardPlace,
            DATEDIFF(IFNULL(a.down_board_date, CURDATE()), a.on_board_date) + 1 AS onBoardDays,
            a.vessel_id AS vesselId,
            cv.mmsi_code AS mmsiCode
        FROM crew_seafarer_service_qualification_info a
        LEFT JOIN crew_seafarer_info b ON a.seafarer_id = b.seafarer_id AND b.delete_flag = '0'
        LEFT JOIN common_vessel cv ON a.vessel_id = cv.vessel_id
        WHERE a.delete_flag = '0'
        AND a.seafarer_id = #{seafarerId}
        <if test="applyDutyId != null and applyDutyId != ''">
            AND a.duty_id = #{applyDutyId}
        </if>
        ORDER BY a.on_board_date DESC
    </select>

    <!-- 查询在船即将到期船员列表 -->
    <select id="getOnBoardCrews" resultType="java.util.Map">
        SELECT
            a.seafarer_id AS seafarerId,
            a.seafarer_name AS seafarerName,
            a.vessel_id AS vesselId,
            a.vessel_name AS vesselName,
            ve.vessel_type_flag AS vesselTypeFlag,
            ve.vessel_type_name AS vesselTypeName,
            a.apply_duty_id AS applyDutyId,
            a.apply_duty_name AS applyDutyName,
            a.crt_level_id AS crtLevelId,
            a.crt_level_name AS crtLevelName,
            a.on_duty_name AS onDutyName,
            a.status_key AS statusKey,
            a.on_board_date AS onBoardDate,
            ROUND(TIMESTAMPDIFF(SECOND, a.on_board_date, NOW()) / 86400.0, 1) AS onBoardDays,
            a.place_of_origin_prov_value AS placeOfOriginProvValue,
            IFNULL(d.have_jiang_certificate, 0) AS haveJiangCertificate,
            ve.mmsi_code AS mmsiCode
        FROM crew_seafarer_info a
        LEFT JOIN common_vessel ve ON a.vessel_id = ve.vessel_id
        LEFT JOIN (
            SELECT ci.seafarer_id, IF(COUNT(*) &gt; 0, 1, 0) AS have_jiang_certificate
            FROM crew_seafarer_certificate_info ci
            JOIN crew_main_certificate_parameter cp ON ci.certificate_id = cp.crt_id
            WHERE ci.delete_flag = '0' AND cp.chinese_name LIKE '%海船船员内河航线行驶资格证明%'
            GROUP BY ci.seafarer_id
        ) d ON a.seafarer_id = d.seafarer_id
        WHERE a.delete_flag = '0'
        AND a.status_key = 'ONBOARD'
        AND a.seafarer_name NOT LIKE '%测试%'
        AND ROUND(TIMESTAMPDIFF(SECOND, a.on_board_date, NOW()) / 86400.0, 1) &gt; #{onBoardDays}
        <if test="vesselId != null and vesselId != ''">
            AND a.vessel_id = #{vesselId}
        </if>
        <if test="vesselIdIn != null and vesselIdIn != ''">
            AND a.vessel_id IN (${vesselIdIn})
        </if>
        <if test="applyDutyId != null and applyDutyId != ''">
            AND a.apply_duty_id = #{applyDutyId}
        </if>
        <if test="applyDutyIdIn != null and applyDutyIdIn != ''">
            AND a.apply_duty_id IN (${applyDutyIdIn})
        </if>
        <if test="haveJiangCertificate != null and haveJiangCertificate != ''">
            AND IFNULL(d.have_jiang_certificate, 0) = #{haveJiangCertificate}
        </if>
        ORDER BY a.apply_duty_id
    </select>

    <!-- 查询下船可上船员列表 -->
    <select id="getAvailableCrews" resultType="java.util.Map">
        SELECT
            a.seafarer_id AS seafarerId,
            a.seafarer_name AS seafarerName,
            a.vessel_name AS vesselName,
            a.apply_duty_id AS applyDutyId,
            a.apply_duty_name AS applyDutyName,
            a.crt_level_id AS crtLevelId,
            a.crt_level_name AS crtLevelName,
            a.on_duty_name AS onDutyName,
            a.status_key AS statusKey,
            a.on_board_date AS onBoardDate,
            a.place_of_origin_prov_value AS placeOfOriginProvValue,
            qua.down_board_date AS downBoardDate,
            IFNULL(ROUND(TIMESTAMPDIFF(SECOND, qua.down_board_date, NOW()) / 86400.0, 1), 0) AS downBoardDays,
            IFNULL(JSON_UNQUOTE(JSON_EXTRACT(b.edit_data, '$.radio_nsh98waz')), '') AS evaluateValue,
            IFNULL(d.have_jiang_certificate, 0) AS haveJiangCertificate,
            e.has_expiring_certificate AS hasExpiringCertificate,
            e.certificates AS certificates,

            <!-- 新增个人统计字段（转换为年，保留2位小数） -->
            ROUND(IFNULL(DATEDIFF(CURDATE(), a.enter_company_date), 0) / 360.0, 2) AS companyYears,
            ROUND(IFNULL(duty_stats.dutyDays, 0) / 360.0, 2) AS dutyYears,
            ROUND(IFNULL(vessel_type_stats.vesselTypeDays, 0) / 360.0, 2) AS vesselTypeYears,

            <!-- 职务组合统计字段（在应用层计算，这里先返回0） -->
            0.00 AS groupCompanyYears,
            0.00 AS groupDutyYears,
            0.00 AS groupVesselTypeYears
        FROM crew_seafarer_info a
        LEFT JOIN (
            SELECT a.seafarer_id, MAX(a.down_board_date) AS down_board_date
            FROM crew_seafarer_service_qualification_info a
            GROUP BY a.seafarer_id
        ) qua ON qua.seafarer_id = a.seafarer_id
        LEFT JOIN crew_evaluate_info c ON a.seafarer_id = c.seafarer_id
        LEFT JOIN crew_evaluate_detail_info b ON c.id = b.evaluate_id
        LEFT JOIN (
            SELECT ci.seafarer_id, IF(COUNT(*) &gt; 0, 1, 0) AS have_jiang_certificate
            FROM crew_seafarer_certificate_info ci
            JOIN crew_main_certificate_parameter cp ON ci.certificate_id = cp.crt_id
            WHERE ci.delete_flag = '0' AND cp.chinese_name LIKE '%海船船员内河航线行驶资格证明%'
            GROUP BY ci.seafarer_id
        ) d ON a.seafarer_id = d.seafarer_id
        JOIN (
            SELECT cer.seafarer_id,
                   MAX(CASE WHEN DATEDIFF(cer.expire_date, CURDATE()) &lt; #{certificateExpireDate} THEN 1 ELSE 0 END) AS has_expiring_certificate,
                   GROUP_CONCAT(cp.chinese_name) AS certificates
            FROM crew_seafarer_certificate_info cer
            JOIN crew_main_certificate_parameter cp ON cer.certificate_id = cp.crt_id
            WHERE cer.delete_flag = '0' AND (cer.expire_date IS NULL OR DATEDIFF(cer.expire_date, CURDATE()) &gt; 0)
            GROUP BY cer.seafarer_id
        ) e ON e.seafarer_id = a.seafarer_id

        <!-- 任该职务年限统计 -->
        LEFT JOIN (
            SELECT
                seafarer_id,
                SUM(DATEDIFF(IFNULL(down_board_date, CURDATE()), on_board_date) + 1) AS dutyDays
            FROM crew_seafarer_service_qualification_info
            WHERE delete_flag = '0'
            <if test="applyDutyId != null and applyDutyId != ''">
                AND duty_id = #{applyDutyId}
            </if>
            GROUP BY seafarer_id
        ) duty_stats ON a.seafarer_id = duty_stats.seafarer_id

        <!-- 任该类型船舶时间统计 -->
        LEFT JOIN (
            SELECT
                q.seafarer_id,
                SUM(DATEDIFF(IFNULL(q.down_board_date, CURDATE()), q.on_board_date) + 1) AS vesselTypeDays
            FROM crew_seafarer_service_qualification_info q
            JOIN common_vessel v ON q.vessel_id = v.vessel_id
            WHERE q.delete_flag = '0'
            <if test="targetVesselTypeFlag != null and targetVesselTypeFlag != ''">
                AND v.vessel_type_flag = #{targetVesselTypeFlag}
            </if>
            GROUP BY q.seafarer_id
        ) vessel_type_stats ON a.seafarer_id = vessel_type_stats.seafarer_id

        WHERE a.delete_flag = '0'
        AND a.status_key != 'ONBOARD'
        AND a.seafarer_name NOT LIKE '%测试%'
        AND (
            (ROUND(TIMESTAMPDIFF(SECOND, qua.down_board_date, NOW()) / 86400.0, 1) &gt; #{downBoardDaysSt}
             AND ROUND(TIMESTAMPDIFF(SECOND, qua.down_board_date, NOW()) / 86400.0, 1) &lt;= #{downBoardDaysEd})
            OR qua.down_board_date IS NULL
        )
        AND c.create_date = (SELECT MAX(ei.create_date) FROM crew_evaluate_info ei WHERE ei.seafarer_id = a.seafarer_id AND delete_flag = '0')
        AND (JSON_UNQUOTE(JSON_EXTRACT(b.edit_data, '$.radio_nsh98waz')) != '差'
             OR CAST(REPLACE(JSON_UNQUOTE(JSON_EXTRACT(b.edit_data, '$.radio_nsh98waz')), '分', '') AS SIGNED) &gt; 60
             OR JSON_UNQUOTE(JSON_EXTRACT(b.edit_data, '$.radio_nsh98waz')) IS NULL)
        <if test="applyDutyId != null and applyDutyId != ''">
            AND a.apply_duty_id = #{applyDutyId}
        </if>
        <if test="applyDutyIdIn != null and applyDutyIdIn != ''">
            AND a.apply_duty_id IN (${applyDutyIdIn})
        </if>
        <if test="crtLevelId != null and crtLevelId != ''">
            AND a.crt_level_id = #{crtLevelId}
        </if>
        <if test="crtLevelIdIn != null and crtLevelIdIn != ''">
            AND a.crt_level_id IN (${crtLevelIdIn})
        </if>
        <if test="haveJiangCertificate != null and haveJiangCertificate != ''">
            AND IFNULL(d.have_jiang_certificate, 0) = #{haveJiangCertificate}
        </if>
        ORDER BY a.apply_duty_id,
                 CASE WHEN JSON_UNQUOTE(JSON_EXTRACT(b.edit_data, '$.radio_nsh98waz')) REGEXP '^[0-9]+分$'
                      THEN CAST(SUBSTRING(JSON_UNQUOTE(JSON_EXTRACT(b.edit_data, '$.radio_nsh98waz')), 1,
                                LENGTH(JSON_UNQUOTE(JSON_EXTRACT(b.edit_data, '$.radio_nsh98waz'))) - 1) AS SIGNED)
                      WHEN JSON_UNQUOTE(JSON_EXTRACT(b.edit_data, '$.radio_nsh98waz')) = '优异' THEN 100
                      WHEN JSON_UNQUOTE(JSON_EXTRACT(b.edit_data, '$.radio_nsh98waz')) = '良好' THEN 85
                      WHEN JSON_UNQUOTE(JSON_EXTRACT(b.edit_data, '$.radio_nsh98waz')) = '合格' THEN 70
                      WHEN JSON_UNQUOTE(JSON_EXTRACT(b.edit_data, '$.radio_nsh98waz')) = '较弱' THEN 55
                      WHEN JSON_UNQUOTE(JSON_EXTRACT(b.edit_data, '$.radio_nsh98waz')) = '差' THEN 40
                      WHEN JSON_UNQUOTE(JSON_EXTRACT(b.edit_data, '$.radio_nsh98waz')) = '新选项' THEN 25
                      ELSE NULL END DESC,
                 IFNULL(ROUND(TIMESTAMPDIFF(SECOND, qua.down_board_date, NOW()) / 86400.0, 1), 0) DESC
    </select>

    <!-- 获取船舶当前船员统计信息 -->
    <select id="getVesselCrewStats" resultType="java.util.Map">
        SELECT
            vessel_id AS vesselId,
            COUNT(*) AS totalCount,
            SUM(CASE WHEN place_of_origin_prov_key = 350000 THEN 1 ELSE 0 END) AS fujianCount,
            ROUND(SUM(CASE WHEN place_of_origin_prov_key = 350000 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS fujianRatio
        FROM crew_seafarer_info
        WHERE delete_flag = '0' AND status_key = 'ONBOARD' AND vessel_id = #{vesselId}
        GROUP BY vessel_id
    </select>

    <!-- 批量获取船员籍贯信息 -->
    <select id="batchGetSeafarerOrigin" resultType="java.util.Map">
        SELECT
            seafarer_id AS seafarerId,
            place_of_origin_prov_key AS placeOfOriginProvKey,
            place_of_origin_prov_value AS placeOfOriginProvValue
        FROM crew_seafarer_info
        WHERE delete_flag = '0'
        AND seafarer_id IN
        <foreach collection="seafarerIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 获取单个船员籍贯信息 -->
    <select id="getSeafarerOrigin" resultType="java.util.Map">
        SELECT
            seafarer_id AS seafarerId,
            place_of_origin_prov_key AS placeOfOriginProvKey,
            place_of_origin_prov_value AS placeOfOriginProvValue
        FROM crew_seafarer_info
        WHERE delete_flag = '0' AND seafarer_id = #{seafarerId}
    </select>

    <!-- 获取搭档统计信息 -->
    <select id="getPartnerStats" resultType="java.util.Map">
        SELECT
            SUM(DATEDIFF(CURDATE(), partner.enter_company_date)) AS partnerCompanyDays,
            SUM(IFNULL(duty_exp.dutyDays, 0)) AS partnerDutyDays,
            SUM(IFNULL(vessel_exp.vesselTypeDays, 0)) AS partnerVesselTypeDays,
            GROUP_CONCAT(DISTINCT partner.seafarer_name ORDER BY partner.seafarer_name SEPARATOR ', ') AS partnerSeafarerNames,
            COUNT(DISTINCT partner.seafarer_id) AS partnerCount
        FROM crew_seafarer_info partner

        <!-- 搭档职务经验统计 -->
        LEFT JOIN (
            SELECT
                seafarer_id,
                SUM(DATEDIFF(IFNULL(down_board_date, CURDATE()), on_board_date) + 1) AS dutyDays
            FROM crew_seafarer_service_qualification_info
            WHERE delete_flag = '0'
            GROUP BY seafarer_id
        ) duty_exp ON partner.seafarer_id = duty_exp.seafarer_id

        <!-- 搭档船舶类型经验统计 -->
        LEFT JOIN (
            SELECT
                q.seafarer_id,
                SUM(DATEDIFF(IFNULL(q.down_board_date, CURDATE()), q.on_board_date) + 1) AS vesselTypeDays
            FROM crew_seafarer_service_qualification_info q
            JOIN common_vessel v ON q.vessel_id = v.vessel_id
            WHERE q.delete_flag = '0'
            <if test="targetVesselTypeFlag != null and targetVesselTypeFlag != ''">
                AND v.vessel_type_flag = #{targetVesselTypeFlag}
            </if>
            GROUP BY q.seafarer_id
        ) vessel_exp ON partner.seafarer_id = vessel_exp.seafarer_id

        WHERE partner.delete_flag = '0'
        AND partner.status_key = 'ONBOARD'
        <if test="partnerDutyName != null and partnerDutyName != ''">
            AND partner.apply_duty_name = #{partnerDutyName}
        </if>
        <if test="vesselId != null and vesselId != ''">
            AND partner.vessel_id = #{vesselId}
        </if>
    </select>

</mapper>
