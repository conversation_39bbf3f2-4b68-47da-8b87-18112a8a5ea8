# 船员服务资历分页功能

## 概述

为`SeafarerScheduleController`中的`getSeafarerQualificationInfo`方法新增了分页功能，解决船员资历数据量大时的性能问题。

## 新增接口

### 分页查询船员服务资历

**接口路径**: `GET /crew/seafarer-schedule/qualification-info-page`

**接口描述**: 分页查询指定船员的服务资历信息，适用于资历数据较多的场景

**请求参数**:
- `seafarerId` (必填): 船员ID
- `applyDutyId` (可选): 申请职务ID，用于筛选特定职务的资历
- `page` (可选): 页码，默认为1
- `size` (可选): 每页大小，默认为10，最大100

**响应格式**:
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "seafarerId": "船员ID",
        "seafarerName": "船员姓名",
        "compName": "公司名称",
        "vesselName": "船舶名称",
        "vesselType": "船舶类型",
        "vesselFlag": "船旗",
        "dutyId": "职务ID",
        "dutyName": "职务名称",
        "navigatingArea": "航行区域",
        "onBoardDate": "上船日期",
        "downBoardDate": "下船日期",
        "onBoardPlace": "上船地点",
        "downBoardPlace": "下船地点",
        "onBoardDays": "在船天数",
        "vesselId": "船舶ID",
        "mmsiCode": "MMSI码",
        "voyageInfo": "航次信息（跨数据源查询）"
      }
    ],
    "total": 100,
    "current": 1,
    "size": 10,
    "pages": 10
  }
}
```

## 功能特性

### 1. 保持原有功能
- 原有的`/qualification-info`接口保持不变，确保向后兼容
- 继续支持跨数据源查询（crew + voyage）
- 保持航次信息扩展功能

### 2. 分页优化
- 数据库层面分页，避免内存中处理大量数据
- 先查询总数，再分页查询具体数据
- 支持空结果的优化处理

### 3. 参数校验
- 页码最小值为1
- 每页大小限制在1-100之间
- 船员ID必填校验

### 4. 性能优化
- 使用LIMIT进行数据库分页
- 只对当前页数据进行航次信息扩展
- 减少跨数据源查询次数

## 使用示例

### 基本分页查询
```bash
GET /crew/seafarer-schedule/qualification-info-page?seafarerId=SF001&page=1&size=10
```

### 按职务筛选分页查询
```bash
GET /crew/seafarer-schedule/qualification-info-page?seafarerId=SF001&applyDutyId=DUTY001&page=2&size=20
```

## 技术实现

### 1. 数据库层面
- 新增`getSeafarerQualificationInfoPage`方法，使用LIMIT分页
- 新增`countSeafarerQualificationInfo`方法，统计总数
- 保持与原查询相同的WHERE条件和排序

### 2. 服务层面
- 先查询总数，判断是否有数据
- 分页查询基础数据
- 对分页结果进行航次信息扩展
- 构建PageResult返回

### 3. 控制器层面
- 添加分页参数校验
- 保持原有接口不变
- 新增分页接口路径

## 注意事项

1. **数据一致性**: 分页查询和总数查询使用相同的WHERE条件
2. **性能考虑**: 航次信息扩展只对当前页数据执行
3. **参数限制**: 每页大小限制在100以内，防止单次查询数据过多
4. **向后兼容**: 原有接口保持不变，新接口为可选使用

## 测试建议

1. 测试基本分页功能
2. 测试边界条件（第一页、最后一页、超出范围）
3. 测试参数校验
4. 测试大数据量场景的性能
5. 测试跨数据源功能是否正常
