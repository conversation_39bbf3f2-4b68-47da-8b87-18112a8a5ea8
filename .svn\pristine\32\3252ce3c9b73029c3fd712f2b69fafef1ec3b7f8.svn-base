package com.example.multidatasource.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 登录响应DTO
 */
@Data
@Schema(description = "登录响应结果")
@JsonInclude(JsonInclude.Include.ALWAYS)
public class LoginResponse {
    
    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String accessToken;
    
    @Schema(description = "令牌类型", example = "Bearer")
    private String tokenType = "Bearer";
    
    @Schema(description = "令牌过期时间（秒）", example = "604800")
    private Long expiresIn;
    
    @Schema(description = "用户信息")
    private UserInfo userInfo;
    
    @Data
    @Schema(description = "用户信息")
    @JsonInclude(JsonInclude.Include.ALWAYS)
    public static class UserInfo {
        @Schema(description = "用户ID", example = "1")
        private Long id;
        
        @Schema(description = "用户名", example = "admin")
        private String username;
        
        @Schema(description = "真实姓名", example = "管理员")
        private String realName;
        
        @Schema(description = "邮箱", example = "<EMAIL>")
        private String email;
        
        @Schema(description = "角色", example = "admin")
        private String role;
        
        @Schema(description = "状态", example = "active")
        private String status;
    }
    
    public static LoginResponse success(String token, Long expiresIn, UserInfo userInfo) {
        LoginResponse response = new LoginResponse();
        response.setAccessToken(token);
        response.setExpiresIn(expiresIn);
        response.setUserInfo(userInfo);
        return response;
    }
}
