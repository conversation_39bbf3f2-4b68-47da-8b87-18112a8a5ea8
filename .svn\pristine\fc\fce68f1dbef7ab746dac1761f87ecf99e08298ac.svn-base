package com.example.multidatasource.crew.service;

import com.example.multidatasource.crew.dto.SeafarerMatchRequestDTO;
import com.example.multidatasource.crew.dto.SeafarerMatchResultDTO;

import java.util.List;
import java.util.Map;

/**
 * 船员匹配服务接口
 * 专门处理船员上下船匹配逻辑
 */
public interface SeafarerMatchingService {

    /**
     * 匹配上下船船员数据（匹配所有候选人）
     * @param request 匹配请求参数
     * @return 匹配结果列表
     */
    List<SeafarerMatchResultDTO> matchAllCandidates(SeafarerMatchRequestDTO request);

    /**
     * 匹配上下船船员数据（每个在船船员只匹配一个最优候选人）
     * @param request 匹配请求参数
     * @return 匹配结果列表
     */
    List<SeafarerMatchResultDTO> matchBestCandidate(SeafarerMatchRequestDTO request);

    /**
     * 兼容原有接口的匹配方法（返回Map格式）
     * @param request 匹配请求参数
     * @return 匹配结果（Map格式，兼容原有接口）
     */
    List<Map<String, Object>> matchSeafarerForShiftChange(SeafarerMatchRequestDTO request);

    /**
     * 兼容原有接口的单个匹配方法（返回Map格式）
     * @param request 匹配请求参数
     * @return 匹配结果（Map格式，兼容原有接口）
     */
    List<Map<String, Object>> matchSingleSeafarerForShiftChange(SeafarerMatchRequestDTO request);
}
