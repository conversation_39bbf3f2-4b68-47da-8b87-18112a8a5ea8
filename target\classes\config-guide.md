# 环境配置说明

## 配置文件说明

### 1. application.yml
- **作用**: 主配置文件，包含所有环境的通用配置
- **内容**: 应用名称、端口、MyBatis配置、Swagger配置等
- **默认环境**: dev（开发环境）

### 2. application-dev.yml
- **作用**: 开发环境配置
- **特点**: 
  - 使用本地数据库（带_dev后缀）
  - 详细的调试日志
  - 较小的连接池配置
  - 暴露所有监控端点
- **适用场景**: 团队开发环境

### 3. application-test.yml
- **作用**: 测试环境配置
- **特点**:
  - 使用测试服务器数据库（带_test后缀）
  - 支持环境变量配置
  - 中等规模的连接池
  - 显示SQL用于调试
- **适用场景**: 集成测试、UAT测试

### 4. application-prod.yml
- **作用**: 生产环境配置
- **特点**:
  - 使用生产数据库服务器
  - 通过环境变量配置敏感信息
  - 大规模连接池配置
  - 精简日志输出
  - 启用SSL连接
  - 性能优化配置
- **适用场景**: 生产部署

### 5. application-local.yml
- **作用**: 本地个人开发环境配置
- **特点**:
  - 使用本地MySQL（无后缀）
  - 最详细的彩色日志输出
  - 最小连接池配置
  - 启用热部署
- **适用场景**: 个人本地开发

## 环境切换方法

### 1. 通过配置文件指定
```yaml
spring:
  profiles:
    active: dev  # 可选: dev, test, prod, local
```

### 2. 通过启动参数指定
```bash
# 开发环境
java -jar app.jar --spring.profiles.active=dev

# 测试环境
java -jar app.jar --spring.profiles.active=test

# 生产环境
java -jar app.jar --spring.profiles.active=prod

# 本地环境
java -jar app.jar --spring.profiles.active=local
```

### 3. 通过环境变量指定
```bash
export SPRING_PROFILES_ACTIVE=prod
java -jar app.jar
```

### 4. 通过Maven指定
```bash
# 开发环境
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 生产环境
mvn spring-boot:run -Dspring-boot.run.profiles=prod
```

## 环境变量配置

### 生产环境数据库配置
```bash
# 船管系统数据库
export DB_CREW_USERNAME=crew_user
export DB_CREW_PASSWORD=secure_password

# 航次管理数据库
export DB_VOYAGE_USERNAME=voyage_user
export DB_VOYAGE_PASSWORD=secure_password

# 货物管理数据库
export DB_CARGO_USERNAME=cargo_user
export DB_CARGO_PASSWORD=secure_password

# 财务管理数据库
export DB_FINANCE_USERNAME=finance_user
export DB_FINANCE_PASSWORD=secure_password
```

## 配置优先级

1. **命令行参数** (最高优先级)
2. **环境变量**
3. **application-{profile}.yml**
4. **application.yml** (最低优先级)

## 最佳实践

### 开发阶段
- 使用 `local` 或 `dev` 环境
- 敏感信息可以直接写在配置文件中

### 测试阶段
- 使用 `test` 环境
- 通过环境变量配置数据库连接

### 生产部署
- 使用 `prod` 环境
- **必须**通过环境变量配置所有敏感信息
- 不要在配置文件中包含生产环境的密码

### 安全建议
1. 生产环境配置文件中不要包含明文密码
2. 使用环境变量或外部配置中心管理敏感信息
3. 定期轮换数据库密码
4. 限制生产环境监控端点的访问权限
