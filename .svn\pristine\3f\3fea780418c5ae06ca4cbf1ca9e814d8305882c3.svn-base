package com.example.multidatasource.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 船员查询参数DTO
 */
@Data
@Schema(description = "船员查询参数")
public class CrewQueryDTO {
    
    @Schema(description = "姓名（支持模糊查询）", example = "张三")
    private String name;
    
    @Schema(description = "职位", example = "船长")
    private String position;
    
    @Schema(description = "状态", example = "active")
    private String status;
    
    @Schema(description = "电话（支持模糊查询）", example = "138")
    private String phone;
    
    @Schema(description = "页码", example = "1")
    private Integer page = 1;
    
    @Schema(description = "每页大小", example = "10")
    private Integer size = 10;
    
    /**
     * 获取偏移量
     */
    public Integer getOffset() {
        return (page - 1) * size;
    }
    
    /**
     * 获取限制数量
     */
    public Integer getLimit() {
        return size;
    }
}
