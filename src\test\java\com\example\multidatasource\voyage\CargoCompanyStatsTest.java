package com.example.multidatasource.voyage;

import com.example.multidatasource.common.dto.CargoCompanyStatsQueryDTO;
import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.voyage.entity.CargoCompanyStats;
import com.example.multidatasource.voyage.service.VoyageService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDate;

/**
 * 货主关联信息查询测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class CargoCompanyStatsTest {

    @Autowired
    private VoyageService voyageService;

    @Test
    public void testGetCargoCompanyStats() {
        // 创建查询参数
        CargoCompanyStatsQueryDTO queryDTO = new CargoCompanyStatsQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setSize(10);
        
        try {
            // 执行查询
            PageResult<CargoCompanyStats> result = voyageService.getCargoCompanyStats(queryDTO);
            
            // 输出结果
            System.out.println("查询结果总数: " + result.getTotal());
            System.out.println("当前页码: " + result.getCurrent());
            System.out.println("每页大小: " + result.getSize());
            System.out.println("总页数: " + result.getPages());
            
            if (result.getRecords() != null && !result.getRecords().isEmpty()) {
                System.out.println("\n货主关联信息统计:");
                for (CargoCompanyStats stats : result.getRecords()) {
                    System.out.println("货主公司: " + stats.getCargoCompanyName());
                    System.out.println("装货总量: " + stats.getLoadUnitAmount());
                    System.out.println("卸货总量: " + stats.getUnloadUnitAmount());
                    System.out.println("货损量: " + stats.getGoodsLossAmount());
                    System.out.println("货损率: " + stats.getGoodsLossRate() + "‰");
                    System.out.println("航次数量: " + stats.getVoyageCount());
                    System.out.println("---");
                }
            } else {
                System.out.println("没有查询到数据");
            }
            
        } catch (Exception e) {
            System.err.println("查询失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGetCargoCompanyStatsWithDateRange() {
        // 创建查询参数（带时间范围）
        CargoCompanyStatsQueryDTO queryDTO = new CargoCompanyStatsQueryDTO();
        queryDTO.setStartDate(LocalDate.of(2024, 1, 1));
        queryDTO.setEndDate(LocalDate.of(2024, 12, 31));
        queryDTO.setPage(1);
        queryDTO.setSize(5);
        
        try {
            // 执行查询
            PageResult<CargoCompanyStats> result = voyageService.getCargoCompanyStats(queryDTO);
            
            System.out.println("指定时间范围查询结果:");
            System.out.println("查询时间范围: " + queryDTO.getStartDate() + " 到 " + queryDTO.getEndDate());
            System.out.println("查询结果总数: " + result.getTotal());
            
        } catch (Exception e) {
            System.err.println("查询失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGetCargoCompanyStatsWithCompanyName() {
        // 创建查询参数（带公司名称模糊查询）
        CargoCompanyStatsQueryDTO queryDTO = new CargoCompanyStatsQueryDTO();
        queryDTO.setCargoCompanyName("海运");  // 模糊查询包含"海运"的公司
        queryDTO.setPage(1);
        queryDTO.setSize(10);
        
        try {
            // 执行查询
            PageResult<CargoCompanyStats> result = voyageService.getCargoCompanyStats(queryDTO);
            
            System.out.println("公司名称模糊查询结果:");
            System.out.println("查询关键词: " + queryDTO.getCargoCompanyName());
            System.out.println("查询结果总数: " + result.getTotal());
            
        } catch (Exception e) {
            System.err.println("查询失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
