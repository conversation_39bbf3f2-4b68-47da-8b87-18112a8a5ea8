package com.example.multidatasource.voyage.mapper;

import com.example.multidatasource.voyage.entity.CargoCompanyStats;
import com.example.multidatasource.voyage.entity.VoyageInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 航次信息Mapper接口
 */
@Mapper
public interface VoyageMapper {

    /**
     * 查询所有航次信息（分页）
     */
    List<VoyageInfo> selectVoyageList(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 根据ID查询航次信息
     */
    VoyageInfo selectVoyageById(@Param("id") String id);

    /**
     * 插入航次信息
     */
    int insertVoyage(VoyageInfo voyageInfo);

    /**
     * 更新航次信息
     */
    int updateVoyage(VoyageInfo voyageInfo);

    /**
     * 删除航次信息
     */
    int deleteVoyageById(@Param("id") Long id);

    /**
     * 根据状态查询航次列表
     */
    List<VoyageInfo> selectVoyageByStatus(@Param("status") String status);

    /**
     * 根据船舶名称查询航次列表
     */
    List<VoyageInfo> selectVoyageByShipName(@Param("shipName") String shipName);

    /**
     * 更新航次状态
     */
    int updateVoyageStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 统计航次总数
     */
    int countVoyage();

    /**
     * 根据多个条件查询航次
     */
    List<VoyageInfo> selectVoyageByConditions(@Param("voyageNo") String voyageNo,
                                             @Param("shipName") String shipName,
                                             @Param("departurePort") String departurePort,
                                             @Param("arrivalPort") String arrivalPort,
                                             @Param("status") String status,
                                             @Param("startDate") LocalDateTime startDate,
                                             @Param("endDate") LocalDateTime endDate,
                                             @Param("offset") Integer offset,
                                             @Param("limit") Integer limit);

    /**
     * 统计符合条件的航次总数
     */
    int countVoyageByConditions(@Param("voyageNo") String voyageNo,
                               @Param("shipName") String shipName,
                               @Param("departurePort") String departurePort,
                               @Param("arrivalPort") String arrivalPort,
                               @Param("status") String status,
                               @Param("startDate") LocalDateTime startDate,
                               @Param("endDate") LocalDateTime endDate);

    /**
     * 获取所有船舶名称列表
     */
    List<String> selectAllShipNames();

    /**
     * 获取所有港口列表
     */
    List<String> selectAllPorts();

    /**
     * 执行自定义SQL查询
     */
    List<Map<String, Object>> executeCustomQuery(@Param("sql") String sql, @Param("params") Object[] params);

    /**
     * 执行自定义SQL更新
     */
    int executeCustomUpdate(@Param("sql") String sql, @Param("params") Object[] params);

    /**
     * 查询货主关联信息统计（分页）
     */
    List<CargoCompanyStats> selectCargoCompanyStats(
            @Param("cargoCompanyName") String cargoCompanyName,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("sortBy") String sortBy,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit
    );

    /**
     * 统计货主关联信息总数
     */
    int countCargoCompanyStats(
            @Param("cargoCompanyName") String cargoCompanyName,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate
    );

    /**
     * 通过船舶名称查询船舶详细信息
     * @param vesselName 船舶名称
     * @return 船舶详细信息（包含navigation_area、gross_tonage、main_engine_power_kw）
     */
    Map<String, Object> getVesselDetailInfo(@Param("vesselName") String vesselName);

    /**
     * 查询所有货品信息
     * @return 货品列表，包含id和cargoName
     */
    List<Map<String, Object>> selectAllCargo();
}
