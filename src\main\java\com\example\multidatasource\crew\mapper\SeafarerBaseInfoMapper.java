package com.example.multidatasource.crew.mapper;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 船员基础信息Mapper
 */
@Mapper
public interface SeafarerBaseInfoMapper {

    /**
     * 查询船舶列表
     * @return 船舶列表
     */
    List<Map<String, Object>> getVesselList();

    /**
     * 查询职务列表
     * @return 职务列表
     */
    List<Map<String, Object>> getCrtDutyList();

    /**
     * 查询证书等级列表
     * @return 证书等级列表
     */
    List<Map<String, Object>> getCrtLevelList();
}
