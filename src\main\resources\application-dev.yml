# 开发环境配置
server:
  port: 54321

# 海事信息定时任务配置
maritime:
  schedule:
    enabled: true  # 是否启用定时任务，默认false

# 多MySQL数据源配置 - 开发环境
datasources:
  # 船管系统数据库 - 开发环境
  crew:
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: ************************************************************************************************************************************************************
    username: root
    password: winsea@2020
    type: mysql
    
  # 航次动态管理数据库 - 开发环境
  voyage:
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: jdbc:mysql://************:3306/hzx_master?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: web
    password: web
    type: mysql
    
  # 货物管理数据库 - 开发环境
  cargo:
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: **************************************************************************************************************************************************************
    username: root
    password: 123456
    type: mysql
    
  # 财务管理数据库 - 开发环境
  finance:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************************
    username: root
    password: 123456
    type: mysql

# HikariCP连接池配置 - 开发环境（较小的连接池）
hikari:
  maximum-pool-size: 10
  minimum-idle: 2
  connection-timeout: 30000
  idle-timeout: 300000
  max-lifetime: 900000
  leak-detection-threshold: 60000

# 日志配置 - 开发环境（详细日志）
logging:
  level:
    root: INFO
    com.example.multidatasource: DEBUG
    org.springframework.jdbc: DEBUG
    org.apache.ibatis: DEBUG
    org.mybatis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/multi-datasource-dev.log
    max-size: 100MB
    max-history: 30

# 开发环境特定配置
management:
  endpoints:
    web:
      exposure:
        include: "*"  # 开发环境暴露所有端点
  endpoint:
    health:
      show-details: always

# 开发环境数据库初始化
spring:
  sql:
    init:
      mode: always  # 开发环境总是执行初始化脚本
      continue-on-error: true

# swagger地址信息
swagger:
  server:
    url: http://192.168.1.50:54321/multi/source/api