<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.multidatasource.voyage.mapper.VoyageMapper">

    <!-- 结果映射 -->
    <resultMap id="VoyageInfoResultMap" type="com.example.multidatasource.voyage.entity.VoyageInfo">
        <id column="id" property="id"/>
        <result column="voyage_no" property="voyageNo"/>
        <result column="ship_name" property="shipName"/>
        <result column="departure_port" property="departurePort"/>
        <result column="arrival_port" property="arrivalPort"/>
        <result column="departure_time" property="departureTime"/>
        <result column="estimated_arrival_time" property="estimatedArrivalTime"/>
        <result column="actual_arrival_time" property="actualArrivalTime"/>
        <result column="status" property="status"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <!-- 查询所有航次信息（分页） -->
    <select id="selectVoyageList" resultMap="VoyageInfoResultMap">
        SELECT * FROM voyage_info 
        ORDER BY departure_time DESC 
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据ID查询航次信息 -->
    <select id="selectVoyageById" resultMap="VoyageInfoResultMap">
        SELECT * FROM voyage_info WHERE id = #{id}
    </select>

    <!-- 插入航次信息 -->
    <insert id="insertVoyage" parameterType="com.example.multidatasource.voyage.entity.VoyageInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO voyage_info (voyage_no, ship_name, departure_port, arrival_port, departure_time, estimated_arrival_time, status, created_time, updated_time)
        VALUES (#{voyageNo}, #{shipName}, #{departurePort}, #{arrivalPort}, #{departureTime}, #{estimatedArrivalTime}, #{status}, NOW(), NOW())
    </insert>

    <!-- 更新航次信息 -->
    <update id="updateVoyage" parameterType="com.example.multidatasource.voyage.entity.VoyageInfo">
        UPDATE voyage_info 
        SET voyage_no = #{voyageNo}, 
            ship_name = #{shipName}, 
            departure_port = #{departurePort}, 
            arrival_port = #{arrivalPort}, 
            departure_time = #{departureTime}, 
            estimated_arrival_time = #{estimatedArrivalTime}, 
            actual_arrival_time = #{actualArrivalTime},
            status = #{status}, 
            updated_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 删除航次信息 -->
    <delete id="deleteVoyageById">
        DELETE FROM voyage_info WHERE id = #{id}
    </delete>

    <!-- 根据状态查询航次列表 -->
    <select id="selectVoyageByStatus" resultMap="VoyageInfoResultMap">
        SELECT * FROM voyage_info WHERE status = #{status} ORDER BY departure_time DESC
    </select>

    <!-- 根据船舶名称查询航次列表 -->
    <select id="selectVoyageByShipName" resultMap="VoyageInfoResultMap">
        SELECT * FROM voyage_info WHERE ship_name LIKE CONCAT('%', #{shipName}, '%') ORDER BY departure_time DESC
    </select>

    <!-- 更新航次状态 -->
    <update id="updateVoyageStatus">
        UPDATE voyage_info SET status = #{status}, updated_time = NOW() WHERE id = #{id}
    </update>

    <!-- 统计航次总数 -->
    <select id="countVoyage" resultType="int">
        SELECT COUNT(*) FROM voyage_info
    </select>

    <!-- 根据多个条件查询航次 -->
    <select id="selectVoyageByConditions" resultMap="VoyageInfoResultMap">
        SELECT * FROM voyage_info 
        <where>
            <if test="voyageNo != null and voyageNo != ''">
                AND voyage_no LIKE CONCAT('%', #{voyageNo}, '%')
            </if>
            <if test="shipName != null and shipName != ''">
                AND ship_name LIKE CONCAT('%', #{shipName}, '%')
            </if>
            <if test="departurePort != null and departurePort != ''">
                AND departure_port LIKE CONCAT('%', #{departurePort}, '%')
            </if>
            <if test="arrivalPort != null and arrivalPort != ''">
                AND arrival_port LIKE CONCAT('%', #{arrivalPort}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="startDate != null">
                AND departure_time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND departure_time &lt;= #{endDate}
            </if>
        </where>
        ORDER BY departure_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 统计符合条件的航次总数 -->
    <select id="countVoyageByConditions" resultType="int">
        SELECT COUNT(*) FROM voyage_info 
        <where>
            <if test="voyageNo != null and voyageNo != ''">
                AND voyage_no LIKE CONCAT('%', #{voyageNo}, '%')
            </if>
            <if test="shipName != null and shipName != ''">
                AND ship_name LIKE CONCAT('%', #{shipName}, '%')
            </if>
            <if test="departurePort != null and departurePort != ''">
                AND departure_port LIKE CONCAT('%', #{departurePort}, '%')
            </if>
            <if test="arrivalPort != null and arrivalPort != ''">
                AND arrival_port LIKE CONCAT('%', #{arrivalPort}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="startDate != null">
                AND departure_time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND departure_time &lt;= #{endDate}
            </if>
        </where>
    </select>

    <!-- 获取所有船舶名称列表 -->
    <select id="selectAllShipNames" resultType="string">
        SELECT DISTINCT ship_name FROM voyage_info WHERE ship_name IS NOT NULL ORDER BY ship_name
    </select>

    <!-- 获取所有港口列表 -->
    <select id="selectAllPorts" resultType="string">
        SELECT DISTINCT port_name FROM (
            SELECT departure_port AS port_name FROM voyage_info WHERE departure_port IS NOT NULL
            UNION
            SELECT arrival_port AS port_name FROM voyage_info WHERE arrival_port IS NOT NULL
        ) AS ports ORDER BY port_name
    </select>

    <!-- 执行自定义SQL查询 -->
    <select id="executeCustomQuery" resultType="map">
        ${sql}
    </select>

    <!-- 执行自定义SQL更新 -->
    <update id="executeCustomUpdate">
        ${sql}
    </update>

</mapper>
