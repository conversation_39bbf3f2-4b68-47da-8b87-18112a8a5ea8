package com.example.multidatasource.crew.service.impl;

import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.common.config.DataSourceContextHolder;
import com.example.multidatasource.crew.dto.SeafarerMatchRequestDTO;
import com.example.multidatasource.crew.dto.SeafarerMatchResultDTO;
import com.example.multidatasource.crew.mapper.SeafarerScheduleMapper;
import com.example.multidatasource.crew.service.SeafarerMatchingService;
import com.example.multidatasource.crew.util.CertificateLevelMatcher;
import com.example.multidatasource.voyage.dto.ShipEngineInfo;
import com.example.multidatasource.voyage.mapper.ShipEngineMapper;
import com.example.multidatasource.voyage.service.VoyageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 船员匹配服务实现类
 * 优化后的匹配逻辑，提升性能和可维护性
 */
@Slf4j
@Service
@DataSource(DataSourceContextHolder.DataSourceType.CREW)
public class SeafarerMatchingServiceImpl implements SeafarerMatchingService {

    @Autowired
    private SeafarerScheduleMapper seafarerScheduleMapper;

    @Autowired
    private VoyageService voyageService;

    @Autowired
    private ShipEngineMapper shipEngineMapper;

    /**
     * 船舶类型与证书的映射关系（支持字符串和整数类型的船舶类型标识）
     */
    private static final Map<String, Set<String>> VESSEL_CERTIFICATE_MAPPING = new HashMap<>();

    /**
     * 职务搭档关系映射
     */
    private static final Map<String, String> DUTY_PARTNER_MAP = new HashMap<>();

    static {
        DUTY_PARTNER_MAP.put("轮机长", "大管轮");
        DUTY_PARTNER_MAP.put("大管轮", "轮机长");
        DUTY_PARTNER_MAP.put("船长", "大副");
        DUTY_PARTNER_MAP.put("大副", "船长");
        DUTY_PARTNER_MAP.put("二管轮", "三管轮");
        DUTY_PARTNER_MAP.put("三管轮", "二管轮");
        DUTY_PARTNER_MAP.put("二副", "三副");
        DUTY_PARTNER_MAP.put("三副", "二副");
    }
    
    static {
        // 1001油化船：T01、T02和T03
        VESSEL_CERTIFICATE_MAPPING.put("1001", new HashSet<>(Arrays.asList("T01", "T02", "T03")));
        // 1002液化气船：T04、T05
        VESSEL_CERTIFICATE_MAPPING.put("1002", new HashSet<>(Arrays.asList("T04", "T05")));
        // 112化学品船：T01和T03
        VESSEL_CERTIFICATE_MAPPING.put("112", new HashSet<>(Arrays.asList("T01", "T03")));
        // 134油船：T01、T02
        VESSEL_CERTIFICATE_MAPPING.put("134", new HashSet<>(Arrays.asList("T01", "T02")));
    }

    /**
     * 评估等级与分数的映射关系
     */
    private static final Map<String, Integer> EVALUATE_SCORE_MAPPING = new HashMap<>();
    
    static {
        EVALUATE_SCORE_MAPPING.put("优异", 100);
        EVALUATE_SCORE_MAPPING.put("良好", 85);
        EVALUATE_SCORE_MAPPING.put("合格", 70);
        EVALUATE_SCORE_MAPPING.put("较弱", 55);
        EVALUATE_SCORE_MAPPING.put("差", 40);
        EVALUATE_SCORE_MAPPING.put("新选项", 25);
    }

    @Override
    public List<SeafarerMatchResultDTO> matchAllCandidates(SeafarerMatchRequestDTO request) {
        log.info("开始匹配所有候选人, 请求参数: {}", request);
        
        try {
            // 1. 获取基础数据
            MatchingData matchingData = getMatchingData(request);
            if (matchingData.onBoardCrews.isEmpty()) {
                log.info("未查询到需要下船的船员");
                return Collections.emptyList();
            }
            
            // 2. 执行匹配逻辑
            List<SeafarerMatchResultDTO> results = new ArrayList<>();
            for (Map<String, Object> onBoardCrew : matchingData.onBoardCrews) {
                SeafarerMatchResultDTO result = new SeafarerMatchResultDTO();
                result.setOnBoardSeafarer(convertToOnBoardSeafarer(onBoardCrew));
                result.setCandidateSeafarers(findAllMatchingCandidates(onBoardCrew, matchingData.availableCrews, request));

                // 注意：在匹配所有候选人模式下，福建省占比信息在每个候选人中单独显示
                // 不在在船员级别显示，因为每个候选人的影响都不同

                results.add(result);
            }
            
            log.info("匹配完成，共处理{}个在船船员", results.size());
            return results;
            
        } catch (Exception e) {
            log.error("匹配所有候选人失败", e);
            throw new RuntimeException("匹配所有候选人失败: " + e.getMessage());
        }
    }

    @Override
    public List<SeafarerMatchResultDTO> matchBestCandidate(SeafarerMatchRequestDTO request) {
        log.info("开始匹配最优候选人, 请求参数: {}", request);
        
        try {
            // 1. 获取基础数据
            MatchingData matchingData = getMatchingData(request);
            if (matchingData.onBoardCrews.isEmpty()) {
                log.info("未查询到需要下船的船员");
                return Collections.emptyList();
            }
            
            // 2. 执行单个匹配逻辑
            Set<String> assignedCandidateIds = new HashSet<>();
            List<SeafarerMatchResultDTO> results = new ArrayList<>();
            
            for (Map<String, Object> onBoardCrew : matchingData.onBoardCrews) {
                SeafarerMatchResultDTO result = new SeafarerMatchResultDTO();
                result.setOnBoardSeafarer(convertToOnBoardSeafarer(onBoardCrew));

                SeafarerMatchResultDTO.CandidateSeafarerDTO bestCandidate = findBestMatchingCandidate(
                        onBoardCrew, matchingData.availableCrews, assignedCandidateIds, request);
                result.setBestCandidate(bestCandidate);

                // 注意：在单个匹配模式下，福建省占比信息在最佳候选人中显示
                // 不在在船员级别显示，避免信息重复

                results.add(result);
            }
            
            log.info("单个匹配完成，共分配{}个候选人", assignedCandidateIds.size());
            return results;
            
        } catch (Exception e) {
            log.error("匹配最优候选人失败", e);
            throw new RuntimeException("匹配最优候选人失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> matchSeafarerForShiftChange(SeafarerMatchRequestDTO request) {
        List<SeafarerMatchResultDTO> results = matchAllCandidates(request);
        return convertToLegacyFormat(results, false);
    }

    @Override
    public List<Map<String, Object>> matchSingleSeafarerForShiftChange(SeafarerMatchRequestDTO request) {
        List<SeafarerMatchResultDTO> results = matchBestCandidate(request);
        return convertToLegacyFormat(results, true);
    }

    /**
     * 获取匹配所需的基础数据
     */
    private MatchingData getMatchingData(SeafarerMatchRequestDTO request) {
        // 查询在船即将到期船员列表
        List<Map<String, Object>> onBoardCrews = seafarerScheduleMapper.getOnBoardCrews(
                request.getOnBoardDays(),
                request.getVesselId(),
                processSplitParam(request.getVesselIdIn()),
                request.getApplyDutyId(),
                processSplitParam(request.getApplyDutyIdIn()),
                request.getHaveJiangCertificate()
        );

        // 查询下船可上船员列表
        List<Map<String, Object>> availableCrews = Collections.emptyList();
        if (!onBoardCrews.isEmpty()) {
            // 获取目标船舶类型（从第一个在船船员信息中获取）
            String targetVesselTypeFlag = (String) onBoardCrews.get(0).get("vesselTypeFlag");

            availableCrews = seafarerScheduleMapper.getAvailableCrews(
                    request.getDownBoardDaysSt(),
                    request.getDownBoardDaysEd(),
                    request.getCertificateExpireDate(),
                    request.getApplyDutyId(),
                    processSplitParam(request.getApplyDutyIdIn()),
                    request.getCrtLevelId(),
                    processSplitParam(request.getCrtLevelIdIn()),
                    request.getHaveJiangCertificate(),
                    targetVesselTypeFlag
            );
            
            // 预处理候选船员数据，计算评估分数
            availableCrews = availableCrews.stream()
                    .peek(this::calculateEvaluateScore)
                    .collect(Collectors.toList());
        }

        // 为在船船员添加船舶引擎信息
        enrichOnBoardCrewsWithEngineInfo(onBoardCrews);

        log.info("查询到{}条在船船员，{}条候选船员", onBoardCrews.size(), availableCrews.size());
        return new MatchingData(onBoardCrews, availableCrews);
    }

    /**
     * 查找所有匹配的候选人
     */
    private List<SeafarerMatchResultDTO.CandidateSeafarerDTO> findAllMatchingCandidates(
            Map<String, Object> onBoardCrew, List<Map<String, Object>> availableCrews, SeafarerMatchRequestDTO request) {

        String onApplyDutyId = (String) onBoardCrew.get("applyDutyId");
        String onVesselTypeFlag = (String) onBoardCrew.get("vesselTypeFlag");
        String vesselId = (String) onBoardCrew.get("vesselId");
        String targetVesselName = (String) onBoardCrew.get("vesselName");
        String replacedSeafarerId = (String) onBoardCrew.get("seafarerId");

        return availableCrews.stream()
                .filter(candidate -> isMatchingCandidate(candidate, onApplyDutyId, onVesselTypeFlag, vesselId, targetVesselName))
                .map(candidate -> {
                    SeafarerMatchResultDTO.CandidateSeafarerDTO dto = convertToCandidateSeafarer(candidate, onVesselTypeFlag, vesselId);

                    // 为每个候选人计算福建省占比影响
                    String candidateId = (String) candidate.get("seafarerId");
                    SeafarerMatchResultDTO.FujianRatioInfo ratioInfo = calculateFujianRatioInfo(
                            vesselId, candidateId, replacedSeafarerId, request);
                    dto.setFujianRatioInfo(ratioInfo);

                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 查找最佳匹配的候选人
     */
    private SeafarerMatchResultDTO.CandidateSeafarerDTO findBestMatchingCandidate(
            Map<String, Object> onBoardCrew, List<Map<String, Object>> availableCrews,
            Set<String> assignedIds, SeafarerMatchRequestDTO request) {

        String onApplyDutyId = (String) onBoardCrew.get("applyDutyId");
        String onVesselTypeFlag = (String) onBoardCrew.get("vesselTypeFlag");
        String vesselId = (String) onBoardCrew.get("vesselId");
        String targetVesselName = (String) onBoardCrew.get("vesselName");
        String replacedSeafarerId = (String) onBoardCrew.get("seafarerId");

        return availableCrews.stream()
                .filter(candidate -> {
                    String candidateId = (String) candidate.get("seafarerId");
                    if (assignedIds.contains(candidateId)) {
                        return false;
                    }

                    return isMatchingCandidate(candidate, onApplyDutyId, onVesselTypeFlag, vesselId, targetVesselName);
                })
                .findFirst()
                .map(candidate -> {
                    String candidateId = (String) candidate.get("seafarerId");
                    assignedIds.add(candidateId);
                    SeafarerMatchResultDTO.CandidateSeafarerDTO result = convertToCandidateSeafarer(candidate, onVesselTypeFlag, vesselId);
                    result.setMatchReason("最优匹配");

                    // 为最佳候选人计算福建省占比影响
                    SeafarerMatchResultDTO.FujianRatioInfo ratioInfo = calculateFujianRatioInfo(
                            vesselId, candidateId, replacedSeafarerId, request);
                    result.setFujianRatioInfo(ratioInfo);

                    return result;
                })
                .orElse(null);
    }

    /**
     * 判断候选人是否匹配
     */
    private boolean isMatchingCandidate(Map<String, Object> candidate, String requiredDutyId, String vesselTypeFlag, String vesselId, String targetVesselName) {
        String candidateDutyId = (String) candidate.get("applyDutyId");
        String certificates = (String) candidate.get("certificates");
        String candidateId = (String) candidate.get("seafarerId");

        // 1. 职务匹配
        if (!requiredDutyId.equals(candidateDutyId)) {
            return false;
        }

        // 2. 船-证匹配
        if (!isCertificateValidForVesselType(certificates, vesselTypeFlag)) {
            return false;
        }

        // 3. 证书等级匹配
        if (!isCertificateLevelMatched(candidate, targetVesselName)) {
            return false;
        }

        // 4. 服务资历匹配
        List<Map<String, Object>> qualifications = seafarerScheduleMapper.getSeafarerQualificationInfo(
                candidateId, candidateDutyId, null, null);

        if (qualifications == null || qualifications.isEmpty()) {
            return false;
        }

        return true;
    }



    /**
     * 船舶-证书匹配验证（优化版本）
     */
    private boolean isCertificateValidForVesselType(String certificates, String vesselTypeFlag) {
        if (!StringUtils.hasText(certificates) || !StringUtils.hasText(vesselTypeFlag)) {
            return false;
        }

        Set<String> requiredCertificates = VESSEL_CERTIFICATE_MAPPING.get(vesselTypeFlag.trim());
        if (requiredCertificates == null) {
            log.warn("未知的船舶类型: {}", vesselTypeFlag);
            return false;
        }

        return requiredCertificates.stream().allMatch(certificates::contains);
    }

    /**
     * 证书等级匹配验证
     */
    private boolean isCertificateLevelMatched(Map<String, Object> candidate, String targetVesselName) {
        try {
            // 1. 获取船员证书等级信息
            String seafarerCertLevel = (String) candidate.get("crtLevelName");
            String applyDutyName = (String) candidate.get("applyDutyName");

            if (!StringUtils.hasText(seafarerCertLevel) || !StringUtils.hasText(applyDutyName)) {
                log.warn("船员证书等级或职务信息缺失: seafarerCertLevel={}, applyDutyName={}",
                        seafarerCertLevel, applyDutyName);
                return false;
            }

            if (!StringUtils.hasText(targetVesselName)) {
                log.warn("目标船舶名称信息缺失: targetVesselName={}", targetVesselName);
                return false;
            }

            // 2. 通过目标船舶名称获取船舶详细信息（跨数据源查询voyage库）
            Map<String, Object> vesselInfo = voyageService.getVesselDetailInfo(targetVesselName);
            if (vesselInfo == null || vesselInfo.isEmpty()) {
                log.warn("无法获取船舶详细信息: targetVesselName={}", targetVesselName);
                return false;
            }

            // 3. 执行证书等级匹配
            boolean matched = CertificateLevelMatcher.isLevelMatched(seafarerCertLevel, applyDutyName, vesselInfo);

            log.debug("证书等级匹配结果: 船员={}, 证书等级={}, 职务={}, 目标船舶={}, 匹配结果={}",
                    candidate.get("seafarerName"), seafarerCertLevel, applyDutyName, targetVesselName, matched);

            return matched;

        } catch (Exception e) {
            log.error("证书等级匹配验证异常: targetVesselName={}, candidate={}", targetVesselName, candidate, e);
            return false;
        }
    }

    /**
     * 计算评估分数
     */
    private void calculateEvaluateScore(Map<String, Object> candidate) {
        String evaluateValue = (String) candidate.get("evaluateValue");
        Integer score = null;

        if (StringUtils.hasText(evaluateValue)) {
            // 处理数字分数格式（如"85分"）
            if (evaluateValue.matches("^\\d+分$")) {
                String numberStr = evaluateValue.substring(0, evaluateValue.length() - 1);
                try {
                    score = Integer.parseInt(numberStr);
                } catch (NumberFormatException e) {
                    log.warn("评估分数解析失败: {}", evaluateValue, e);
                }
            } else {
                // 处理文字评估格式
                score = EVALUATE_SCORE_MAPPING.get(evaluateValue);
            }
        }

        candidate.put("evaluateScore", score != null ? score : 0);
    }

    /**
     * 匹配数据容器类
     */
    private static class MatchingData {
        final List<Map<String, Object>> onBoardCrews;
        final List<Map<String, Object>> availableCrews;

        MatchingData(List<Map<String, Object>> onBoardCrews, List<Map<String, Object>> availableCrews) {
            this.onBoardCrews = onBoardCrews;
            this.availableCrews = availableCrews;
        }
    }

    /**
     * 转换为在船船员DTO
     */
    private SeafarerMatchResultDTO.OnBoardSeafarerDTO convertToOnBoardSeafarer(Map<String, Object> data) {
        SeafarerMatchResultDTO.OnBoardSeafarerDTO dto = new SeafarerMatchResultDTO.OnBoardSeafarerDTO();
        dto.setSeafarerId((String) data.get("seafarerId"));
        dto.setSeafarerName((String) data.get("seafarerName"));
        dto.setVesselId((String) data.get("vesselId"));
        dto.setVesselName((String) data.get("vesselName"));
        dto.setVesselTypeFlag((String) data.get("vesselTypeFlag"));
        dto.setVesselTypeName((String) data.get("vesselTypeName"));
        dto.setApplyDutyId((String) data.get("applyDutyId"));
        dto.setApplyDutyName((String) data.get("applyDutyName"));
        dto.setCrtLevelId((String) data.get("crtLevelId"));
        dto.setCrtLevelName((String) data.get("crtLevelName"));
        dto.setOnDutyName((String) data.get("onDutyName"));
        dto.setStatusKey((String) data.get("statusKey"));
        dto.setOnBoardDate(parseDate(data.get("onBoardDate")));
        dto.setOnBoardDays(parseBigDecimal(data.get("onBoardDays")));
        dto.setPlaceOfOriginProvValue((String) data.get("placeOfOriginProvValue"));
        dto.setHaveJiangCertificate(parseInteger(data.get("haveJiangCertificate")));

        // 船舶引擎信息字段（在应用层聚合时设置）
        dto.setMainEngineModel((String) data.get("mainEngineModel"));
        dto.setMainEngineManufacturer((String) data.get("mainEngineManufacturer"));
        dto.setAuxiliaryEngineModel((String) data.get("auxiliaryEngineModel"));
        dto.setAuxiliaryEngineManufacturer((String) data.get("auxiliaryEngineManufacturer"));

        return dto;
    }

    /**
     * 转换为候选船员DTO
     *
     * 功能说明：
     * 1. 转换基础候选人信息
     * 2. 设置个人统计字段（入司时间、职务年限、船型经验）
     * 3. 计算职务组合统计字段（候选人 + 搭档）
     * 4. 设置搭档信息字段（职务名称、船员姓名）
     *
     * @param data 候选人原始数据（来自getAvailableCrews查询）
     * @param targetVesselTypeFlag 目标船舶类型标识（用于搭档船型经验查询）
     * @param vesselId 当前在船船员的船舶ID（用于查询同船搭档）
     * @return 完整的候选船员DTO对象
     */
    private SeafarerMatchResultDTO.CandidateSeafarerDTO convertToCandidateSeafarer(Map<String, Object> data, String targetVesselTypeFlag, String vesselId) {
        SeafarerMatchResultDTO.CandidateSeafarerDTO dto = new SeafarerMatchResultDTO.CandidateSeafarerDTO();
        dto.setSeafarerId((String) data.get("seafarerId"));
        dto.setSeafarerName((String) data.get("seafarerName"));
        dto.setVesselName((String) data.get("vesselName"));
        dto.setApplyDutyId((String) data.get("applyDutyId"));
        dto.setApplyDutyName((String) data.get("applyDutyName"));
        dto.setCrtLevelId((String) data.get("crtLevelId"));
        dto.setCrtLevelName((String) data.get("crtLevelName"));
        dto.setOnDutyName((String) data.get("onDutyName"));
        dto.setStatusKey((String) data.get("statusKey"));
        dto.setOnBoardDate(parseDate(data.get("onBoardDate")));
        dto.setPlaceOfOriginProvValue((String) data.get("placeOfOriginProvValue"));
        dto.setDownBoardDate(parseDate(data.get("downBoardDate")));
        dto.setDownBoardDays(parseBigDecimal(data.get("downBoardDays")));
        dto.setEvaluateValue((String) data.get("evaluateValue"));
        dto.setEvaluateScore(parseInteger(data.get("evaluateScore")));
        dto.setHaveJiangCertificate(parseInteger(data.get("haveJiangCertificate")));
        dto.setHasExpiringCertificate(parseInteger(data.get("hasExpiringCertificate")));
        dto.setCertificates((String) data.get("certificates"));

        // 个人统计字段（年数）
        dto.setCompanyYears(parseDouble(data.get("companyYears")));
        dto.setDutyYears(parseDouble(data.get("dutyYears")));
        dto.setVesselTypeYears(parseDouble(data.get("vesselTypeYears")));

        // ==================== 职务组合统计字段计算 ====================
        // 步骤1: 获取候选人职务，查找对应的搭档职务
        String candidateDutyName = (String) data.get("applyDutyName");
        String partnerDutyName = DUTY_PARTNER_MAP.get(candidateDutyName);

        // 步骤2: 设置搭档职务名称（用于前端显示）
        dto.setPartnerDutyName(partnerDutyName);

        // 步骤3: 如果存在搭档关系，查询搭档统计信息
        if (partnerDutyName != null) {
            try {
                // 调用专门的搭档统计查询方法（添加vesselId过滤同船搭档）
                Map<String, Object> partnerStats = seafarerScheduleMapper.getPartnerStats(partnerDutyName, targetVesselTypeFlag, vesselId);
                if (partnerStats != null) {
                    // 步骤3.1: 设置搭档船员姓名和人数信息
                    String partnerSeafarerNames = (String) partnerStats.get("partnerSeafarerNames");
                    Integer partnerCount = parseInteger(partnerStats.get("partnerCount"));

                    if (partnerSeafarerNames != null && partnerCount != null && partnerCount > 0) {
                        dto.setPartnerSeafarerName(partnerSeafarerNames + "（" + partnerCount + "人）");
                    } else {
                        dto.setPartnerSeafarerName("暂无在船搭档");
                    }
                    // 步骤3.2: 计算职务组合统计（候选人 + 搭档）
                    // 注意：候选人数据已经是年数，需要转换为天数进行计算
                    Double candidateCompanyDays = parseDouble(data.get("companyYears")) * 360.0;
                    Double candidateDutyDays = parseDouble(data.get("dutyYears")) * 360.0;
                    Double candidateVesselTypeDays = parseDouble(data.get("vesselTypeYears")) * 360.0;

                    // 搭档数据是天数，直接使用
                    Double partnerCompanyDays = parseDouble(partnerStats.get("partnerCompanyDays"));
                    Double partnerDutyDays = parseDouble(partnerStats.get("partnerDutyDays"));
                    Double partnerVesselTypeDays = parseDouble(partnerStats.get("partnerVesselTypeDays"));

                    // 计算组合统计：(候选人天数 + 搭档天数) / 360 = 组合年数，保留2位小数
                    dto.setGroupCompanyYears(Math.round(((candidateCompanyDays != null ? candidateCompanyDays : 0.0) +
                                                        (partnerCompanyDays != null ? partnerCompanyDays : 0.0)) / 360.0 * 100.0) / 100.0);
                    dto.setGroupDutyYears(Math.round(((candidateDutyDays != null ? candidateDutyDays : 0.0) +
                                                     (partnerDutyDays != null ? partnerDutyDays : 0.0)) / 360.0 * 100.0) / 100.0);
                    dto.setGroupVesselTypeYears(Math.round(((candidateVesselTypeDays != null ? candidateVesselTypeDays : 0.0) +
                                                           (partnerVesselTypeDays != null ? partnerVesselTypeDays : 0.0)) / 360.0 * 100.0) / 100.0);
                } else {
                    // 搭档不存在，组合统计 = 候选人统计
                    dto.setPartnerSeafarerName("暂无在船搭档");
                    dto.setGroupCompanyYears(dto.getCompanyYears());
                    dto.setGroupDutyYears(dto.getDutyYears());
                    dto.setGroupVesselTypeYears(dto.getVesselTypeYears());
                }
            } catch (Exception e) {
                log.warn("获取搭档统计信息失败: partnerDutyName={}, targetVesselTypeFlag={}", partnerDutyName, targetVesselTypeFlag, e);
                // 异常时，组合统计 = 候选人统计
                dto.setPartnerSeafarerName("查询搭档信息失败");
                dto.setGroupCompanyYears(dto.getCompanyYears());
                dto.setGroupDutyYears(dto.getDutyYears());
                dto.setGroupVesselTypeYears(dto.getVesselTypeYears());
            }
        } else {
            // 无搭档关系，组合统计 = 候选人统计
            dto.setPartnerSeafarerName("无搭档职务");
            dto.setGroupCompanyYears(dto.getCompanyYears());
            dto.setGroupDutyYears(dto.getDutyYears());
            dto.setGroupVesselTypeYears(dto.getVesselTypeYears());
        }

        return dto;
    }

    /**
     * 转换为兼容原有接口的格式
     */
    private List<Map<String, Object>> convertToLegacyFormat(List<SeafarerMatchResultDTO> results, boolean singleMode) {
        return results.stream().map(result -> {
            Map<String, Object> legacyResult = convertOnBoardSeafarerToMap(result.getOnBoardSeafarer());

            if (singleMode) {
                // 单个匹配模式：shiftCrew是单个对象或空对象
                if (result.getBestCandidate() != null) {
                    legacyResult.put("shiftCrew", convertCandidateSeafarerToMap(result.getBestCandidate()));

                    // 在单个匹配模式下，将候选人的占比信息提升到在船员级别（兼容旧接口）
                    if (result.getBestCandidate().getFujianRatioInfo() != null) {
                        legacyResult.put("fujianRatioInfo", convertFujianRatioInfoToMap(result.getBestCandidate().getFujianRatioInfo()));
                    }
                } else {
                    legacyResult.put("shiftCrew", new HashMap<>());
                }
            } else {
                // 匹配所有模式：shiftCrew是数组，每个候选人都有自己的占比信息
                List<Map<String, Object>> candidateList = result.getCandidateSeafarers().stream()
                        .map(this::convertCandidateSeafarerToMap)
                        .collect(Collectors.toList());
                legacyResult.put("shiftCrew", candidateList);
            }

            return legacyResult;
        }).collect(Collectors.toList());
    }

    /**
     * 转换在船船员DTO为Map
     */
    private Map<String, Object> convertOnBoardSeafarerToMap(SeafarerMatchResultDTO.OnBoardSeafarerDTO dto) {
        Map<String, Object> map = new HashMap<>();
        map.put("seafarerId", dto.getSeafarerId());
        map.put("seafarerName", dto.getSeafarerName());
        map.put("vesselId", dto.getVesselId());
        map.put("vesselName", dto.getVesselName());
        map.put("vesselTypeFlag", dto.getVesselTypeFlag());
        map.put("vesselTypeName", dto.getVesselTypeName());
        map.put("applyDutyId", dto.getApplyDutyId());
        map.put("applyDutyName", dto.getApplyDutyName());
        map.put("crtLevelId", dto.getCrtLevelId());
        map.put("crtLevelName", dto.getCrtLevelName());
        map.put("onDutyName", dto.getOnDutyName());
        map.put("statusKey", dto.getStatusKey());
        map.put("onBoardDate", dto.getOnBoardDate());
        map.put("onBoardDays", dto.getOnBoardDays());
        map.put("placeOfOriginProvValue", dto.getPlaceOfOriginProvValue());
        map.put("haveJiangCertificate", dto.getHaveJiangCertificate());
        return map;
    }

    /**
     * 转换候选船员DTO为Map
     */
    private Map<String, Object> convertCandidateSeafarerToMap(SeafarerMatchResultDTO.CandidateSeafarerDTO dto) {
        Map<String, Object> map = new HashMap<>();
        map.put("seafarerId", dto.getSeafarerId());
        map.put("seafarerName", dto.getSeafarerName());
        map.put("vesselName", dto.getVesselName());
        map.put("applyDutyId", dto.getApplyDutyId());
        map.put("applyDutyName", dto.getApplyDutyName());
        map.put("crtLevelId", dto.getCrtLevelId());
        map.put("crtLevelName", dto.getCrtLevelName());
        map.put("onDutyName", dto.getOnDutyName());
        map.put("statusKey", dto.getStatusKey());
        map.put("onBoardDate", dto.getOnBoardDate());
        map.put("placeOfOriginProvValue", dto.getPlaceOfOriginProvValue());
        map.put("downBoardDate", dto.getDownBoardDate());
        map.put("downBoardDays", dto.getDownBoardDays());
        map.put("evaluateValue", dto.getEvaluateValue());
        map.put("evaluateScore", dto.getEvaluateScore());
        map.put("haveJiangCertificate", dto.getHaveJiangCertificate());
        map.put("hasExpiringCertificate", dto.getHasExpiringCertificate());
        map.put("certificates", dto.getCertificates());
        map.put("matchReason", dto.getMatchReason());

        // 添加福建省占比信息
        if (dto.getFujianRatioInfo() != null) {
            map.put("fujianRatioInfo", convertFujianRatioInfoToMap(dto.getFujianRatioInfo()));
        }

        return map;
    }

    /**
     * 处理分割参数（将逗号分隔的字符串转换为SQL IN子句格式）
     */
    private String processSplitParam(String param) {
        if (!StringUtils.hasText(param)) {
            return null;
        }

        return Arrays.stream(param.split(","))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .map(s -> "'" + s + "'")
                .collect(Collectors.joining(","));
    }

    /**
     * 解析日期
     */
    private LocalDate parseDate(Object dateObj) {
        if (dateObj == null) {
            return null;
        }

        log.debug("解析日期对象: type={}, value={}", dateObj.getClass().getSimpleName(), dateObj);

        if (dateObj instanceof LocalDate) {
            return (LocalDate) dateObj;
        }

        if (dateObj instanceof LocalDateTime) {
            LocalDate result = ((LocalDateTime) dateObj).toLocalDate();
            log.debug("LocalDateTime转换为LocalDate: {} -> {}", dateObj, result);
            return result;
        }

        if (dateObj instanceof java.sql.Date) {
            return ((java.sql.Date) dateObj).toLocalDate();
        }

        if (dateObj instanceof java.sql.Timestamp) {
            return ((java.sql.Timestamp) dateObj).toLocalDateTime().toLocalDate();
        }

        if (dateObj instanceof java.util.Date) {
            return ((java.util.Date) dateObj).toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
        }

        if (dateObj instanceof String) {
            String dateStr = (String) dateObj;
            if (StringUtils.hasText(dateStr)) {
                try {
                    // 尝试解析 yyyy-MM-dd 格式
                    return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                } catch (Exception e1) {
                    try {
                        // 尝试解析 yyyy-MM-dd HH:mm:ss 格式
                        return LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).toLocalDate();
                    } catch (Exception e2) {
                        log.warn("日期解析失败: {}", dateStr, e2);
                    }
                }
            }
        }

        return null;
    }

    /**
     * 解析BigDecimal
     */
    private BigDecimal parseBigDecimal(Object obj) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof BigDecimal) {
            return (BigDecimal) obj;
        }

        if (obj instanceof Number) {
            return BigDecimal.valueOf(((Number) obj).doubleValue());
        }

        if (obj instanceof String) {
            String str = (String) obj;
            if (StringUtils.hasText(str)) {
                try {
                    return new BigDecimal(str);
                } catch (NumberFormatException e) {
                    log.warn("数字解析失败: {}", str, e);
                }
            }
        }

        return null;
    }



    /**
     * 批量检查船员是否来自福建省
     */
    private Map<String, Boolean> batchCheckFujianOrigin(List<String> seafarerIds) {
        if (seafarerIds == null || seafarerIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            List<Map<String, Object>> results = seafarerScheduleMapper.batchGetSeafarerOrigin(seafarerIds);

            return results.stream().collect(Collectors.toMap(
                row -> (String) row.get("seafarerId"),
                row -> {
                    Integer provKey = parseInteger(row.get("placeOfOriginProvKey"));
                    return provKey != null && provKey == 350000;
                }
            ));
        } catch (Exception e) {
            log.error("批量获取船员籍贯信息失败, seafarerIds: {}", seafarerIds, e);
            return seafarerIds.stream().collect(Collectors.toMap(id -> id, id -> false));
        }
    }



    /**
     * 计算福建省占比信息
     */
    private SeafarerMatchResultDTO.FujianRatioInfo calculateFujianRatioInfo(
            String vesselId, String candidateSeafarerId, String replacedSeafarerId, SeafarerMatchRequestDTO request) {

        SeafarerMatchResultDTO.FujianRatioInfo ratioInfo = new SeafarerMatchResultDTO.FujianRatioInfo();
        ratioInfo.setVesselId(vesselId);

        try {
            // 获取当前船舶统计信息
            Map<String, Object> stats = seafarerScheduleMapper.getVesselCrewStats(vesselId);
            if (stats == null) {
                return ratioInfo;
            }

            Integer totalCount = parseInteger(stats.get("totalCount"));
            Integer fujianCount = parseInteger(stats.get("fujianCount"));

            if (totalCount == null || totalCount == 0) {
                return ratioInfo;
            }

            // 设置当前信息
            ratioInfo.setCurrentTotalCount(totalCount);
            ratioInfo.setCurrentFujianCount(fujianCount);
            ratioInfo.setCurrentFujianRatio((double) fujianCount / totalCount);

            // 批量获取籍贯信息
            List<String> seafarerIds = new ArrayList<>();
            seafarerIds.add(replacedSeafarerId);
            if (candidateSeafarerId != null) {
                seafarerIds.add(candidateSeafarerId);
            }
            Map<String, Boolean> fujianMap = batchCheckFujianOrigin(seafarerIds);

            boolean replacedIsFujian = fujianMap.getOrDefault(replacedSeafarerId, false);
            boolean candidateIsFujian = fujianMap.getOrDefault(candidateSeafarerId, false);

            // 计算替换后信息
            int newFujianCount = fujianCount;
            if (replacedIsFujian) newFujianCount--;
            if (candidateIsFujian) newFujianCount++;

            ratioInfo.setAfterTotalCount(totalCount);
            ratioInfo.setAfterFujianCount(newFujianCount);
            ratioInfo.setAfterFujianRatio((double) newFujianCount / totalCount);
            ratioInfo.setRatioChange(ratioInfo.getAfterFujianRatio() - ratioInfo.getCurrentFujianRatio());

            // 设置要求范围和验证结果
            double minRatio = 0.4;
            double maxRatio = 0.7;
            ratioInfo.setRatioRequirement(String.format("%.0f%%-%.0f%%", minRatio * 100, maxRatio * 100));
            ratioInfo.setMeetRatioRequirement(ratioInfo.getAfterFujianRatio() >= minRatio &&
                                            ratioInfo.getAfterFujianRatio() <= maxRatio);

        } catch (Exception e) {
            log.error("计算福建省占比信息失败", e);
        }

        return ratioInfo;
    }

    /**
     * 转换福建省占比信息为Map
     */
    private Map<String, Object> convertFujianRatioInfoToMap(SeafarerMatchResultDTO.FujianRatioInfo ratioInfo) {
        Map<String, Object> map = new HashMap<>();
        map.put("vesselId", ratioInfo.getVesselId());
        map.put("currentTotalCount", ratioInfo.getCurrentTotalCount());
        map.put("currentFujianCount", ratioInfo.getCurrentFujianCount());
        map.put("currentFujianRatio", ratioInfo.getCurrentFujianRatio());
        map.put("afterTotalCount", ratioInfo.getAfterTotalCount());
        map.put("afterFujianCount", ratioInfo.getAfterFujianCount());
        map.put("afterFujianRatio", ratioInfo.getAfterFujianRatio());
        map.put("ratioChange", ratioInfo.getRatioChange());
        map.put("meetRatioRequirement", ratioInfo.getMeetRatioRequirement());
        map.put("ratioRequirement", ratioInfo.getRatioRequirement());
        return map;
    }

    /**
     * 解析Integer
     */
    private Integer parseInteger(Object obj) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof Integer) {
            return (Integer) obj;
        }

        if (obj instanceof Number) {
            return ((Number) obj).intValue();
        }

        if (obj instanceof String) {
            String str = (String) obj;
            if (StringUtils.hasText(str)) {
                try {
                    return Integer.parseInt(str.trim());
                } catch (NumberFormatException e) {
                    log.warn("整数解析失败: {}", str, e);
                }
            }
        }

        return null;
    }

    /**
     * 解析Double
     */
    private Double parseDouble(Object obj) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof Double) {
            return (Double) obj;
        }

        if (obj instanceof Number) {
            return ((Number) obj).doubleValue();
        }

        if (obj instanceof String) {
            String str = (String) obj;
            if (StringUtils.hasText(str)) {
                try {
                    return Double.parseDouble(str.trim());
                } catch (NumberFormatException e) {
                    log.warn("浮点数解析失败: {}", str, e);
                }
            }
        }

        return null;
    }

    /**
     * 为在船船员添加船舶引擎信息
     *
     * 功能说明：
     * 1. 一次性查询所有非国际船舶的引擎信息
     * 2. 构建MMSI码到引擎信息的映射缓存
     * 3. 为每个在船船员从缓存中获取对应的引擎信息
     *
     * 性能优化：
     * - 避免N次数据库查询，改为1次查询 + Map缓存
     * - 查询结果通常只有几十条记录，内存占用很小
     *
     * @param onBoardCrews 在船船员列表（会被直接修改）
     */
    private void enrichOnBoardCrewsWithEngineInfo(List<Map<String, Object>> onBoardCrews) {
        if (onBoardCrews == null || onBoardCrews.isEmpty()) {
            return;
        }

        try {
            // 步骤1: 一次性查询所有船舶引擎信息
            Map<String, ShipEngineInfo> shipEngineMap = loadShipEngineInfoMap();
            log.debug("加载了{}条船舶引擎信息到缓存", shipEngineMap.size());

            // 步骤2: 为每个在船船员设置引擎信息
            for (Map<String, Object> crew : onBoardCrews) {
                setEngineInfoForCrew(crew, shipEngineMap);
            }

            log.debug("成功为{}条在船船员添加引擎信息", onBoardCrews.size());

        } catch (Exception e) {
            log.error("为在船船员添加引擎信息失败", e);
            // 异常时设置默认值，确保接口正常返回
            for (Map<String, Object> crew : onBoardCrews) {
                setDefaultEngineInfo(crew);
            }
        }
    }

    /**
     * 加载所有船舶引擎信息到Map缓存
     *
     * @return MMSI码到船舶引擎信息的映射
     */
    private Map<String, ShipEngineInfo> loadShipEngineInfoMap() {
        List<ShipEngineInfo> shipEngineList = shipEngineMapper.getAllShipEngineInfo();

        return shipEngineList.stream()
                .filter(info -> info.getMmsi() != null && !info.getMmsi().trim().isEmpty())
                .collect(Collectors.toMap(
                        ShipEngineInfo::getMmsi,
                        Function.identity(),
                        (existing, replacement) -> {
                            // 如果有重复MMSI，保留第一个，并记录警告
                            log.warn("发现重复MMSI码: {}, 保留第一个记录", existing.getMmsi());
                            return existing;
                        }
                ));
    }

    /**
     * 为单个在船船员设置引擎信息
     *
     * @param crew 在船船员记录（会被直接修改）
     * @param shipEngineMap 船舶引擎信息缓存
     */
    private void setEngineInfoForCrew(Map<String, Object> crew, Map<String, ShipEngineInfo> shipEngineMap) {
        String mmsiCode = (String) crew.get("mmsiCode");

        if (mmsiCode == null || mmsiCode.trim().isEmpty()) {
            log.debug("船员{}的MMSI码为空，设置默认引擎信息", crew.get("seafarerId"));
            setDefaultEngineInfo(crew);
            return;
        }

        ShipEngineInfo engineInfo = shipEngineMap.get(mmsiCode.trim());
        if (engineInfo != null) {
            crew.put("mainEngineModel", cleanString(engineInfo.getMainEngineModel()));
            crew.put("mainEngineManufacturer", cleanString(engineInfo.getMainEngineManufacturer()));
            crew.put("auxiliaryEngineModel", cleanString(engineInfo.getAuxiliaryEngineModel()));
            crew.put("auxiliaryEngineManufacturer", cleanString(engineInfo.getAuxiliaryEngineManufacturer()));

            log.debug("成功为船员{}设置引擎信息，MMSI: {}", crew.get("seafarerId"), mmsiCode);
        } else {
            log.debug("未找到MMSI码{}对应的船舶引擎信息，设置默认值", mmsiCode);
            setDefaultEngineInfo(crew);
        }
    }

    /**
     * 设置默认的引擎信息（空值）
     *
     * @param crew 在船船员记录（会被直接修改）
     */
    private void setDefaultEngineInfo(Map<String, Object> crew) {
        crew.put("mainEngineModel", "");
        crew.put("mainEngineManufacturer", "");
        crew.put("auxiliaryEngineModel", "");
        crew.put("auxiliaryEngineManufacturer", "");
    }

    /**
     * 清理字符串，去除null和空值
     *
     * @param str 原始字符串
     * @return 清理后的字符串
     */
    private String cleanString(String str) {
        return (str == null || str.trim().isEmpty()) ? "" : str.trim();
    }
}
