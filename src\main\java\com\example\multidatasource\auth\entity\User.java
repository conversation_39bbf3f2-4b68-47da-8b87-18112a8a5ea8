package com.example.multidatasource.auth.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@Data
public class User {
    private Long id;
    private String username;
    private String password;
    private String email;
    private String phone;
    private String realName;
    private String status;
    private String role;
    private LocalDateTime lastLoginTime;
    private String lastLoginIp;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    
    // 业务方法
    public boolean isEnabled() {
        return "active".equals(status);
    }
    
    public boolean isAccountNonExpired() {
        return true;
    }
    
    public boolean isAccountNonLocked() {
        return !"locked".equals(status);
    }
    
    public boolean isCredentialsNonExpired() {
        return true;
    }
}
