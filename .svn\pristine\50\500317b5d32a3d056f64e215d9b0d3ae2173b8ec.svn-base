package com.example.multidatasource.voyage.mapper;

import com.example.multidatasource.voyage.dto.MaritimeQueryDTO;
import com.example.multidatasource.voyage.entity.MaritimeInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 海事信息Mapper
 */
@Mapper
public interface MaritimeInfoMapper {
    
    /**
     * 分页查询海事信息
     */
    List<MaritimeInfo> selectPage(@Param("query") MaritimeQueryDTO query, 
                                  @Param("offset") int offset, 
                                  @Param("limit") int limit);
    
    /**
     * 统计查询结果总数
     */
    int countByQuery(@Param("query") MaritimeQueryDTO query);
    
    /**
     * 根据文章ID查询
     */
    MaritimeInfo selectByArticleId(@Param("articleId") String articleId);
    
    /**
     * 根据ID查询
     */
    MaritimeInfo selectById(@Param("id") Long id);
    
    /**
     * 插入海事信息
     */
    int insert(MaritimeInfo maritimeInfo);
    
    /**
     * 更新海事信息
     */
    int updateById(MaritimeInfo maritimeInfo);
    
    /**
     * 根据文章ID更新
     */
    int updateByArticleId(MaritimeInfo maritimeInfo);
    
    /**
     * 删除海事信息
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 批量插入海事信息
     */
    int batchInsert(@Param("list") List<MaritimeInfo> list);
}
