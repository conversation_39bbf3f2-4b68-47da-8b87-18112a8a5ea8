# 模块化架构设计

## 🏗️ 项目结构

### 重构后的模块化结构
```
src/main/java/com/example/multidatasource/
├── crew/                           # 船管模块
│   ├── entity/
│   │   └── CrewInfo.java          # 船员实体
│   ├── mapper/
│   │   └── CrewMapper.java        # 船员Mapper
│   ├── service/
│   │   ├── CrewService.java       # 船员服务接口
│   │   └── impl/
│   │       └── CrewServiceImpl.java # 船员服务实现
│   └── controller/
│       └── CrewController.java    # 船员控制器
├── voyage/                         # 航次模块
│   ├── entity/
│   │   └── VoyageInfo.java
│   ├── mapper/
│   │   └── VoyageMapper.java
│   ├── service/
│   │   ├── VoyageService.java
│   │   └── impl/
│   │       └── VoyageServiceImpl.java
│   └── controller/
│       └── VoyageController.java
├── cargo/                          # 货物模块
│   └── ...
├── finance/                        # 财务模块
│   └── ...
└── common/                         # 公共模块
    ├── annotation/                 # 公共注解
    │   └── DataSource.java
    ├── aspect/                     # AOP切面
    │   └── DataSourceAspect.java
    ├── config/                     # 配置类
    │   ├── DataSourceContextHolder.java
    │   ├── DynamicDataSource.java
    │   └── MyBatisDataSourceConfig.java
    ├── dto/                        # 数据传输对象
    │   ├── PageResult.java
    │   ├── CrewQueryDTO.java
    │   ├── VoyageQueryDTO.java
    │   └── CargoQueryDTO.java
    └── exception/                  # 异常处理
        └── GlobalExceptionHandler.java

src/main/resources/
├── mapper/                         # MyBatis映射文件
│   ├── crew/
│   │   └── CrewMapper.xml
│   ├── voyage/
│   │   └── VoyageMapper.xml
│   ├── cargo/
│   │   └── CargoMapper.xml
│   └── finance/
│       └── FinanceMapper.xml
├── application.yml                 # 主配置文件
├── application-dev.yml             # 开发环境配置
├── application-test.yml            # 测试环境配置
├── application-prod.yml            # 生产环境配置
└── application-local.yml           # 本地环境配置
```

## 🎯 模块化设计原则

### 1. 业务模块独立
- **crew**: 船员管理相关功能
- **voyage**: 航次管理相关功能
- **cargo**: 货物管理相关功能
- **finance**: 财务管理相关功能

### 2. 公共组件共享
- **common**: 所有模块共享的组件
- 避免代码重复
- 统一技术标准

### 3. 数据源隔离
- 每个模块对应独立的数据库
- 通过@DataSource注解自动切换
- 支持跨模块数据访问

## 📋 模块职责划分

### 业务模块职责
| 模块 | 数据库 | 主要功能 | API前缀 |
|------|--------|----------|---------|
| crew | crew_management | 船员信息管理 | /api/crew/* |
| voyage | voyage_management | 航次计划管理 | /api/voyage/* |
| cargo | cargo_management | 货物信息管理 | /api/cargo/* |
| finance | finance_management | 财务数据管理 | /api/finance/* |

### 公共模块职责
| 包 | 职责 | 主要类 |
|----|------|--------|
| annotation | 自定义注解 | @DataSource |
| aspect | AOP切面 | DataSourceAspect |
| config | 配置管理 | 数据源配置、MyBatis配置 |
| dto | 数据传输 | 查询DTO、响应DTO |
| exception | 异常处理 | 全局异常处理器 |

## 🔧 模块间通信

### 1. 同模块内通信
```java
// 在crew模块内
@Service
public class CrewServiceImpl implements CrewService {
    @Autowired
    private CrewMapper crewMapper; // 直接注入同模块的Mapper
}
```

### 2. 跨模块通信
```java
// 在crew模块中访问voyage模块数据
@Service
public class CrewServiceImpl implements CrewService {
    @Autowired
    private VoyageService voyageService; // 注入其他模块的Service
    
    @DataSource(DataSourceContextHolder.DataSourceType.VOYAGE)
    public List<VoyageInfo> getCrewVoyages(Long crewId) {
        // 切换到voyage数据源查询
        return voyageService.getVoyagesByCrewId(crewId);
    }
}
```

## 🚀 模块扩展指南

### 1. 添加新模块
1. 创建模块目录结构
2. 添加数据源配置
3. 实现Entity、Mapper、Service、Controller
4. 更新代码生成器配置

### 2. 模块内扩展
1. 添加新的Entity和Mapper
2. 扩展Service接口和实现
3. 添加新的Controller接口
4. 更新相关的DTO

### 3. 公共组件扩展
1. 添加新的注解或配置
2. 扩展AOP切面功能
3. 添加新的DTO或异常类
4. 更新全局配置

## 💡 最佳实践

### 1. 命名规范
- **包名**: 使用业务模块名（crew、voyage等）
- **类名**: 使用模块前缀（CrewInfo、VoyageInfo等）
- **接口**: 使用统一的URL前缀（/crew/*、/voyage/*等）

### 2. 依赖管理
- **模块内**: 可以直接依赖同模块的组件
- **跨模块**: 只能依赖其他模块的Service接口
- **公共组件**: 所有模块都可以依赖

### 3. 数据源管理
- **Service层**: 使用@DataSource注解指定数据源
- **自动切换**: AOP自动处理数据源切换
- **事务管理**: 注意跨数据源事务的处理

### 4. 代码生成
- **使用生成器**: 利用MultiDataSourceGenerator生成基础代码
- **模块化配置**: 在DataSourceConfig中配置新模块
- **后续调整**: 根据业务需求调整生成的代码

## ⚠️ 注意事项

1. **循环依赖**: 避免模块间的循环依赖
2. **事务边界**: 跨数据源操作需要特别注意事务管理
3. **性能考虑**: 频繁的数据源切换可能影响性能
4. **测试策略**: 每个模块需要独立的测试策略
5. **部署考虑**: 模块化后的部署和监控策略
