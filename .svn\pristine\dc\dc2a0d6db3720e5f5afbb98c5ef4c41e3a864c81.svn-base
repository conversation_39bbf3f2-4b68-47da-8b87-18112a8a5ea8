package com.example.multidatasource.crew.service;

import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.crew.entity.CrewInfo;

import java.util.List;
import java.util.Map;

/**
 * 船员管理服务接口
 */
public interface SeafarerScheduleService {

    /**
     * 多条件查询船员（分页）
     */
    PageResult<CrewInfo> queryCrewsPage(Integer page, Integer size, String seafarerId, String seafarerName, String applyDutyId);

    /**
     * 查询船员证书到期列表
     * @param seafarerId 船员ID
     * @return 证书信息列表
     */
    List<Map<String, Object>> getSeafarerCertificateInfo(String seafarerId);

    /**
     * 查询船员服务资历
     * @param seafarerId 船员ID
     * @param applyDutyId 申请职务ID（可选）
     * @return 服务资历列表
     */
    List<Map<String, Object>> getSeafarerQualificationInfo(String seafarerId, String applyDutyId);

}
