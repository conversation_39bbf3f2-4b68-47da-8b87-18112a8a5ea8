package com.example.multidatasource.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 货物查询参数DTO
 */
@Data
@Schema(description = "货物查询参数")
public class CargoQueryDTO {
    
    @Schema(description = "货物名称（支持模糊查询）", example = "集装箱")
    private String cargoName;
    
    @Schema(description = "货物类型", example = "普通货物")
    private String cargoType;
    
    @Schema(description = "起始地（支持模糊查询）", example = "上海")
    private String origin;
    
    @Schema(description = "目的地（支持模糊查询）", example = "洛杉矶")
    private String destination;
    
    @Schema(description = "状态", example = "pending")
    private String status;
    
    @Schema(description = "最小重量", example = "10.0")
    private BigDecimal minWeight;
    
    @Schema(description = "最大重量", example = "100.0")
    private BigDecimal maxWeight;
    
    @Schema(description = "最小体积", example = "20.0")
    private BigDecimal minVolume;
    
    @Schema(description = "最大体积", example = "200.0")
    private BigDecimal maxVolume;
    
    @Schema(description = "页码", example = "1")
    private Integer page = 1;
    
    @Schema(description = "每页大小", example = "10")
    private Integer size = 10;
    
    /**
     * 获取偏移量
     */
    public Integer getOffset() {
        return (page - 1) * size;
    }
    
    /**
     * 获取限制数量
     */
    public Integer getLimit() {
        return size;
    }
}
