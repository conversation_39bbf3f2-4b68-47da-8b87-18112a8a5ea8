<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.multidatasource.voyage.mapper.VoyageMapper">

    <!-- 结果映射 -->
    <resultMap id="VoyageInfoResultMap" type="com.example.multidatasource.voyage.entity.VoyageInfo">
        <id column="id" property="id"/>
        <result column="voyage_no" property="voyageNo"/>
        <result column="ship_name" property="shipName"/>
        <result column="departure_port" property="departurePort"/>
        <result column="arrival_port" property="arrivalPort"/>
        <result column="departure_time" property="departureTime"/>
        <result column="estimated_arrival_time" property="estimatedArrivalTime"/>
        <result column="actual_arrival_time" property="actualArrivalTime"/>
        <result column="status" property="status"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <!-- 货主关联信息统计结果映射 -->
    <resultMap id="CargoCompanyStatsResultMap" type="com.example.multidatasource.voyage.entity.CargoCompanyStats">
        <result column="cargo_company_id" property="cargoCompanyId"/>
        <result column="cargo_company_name" property="cargoCompanyName"/>
        <result column="cargo_names" property="cargoNames"/>
        <result column="ship_names" property="shipNames"/>
        <result column="load_unit_amount" property="loadUnitAmount"/>
        <result column="unload_unit_amount" property="unloadUnitAmount"/>
        <result column="goods_loss_amount" property="goodsLossAmount"/>
        <result column="goods_loss_rate" property="goodsLossRate"/>
        <result column="total_anchor_time" property="totalAnchorTime"/>
        <result column="voyage_count" property="voyageCount"/>
        <result column="intro" property="intro"/>
        <result column="average_mooring_time" property="averageMooringTime"/>
        <result column="shipped_goods_ids" property="shippedGoodsIds"/>
    </resultMap>

    <!-- 查询所有航次信息（分页） -->
    <select id="selectVoyageList" resultMap="VoyageInfoResultMap">
        SELECT * FROM voyage_info 
        ORDER BY departure_time DESC 
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据ID查询航次信息 -->
    <select id="selectVoyageById" resultMap="VoyageInfoResultMap">
        SELECT * FROM voyage_info WHERE id = #{id}
    </select>

    <!-- 插入航次信息 -->
    <insert id="insertVoyage" parameterType="com.example.multidatasource.voyage.entity.VoyageInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO voyage_info (voyage_no, ship_name, departure_port, arrival_port, departure_time, estimated_arrival_time, status, created_time, updated_time)
        VALUES (#{voyageNo}, #{shipName}, #{departurePort}, #{arrivalPort}, #{departureTime}, #{estimatedArrivalTime}, #{status}, NOW(), NOW())
    </insert>

    <!-- 更新航次信息 -->
    <update id="updateVoyage" parameterType="com.example.multidatasource.voyage.entity.VoyageInfo">
        UPDATE voyage_info 
        SET voyage_no = #{voyageNo}, 
            ship_name = #{shipName}, 
            departure_port = #{departurePort}, 
            arrival_port = #{arrivalPort}, 
            departure_time = #{departureTime}, 
            estimated_arrival_time = #{estimatedArrivalTime}, 
            actual_arrival_time = #{actualArrivalTime},
            status = #{status}, 
            updated_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 删除航次信息 -->
    <delete id="deleteVoyageById">
        DELETE FROM voyage_info WHERE id = #{id}
    </delete>

    <!-- 根据状态查询航次列表 -->
    <select id="selectVoyageByStatus" resultMap="VoyageInfoResultMap">
        SELECT * FROM voyage_info WHERE status = #{status} ORDER BY departure_time DESC
    </select>

    <!-- 根据船舶名称查询航次列表 -->
    <select id="selectVoyageByShipName" resultMap="VoyageInfoResultMap">
        SELECT * FROM voyage_info WHERE ship_name LIKE CONCAT('%', #{shipName}, '%') ORDER BY departure_time DESC
    </select>

    <!-- 更新航次状态 -->
    <update id="updateVoyageStatus">
        UPDATE voyage_info SET status = #{status}, updated_time = NOW() WHERE id = #{id}
    </update>

    <!-- 统计航次总数 -->
    <select id="countVoyage" resultType="int">
        SELECT COUNT(*) FROM voyage_info
    </select>

    <!-- 根据多个条件查询航次 -->
    <select id="selectVoyageByConditions" resultMap="VoyageInfoResultMap">
        SELECT * FROM voyage_info 
        <where>
            <if test="voyageNo != null and voyageNo != ''">
                AND voyage_no LIKE CONCAT('%', #{voyageNo}, '%')
            </if>
            <if test="shipName != null and shipName != ''">
                AND ship_name LIKE CONCAT('%', #{shipName}, '%')
            </if>
            <if test="departurePort != null and departurePort != ''">
                AND departure_port LIKE CONCAT('%', #{departurePort}, '%')
            </if>
            <if test="arrivalPort != null and arrivalPort != ''">
                AND arrival_port LIKE CONCAT('%', #{arrivalPort}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="startDate != null">
                AND departure_time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND departure_time &lt;= #{endDate}
            </if>
        </where>
        ORDER BY departure_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 统计符合条件的航次总数 -->
    <select id="countVoyageByConditions" resultType="int">
        SELECT COUNT(*) FROM voyage_info 
        <where>
            <if test="voyageNo != null and voyageNo != ''">
                AND voyage_no LIKE CONCAT('%', #{voyageNo}, '%')
            </if>
            <if test="shipName != null and shipName != ''">
                AND ship_name LIKE CONCAT('%', #{shipName}, '%')
            </if>
            <if test="departurePort != null and departurePort != ''">
                AND departure_port LIKE CONCAT('%', #{departurePort}, '%')
            </if>
            <if test="arrivalPort != null and arrivalPort != ''">
                AND arrival_port LIKE CONCAT('%', #{arrivalPort}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="startDate != null">
                AND departure_time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND departure_time &lt;= #{endDate}
            </if>
        </where>
    </select>

    <!-- 获取所有船舶名称列表 -->
    <select id="selectAllShipNames" resultType="string">
        SELECT DISTINCT ship_name FROM voyage_info WHERE ship_name IS NOT NULL ORDER BY ship_name
    </select>

    <!-- 获取所有港口列表 -->
    <select id="selectAllPorts" resultType="string">
        SELECT DISTINCT port_name FROM (
            SELECT departure_port AS port_name FROM voyage_info WHERE departure_port IS NOT NULL
            UNION
            SELECT arrival_port AS port_name FROM voyage_info WHERE arrival_port IS NOT NULL
        ) AS ports ORDER BY port_name
    </select>

    <!-- 执行自定义SQL查询 -->
    <select id="executeCustomQuery" resultType="map">
        ${sql}
    </select>

    <!-- 执行自定义SQL更新 -->
    <update id="executeCustomUpdate">
        ${sql}
    </update>

    <!-- 查询货主关联信息统计（分页） -->
    <select id="selectCargoCompanyStats" resultMap="CargoCompanyStatsResultMap">
        SELECT
            company_stats.cargo_company_id,
            company_stats.cargo_company_name,
            uc.intro,
            GROUP_CONCAT(DISTINCT company_stats.cargo_names SEPARATOR '; ') AS cargo_names,
            GROUP_CONCAT(DISTINCT company_stats.ship_names SEPARATOR '; ') AS ship_names,
            GROUP_CONCAT(DISTINCT company_stats.goods_ids) AS shipped_goods_ids,
            ROUND(SUM(company_stats.load_sum), 2) AS load_unit_amount,
            ROUND(SUM(company_stats.unload_sum), 2) AS unload_unit_amount,
            ROUND(SUM(company_stats.load_sum) - SUM(company_stats.unload_sum), 2) AS goods_loss_amount,
            CASE WHEN SUM(company_stats.load_sum) = 0 THEN 0
                 ELSE ROUND((SUM(company_stats.load_sum) - SUM(company_stats.unload_sum)) / SUM(company_stats.load_sum) * 1000, 4)
            END AS goods_loss_rate,
            ROUND(SUM(IFNULL(line.anchor_time,0)),2) AS total_anchor_time,
            COUNT(DISTINCT company_stats.voyage_ids) AS voyage_count,
            CASE WHEN COUNT(DISTINCT company_stats.voyage_ids) = 0 THEN 0
                 ELSE ROUND(SUM(IFNULL(line.anchor_time,0)) / COUNT(DISTINCT company_stats.voyage_ids), 2)
            END AS average_mooring_time
        FROM (
            SELECT
                a.cargo_company_id,
                b.name AS cargo_company_name,
                a.stat_line_id,
                GROUP_CONCAT(DISTINCT a.voyage_id) AS voyage_ids,
                GROUP_CONCAT(DISTINCT e.cargo_name ORDER BY e.cargo_name SEPARATOR '; ') AS cargo_names,
                GROUP_CONCAT(DISTINCT f.ship_name_cn ORDER BY f.ship_name_cn SEPARATOR '; ') AS ship_names,
                GROUP_CONCAT(DISTINCT a.goods_id) as goods_ids,
                SUM(CASE WHEN d.port_type = 1 THEN a.unit_amount END) AS load_sum,
                SUM(CASE WHEN d.port_type = 2 THEN a.unit_amount END) AS unload_sum
            FROM
                t_vy_stat_cargo a
                LEFT JOIN t_us_company b ON b.id = a.cargo_company_id
                INNER JOIN t_vy_stat_voyage c ON a.voyage_id = c.voyage_id AND c.is_available = 0
                INNER JOIN t_vy_stat_line d ON a.stat_line_id = d.id AND d.is_available = 0
                INNER JOIN t_cg_cargo e ON a.goods_id = e.id
                JOIN t_sh_ship f ON c.ship_id = f.id
            WHERE
                a.is_available = 0
                AND a.cargo_company_id IS NOT NULL
                <choose>
                    <when test="startDate != null and endDate != null">
                        AND DATE(c.voyage_over_date) BETWEEN #{startDate} AND #{endDate}
                    </when>
                    <when test="startDate != null">
                        AND DATE(c.voyage_over_date) >= #{startDate}
                    </when>
                    <when test="endDate != null">
                        AND DATE(c.voyage_over_date) &lt;= #{endDate}
                    </when>
                    <otherwise>
                        AND YEAR(c.voyage_over_date) = YEAR(CURDATE())
                    </otherwise>
                </choose>
                <if test="cargoCompanyName != null and cargoCompanyName != ''">
                    AND b.name LIKE CONCAT('%', #{cargoCompanyName}, '%')
                </if>
            GROUP BY
                a.cargo_company_id, b.name, a.stat_line_id
        ) company_stats
        INNER JOIN t_vy_stat_line line ON company_stats.stat_line_id = line.id AND line.is_available = 0
        LEFT JOIN t_us_company uc ON uc.id = company_stats.cargo_company_id
        GROUP BY
            company_stats.cargo_company_id,
            company_stats.cargo_company_name,
            uc.intro
        ORDER BY
            <choose>
                <when test="sortBy == 'goodsLossAmount'">
                    goods_loss_amount DESC
                </when>
                <when test="sortBy == 'averageMooringTime'">
                    average_mooring_time DESC
                </when>
                <otherwise>
                    load_unit_amount DESC
                </otherwise>
            </choose>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 统计货主关联信息总数 -->
    <select id="countCargoCompanyStats" resultType="int">
        SELECT COUNT(DISTINCT company_stats.cargo_company_id)
        FROM (
            SELECT
                a.cargo_company_id,
                b.name AS cargo_company_name,
                a.stat_line_id
            FROM
                t_vy_stat_cargo a
                LEFT JOIN t_us_company b ON b.id = a.cargo_company_id
                INNER JOIN t_vy_stat_voyage c ON a.voyage_id = c.voyage_id AND c.is_available = 0
                INNER JOIN t_vy_stat_line d ON a.stat_line_id = d.id AND d.is_available = 0
                INNER JOIN t_cg_cargo e ON a.goods_id = e.id
                JOIN t_sh_ship f ON c.ship_id = f.id
            WHERE
                a.is_available = 0
                AND a.cargo_company_id IS NOT NULL
                <choose>
                    <when test="startDate != null and endDate != null">
                        AND DATE(c.voyage_over_date) BETWEEN #{startDate} AND #{endDate}
                    </when>
                    <when test="startDate != null">
                        AND DATE(c.voyage_over_date) >= #{startDate}
                    </when>
                    <when test="endDate != null">
                        AND DATE(c.voyage_over_date) &lt;= #{endDate}
                    </when>
                    <otherwise>
                        AND YEAR(c.voyage_over_date) = YEAR(CURDATE())
                    </otherwise>
                </choose>
                <if test="cargoCompanyName != null and cargoCompanyName != ''">
                    AND b.name LIKE CONCAT('%', #{cargoCompanyName}, '%')
                </if>
            GROUP BY
                a.cargo_company_id, b.name, a.stat_line_id
        ) company_stats
        INNER JOIN t_vy_stat_line line ON company_stats.stat_line_id = line.id AND line.is_available = 0
    </select>

    <!-- 通过船舶名称查询船舶详细信息 -->
    <select id="getVesselDetailInfo" resultType="java.util.Map">
        SELECT
            id AS vesselId,
            ship_name_cn AS shipName,
            navigation_area AS navigationArea,
            gross_tonage AS grossTonage,
            main_engine_power_kw AS mainEnginePowerKw
        FROM t_sh_ship
        WHERE ship_name_cn = #{vesselName}
        LIMIT 1
    </select>

    <!-- 查询所有货品信息 -->
    <select id="selectAllCargo" resultType="map">
        SELECT id, cargo_name AS cargoName FROM t_cg_cargo ORDER BY cargo_name
    </select>

</mapper>
