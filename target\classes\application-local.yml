# 本地开发环境配置（个人开发使用）
server:
  port: 54321

# 多MySQL数据源配置 - 本地环境（使用Docker或本地MySQL）
datasources:
  # 船管系统数据库 - 本地环境
  crew:
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: ************************************************************************************************************************************************************
    username: root
    password: winsea@2020
    type: mysql

  # 航次动态管理数据库 - 开发环境
  voyage:
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: jdbc:mysql://************:3306/hzx_master?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: web
    password: web
    type: mysql
    
  # 货物管理数据库 - 本地环境
  cargo:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************
    username: root
    password: root
    type: mysql
    
  # 财务管理数据库 - 本地环境
  finance:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************************************************
    username: root
    password: root
    type: mysql

# HikariCP连接池配置 - 本地环境（最小配置）
hikari:
  maximum-pool-size: 5
  minimum-idle: 1
  connection-timeout: 30000
  idle-timeout: 300000
  max-lifetime: 900000

# 日志配置 - 本地环境（最详细的日志）
logging:
  level:
    root: INFO
    com.example.multidatasource: DEBUG
    org.springframework.jdbc: DEBUG
    org.apache.ibatis: DEBUG
    org.mybatis: DEBUG
    org.springframework.web: DEBUG
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss}){faint} %clr([%thread]){magenta} %clr(%-5level){highlight} %clr(%logger{36}){cyan} - %msg%n"

# 本地开发环境监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"  # 本地环境暴露所有端点
  endpoint:
    health:
      show-details: always
    env:
      show-values: always

# 开发环境数据库初始化
spring:
  sql:
    init:
      mode: always  # 开发环境总是执行初始化脚本
      continue-on-error: true

# swagger地址信息
swagger:
  server:
    url: http://localhost:54321/multi/source/api