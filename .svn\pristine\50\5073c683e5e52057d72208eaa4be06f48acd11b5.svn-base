package com.example.multidatasource.crew.service.impl;

import com.example.multidatasource.common.config.DataSourceContextHolder;
import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.crew.dto.SeafarerBaseInfoDTO;
import com.example.multidatasource.crew.mapper.SeafarerBaseInfoMapper;
import com.example.multidatasource.crew.service.SeafarerBaseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 船员基础信息服务实现类
 */
@Slf4j
@Service
@DataSource(DataSourceContextHolder.DataSourceType.CREW)
public class SeafarerBaseInfoServiceImpl implements SeafarerBaseInfoService {

    @Autowired
    private SeafarerBaseInfoMapper seafarerBaseInfoMapper;

    @Override
    public SeafarerBaseInfoDTO querySeafarerBaseInfo() {
        log.info("查询船员基础信息（船舶列表、职务列表、证书等级）");
        try {
            SeafarerBaseInfoDTO result = new SeafarerBaseInfoDTO();
            
            // 查询船舶列表
            List<Map<String, Object>> vesselList = seafarerBaseInfoMapper.getVesselList();
            log.info("查询到{}条船舶记录", vesselList.size());
            
            // 查询职务列表
            List<Map<String, Object>> crtDutyList = seafarerBaseInfoMapper.getCrtDutyList();
            log.info("查询到{}条职务记录", crtDutyList.size());
            
            // 查询证书等级列表
            List<Map<String, Object>> crtLevelList = seafarerBaseInfoMapper.getCrtLevelList();
            log.info("查询到{}条证书等级记录", crtLevelList.size());
            
            result.setVesselList(vesselList);
            result.setCrtDutyList(crtDutyList);
            result.setCrtLevelList(crtLevelList);
            
            log.info("成功查询船员基础信息");
            return result;
        } catch (Exception e) {
            log.error("查询船员基础信息失败", e);
            throw new RuntimeException("查询船员基础信息失败: " + e.getMessage());
        }
    }
}
