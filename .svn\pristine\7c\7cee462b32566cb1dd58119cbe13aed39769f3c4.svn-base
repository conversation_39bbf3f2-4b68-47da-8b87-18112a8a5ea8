package com.example.multidatasource.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 航次查询参数DTO
 */
@Data
@Schema(description = "航次查询参数")
public class VoyageQueryDTO {
    
    @Schema(description = "航次号（支持模糊查询）", example = "V2024001")
    private String voyageNo;
    
    @Schema(description = "船舶名称（支持模糊查询）", example = "远洋号")
    private String shipName;
    
    @Schema(description = "出发港口（支持模糊查询）", example = "上海港")
    private String departurePort;
    
    @Schema(description = "到达港口（支持模糊查询）", example = "洛杉矶港")
    private String arrivalPort;
    
    @Schema(description = "状态", example = "planned")
    private String status;
    
    @Schema(description = "开始日期", example = "2024-01-01T00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDate;
    
    @Schema(description = "结束日期", example = "2024-12-31T23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDate;
    
    @Schema(description = "页码", example = "1")
    private Integer page = 1;
    
    @Schema(description = "每页大小", example = "10")
    private Integer size = 10;
    
    /**
     * 获取偏移量
     */
    public Integer getOffset() {
        return (page - 1) * size;
    }
    
    /**
     * 获取限制数量
     */
    public Integer getLimit() {
        return size;
    }
}
