package com.example.multidatasource.controller;

import com.example.multidatasource.config.DynamicDataSourceManager;
import com.example.multidatasource.model.DataSourceConfig;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 数据源管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/datasource")
@Tag(name = "数据源管理", description = "数据源配置和管理API")
public class DataSourceController {
    
    @Autowired
    private DynamicDataSourceManager dataSourceManager;
    
    @GetMapping("/list")
    @Operation(summary = "获取所有数据源", description = "获取系统中配置的所有数据源信息")
    public ResponseEntity<Map<String, DataSourceConfig>> getAllDataSources() {
        Map<String, DataSourceConfig> dataSources = dataSourceManager.getAllDataSourceConfigs();
        return ResponseEntity.ok(dataSources);
    }
    
    @PostMapping("/test/{dataSourceName}")
    @Operation(summary = "测试数据源连接", description = "测试指定数据源的连接是否正常")
    public ResponseEntity<String> testDataSource(@PathVariable String dataSourceName) {
        try {
            dataSourceManager.getDataSource(dataSourceName);
            return ResponseEntity.ok("数据源连接测试成功: " + dataSourceName);
        } catch (Exception e) {
            log.error("数据源连接测试失败: {}", dataSourceName, e);
            return ResponseEntity.badRequest().body("数据源连接测试失败: " + e.getMessage());
        }
    }
    
    @DeleteMapping("/cache/{dataSourceName}")
    @Operation(summary = "清除数据源缓存", description = "清除指定数据源的连接池缓存")
    public ResponseEntity<String> clearDataSourceCache(@PathVariable String dataSourceName) {
        try {
            dataSourceManager.removeDataSource(dataSourceName);
            return ResponseEntity.ok("数据源缓存清除成功: " + dataSourceName);
        } catch (Exception e) {
            log.error("数据源缓存清除失败: {}", dataSourceName, e);
            return ResponseEntity.badRequest().body("数据源缓存清除失败: " + e.getMessage());
        }
    }
}
