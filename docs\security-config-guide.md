# Spring Security配置指南

## 🔧 配置无需认证的接口

### 方法1：在SecurityConfig中配置（推荐）

在 `src/main/java/com/example/multidatasource/config/SecurityConfig.java` 中的 `filterChain` 方法里添加：

```java
.authorizeRequests()
    .antMatchers(
        // 添加您的公开接口
        "/your-public-api/**",
        "/another-public-api/**"
    ).permitAll()
```

### 方法2：在JWT过滤器中配置

在 `src/main/java/com/example/multidatasource/config/JwtAuthenticationFilter.java` 中的 `isPublicPath` 方法里添加：

```java
private boolean isPublicPath(String path) {
    String[] publicPaths = {
        // 添加您的公开路径
        "/your-public-api",
        "/another-public-api"
    };
    // ...
}
```

## 📋 当前已配置的公开接口

### 认证相关接口
- `/auth/login` - 用户登录
- `/auth/register` - 用户注册
- `/auth/validate` - 令牌验证

### 系统监控接口
- `/health/**` - 健康检查
- `/actuator/**` - Spring Boot Actuator

### Swagger文档接口
- `/swagger-ui/**` - Swagger UI
- `/swagger-ui.html` - Swagger首页
- `/v3/api-docs/**` - OpenAPI文档
- `/swagger-resources/**` - Swagger资源
- `/webjars/**` - 前端资源

### 自定义公开接口
- `/public/**` - 通用公开接口
- `/crew/public/**` - 船管模块公开接口
- `/voyage/public/**` - 航次模块公开接口

## 🎯 添加新的公开接口示例

### 1. 添加整个模块的公开接口
```java
.antMatchers("/newmodule/public/**").permitAll()
```

### 2. 添加特定接口
```java
.antMatchers(
    "/crew/statistics",      // 船员统计（无需认证）
    "/voyage/schedule",      // 航次时刻表（无需认证）
    "/system/version"        // 系统版本（无需认证）
).permitAll()
```

### 3. 添加HTTP方法限制
```java
.antMatchers(HttpMethod.GET, "/crew/list").permitAll()  // 只允许GET请求
.antMatchers(HttpMethod.POST, "/feedback").permitAll()  // 只允许POST请求
```

## ⚠️ Spring Boot版本兼容性

### Spring Boot 2.7.x（当前版本）
使用 `.authorizeRequests()` 和 `.antMatchers()`：
```java
.authorizeRequests()
    .antMatchers("/public/**").permitAll()
    .anyRequest().authenticated()
```

### Spring Boot 3.x
使用 `.authorizeHttpRequests()` 和 `.requestMatchers()`：
```java
.authorizeHttpRequests(authz -> authz
    .requestMatchers("/public/**").permitAll()
    .anyRequest().authenticated()
)
```

## 🔒 安全注意事项

### 1. 谨慎配置公开接口
- 只将真正需要公开的接口设为无需认证
- 避免暴露敏感数据的接口
- 定期审查公开接口列表

### 2. 接口路径匹配规则
- `/**` 匹配所有子路径
- `/*` 只匹配一级子路径
- 精确匹配优先于通配符匹配

### 3. 测试公开接口
```bash
# 测试无需认证的接口
curl -X GET http://localhost:8080/api/public/info

# 测试需要认证的接口（应该返回401）
curl -X GET http://localhost:8080/api/crew/crew-list
```

## 🛠️ 常见问题解决

### 问题1：requestMatchers方法不存在
**原因**：Spring Security版本不匹配
**解决**：使用 `antMatchers` 替代 `requestMatchers`

### 问题2：接口仍然需要认证
**检查**：
1. 路径是否正确匹配
2. 是否在两个地方都配置了（SecurityConfig和JwtFilter）
3. 是否有其他安全配置覆盖

### 问题3：编译错误
**检查**：
1. Spring Boot版本
2. 导入的包是否正确
3. 方法签名是否匹配版本

## 📝 配置模板

### 基础模板
```java
@Bean
public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
    http
        .csrf().disable()
        .cors().configurationSource(corsConfigurationSource())
        .and()
        .sessionManagement()
        .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
        .and()
        .authorizeRequests()
            // 公开接口
            .antMatchers(
                "/auth/login",
                "/auth/register",
                "/public/**",
                "/your-public-api/**"  // 添加您的公开接口
            ).permitAll()
            // 需要认证的接口
            .anyRequest().authenticated()
        .and()
        .addFilterBefore(jwtAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
    
    return http.build();
}
```

### 高级配置模板
```java
.authorizeRequests()
    // 完全公开的接口
    .antMatchers("/public/**").permitAll()
    
    // 只允许GET请求的公开接口
    .antMatchers(HttpMethod.GET, "/crew/statistics").permitAll()
    
    // 需要特定角色的接口
    .antMatchers("/admin/**").hasRole("ADMIN")
    
    // 需要认证但不限制角色的接口
    .antMatchers("/user/**").authenticated()
    
    // 其他所有请求都需要认证
    .anyRequest().authenticated()
```
