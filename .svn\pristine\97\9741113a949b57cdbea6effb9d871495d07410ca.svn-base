package com.example.multidatasource.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户查询参数DTO
 */
@Data
@Schema(description = "用户查询参数")
@JsonInclude(JsonInclude.Include.ALWAYS)
public class UserQueryDTO {
    
    @Schema(description = "用户名（支持模糊查询）", example = "admin")
    private String username;
    
    @Schema(description = "邮箱（支持模糊查询）", example = "<EMAIL>")
    private String email;
    
    @Schema(description = "真实姓名（支持模糊查询）", example = "张三")
    private String realName;
    
    @Schema(description = "角色", example = "admin")
    private String role;
    
    @Schema(description = "状态", example = "active")
    private String status;
    
    @Schema(description = "页码", example = "1")
    private Integer page = 1;
    
    @Schema(description = "每页大小", example = "10")
    private Integer size = 10;
    
    /**
     * 获取偏移量
     */
    public Integer getOffset() {
        return (page - 1) * size;
    }
    
    /**
     * 获取限制数量
     */
    public Integer getLimit() {
        return size;
    }
}
