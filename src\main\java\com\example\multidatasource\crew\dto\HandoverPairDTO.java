package com.example.multidatasource.crew.dto;

import com.example.multidatasource.crew.dto.SeafarerMatchResultDTO.CandidateSeafarerDTO;
import com.example.multidatasource.crew.dto.SeafarerMatchResultDTO.OnBoardSeafarerDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用于接收前端提交的交接班配对信息
 */
@Data
@Schema(description = "交接班配对信息")
public class HandoverPairDTO {

    @Schema(description = "在船交班人员信息")
    private OnBoardSeafarerDTO onBoardSeafarer;

    @Schema(description = "选中的接班候选人信息")
    private CandidateSeafarerDTO candidateSeafarer;
}

