package com.example.multidatasource.crew.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 封装船员最新考评信息的DTO
 */
@Data
@Schema(description = "船员最新考评信息")
@JsonInclude(JsonInclude.Include.ALWAYS)
public class EvaluationInfoDTO {

    @Schema(description = "最新考评类型 (下船考评 / 年度考评)")
    private String evaluationType;

    @Schema(description = "最新考评日期")
    private LocalDate evaluationDate;

    @Schema(description = "船长评语")
    private String captainComment;

    @Schema(description = "船长姓名")
    private String captainName;

    @Schema(description = "部门长姓名")
    private String departmentHeadName;

    @Schema(description = "部门长职务")
    private String departmentHeadDuty;

    @Schema(description = "部门长意见")
    private String departmentHeadOpinion;

    @Schema(description = "船员部经理姓名")
    private String crewManagerName;

    @Schema(description = "船员部经理职务")
    private String crewManagerDuty;

    @Schema(description = "船员部经理评价")
    private String crewDeptManagerEvaluation;

}

