#!/bin/bash

echo "========================================"
echo "多数据源API文档生成工具"
echo "========================================"

# 检查应用是否运行
echo "正在检查应用状态..."
if ! curl -s http://localhost:54321/multi/source/api/health/status > /dev/null 2>&1; then
    echo "错误: 应用未运行，请先启动应用"
    echo "启动命令: mvn spring-boot:run"
    exit 1
fi

echo "应用运行正常，开始生成文档..."

# 创建文档目录
mkdir -p docs/api

# 1. 下载OpenAPI JSON
echo "1. 生成OpenAPI JSON文档..."
if curl -s http://localhost:54321/multi/source/api/v3/api-docs > docs/api/openapi.json; then
    echo "   ✓ OpenAPI JSON已生成: docs/api/openapi.json"
else
    echo "   ✗ OpenAPI JSON生成失败"
fi

# 2. 生成Postman Collection
echo "2. 生成Postman Collection..."
if curl -s http://localhost:54321/multi/source/api/v3/api-docs > docs/api/postman-collection.json; then
    echo "   ✓ Postman Collection已生成: docs/api/postman-collection.json"
else
    echo "   ✗ Postman Collection生成失败"
fi

# 3. 创建API文档说明
echo "3. 生成API文档说明..."
cat > docs/api/README.md << 'EOF'
# API文档使用指南

## 📋 文档类型

### 1. 在线交互式文档
- **Swagger UI**: http://localhost:54321/multi/source/api/swagger-ui/index.html
- **特点**: 可以直接测试接口，支持JWT认证
- **使用方法**: 
  1. 通过 /auth/login 获取JWT令牌
  2. 点击右上角 Authorize 按钮配置认证
  3. 测试各种API接口

### 2. OpenAPI规范文档
- **JSON格式**: http://localhost:54321/multi/source/api/v3/api-docs
- **本地文件**: docs/api/openapi.json
- **用途**: 前端代码生成、第三方工具集成

### 3. Postman Collection
- **文件位置**: docs/api/postman-collection.json
- **使用方法**: 导入到Postman中进行接口测试

## 🔐 认证说明

### 默认用户账户
- **管理员**: admin / admin123
- **普通用户**: testuser / user123

### JWT令牌使用
1. 调用 POST /auth/login 获取令牌
2. 在请求头中添加: Authorization: Bearer <token>
3. 令牌默认1小时过期

## 📚 API分组说明

- **身份认证** (/auth/*): 用户登录、注册、令牌管理
- **用户管理** (/users/*): 用户信息管理(需要认证)
- **船管系统** (/crew/*): 船员信息管理(需要认证)
- **航次管理** (/voyage/*): 航次计划管理(需要认证)
- **通用SQL** (/sql/*): 动态SQL执行(需要认证)
- **系统监控** (/health/*): 系统状态检查(公开)
- **公开接口** (/public/*): 无需认证的接口

## 🚀 快速开始

1. 启动应用: mvn spring-boot:run
2. 访问Swagger UI: http://localhost:54321/multi/source/api/swagger-ui/index.html
3. 使用默认账户登录获取令牌
4. 配置认证后测试各种接口

生成时间: $(date)
EOF

echo "   ✓ API文档说明已生成: docs/api/README.md"

# 4. 创建前端集成示例
echo "4. 生成前端集成示例..."
cat > docs/api/frontend-example.js << 'EOF'
// API调用示例 - JavaScript

// 1. 用户登录
async function login(username, password) {
  const response = await fetch('/multi/source/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, password, rememberMe: false })
  });
  const result = await response.json();
  if (result.code === 200) {
    localStorage.setItem('token', result.data.accessToken);
    return result.data;
  }
  throw new Error(result.message);
}

// 2. 调用需要认证的API
async function apiCall(url, options = {}) {
  const token = localStorage.getItem('token');
  const headers = {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` }),
    ...options.headers
  };
  
  const response = await fetch(url, { ...options, headers });
  return await response.json();
}

// 3. 使用示例
// 获取船员列表
const crewList = await apiCall('/multi/source/api/crew/crew-list?page=1&size=10');

// 获取航次详情
const voyageDetail = await apiCall('/multi/source/api/voyage/voyage/1');

// 获取用户信息
const userInfo = await apiCall('/multi/source/api/users/1');
EOF

echo "   ✓ 前端集成示例已生成: docs/api/frontend-example.js"

echo ""
echo "========================================"
echo "文档生成完成！"
echo "========================================"
echo ""
echo "📁 生成的文件:"
echo "  - docs/api/openapi.json          (OpenAPI规范文档)"
echo "  - docs/api/postman-collection.json (Postman导入文件)"
echo "  - docs/api/README.md             (API使用说明)"
echo "  - docs/api/frontend-example.js   (前端集成示例)"
echo ""
echo "🌐 在线文档:"
echo "  - Swagger UI: http://localhost:54321/multi/source/api/swagger-ui/index.html"
echo "  - OpenAPI JSON: http://localhost:54321/multi/source/api/v3/api-docs"
echo ""
