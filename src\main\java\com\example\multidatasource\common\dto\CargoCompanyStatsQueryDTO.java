package com.example.multidatasource.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 货主关联信息查询参数DTO
 */
@Data
@Schema(description = "货主关联信息查询参数")
public class CargoCompanyStatsQueryDTO {
    
    @Schema(description = "货主公司名称（支持模糊查询）", example = "中远海运")
    private String cargoCompanyName;
    
    @Schema(description = "开始日期", example = "2024-01-01")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;
    
    @Schema(description = "结束日期", example = "2024-12-31")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;
    
    @Schema(description = "页码", example = "1")
    private Integer page = 1;
    
    @Schema(description = "每页大小", example = "10")
    private Integer size = 10;

    @Schema(description = "排序字段 (驼峰式)，支持：goodsLossAmount, loadUnitAmount, averageMooringTime", defaultValue = "loadUnitAmount")
    private String sortBy = "loadUnitAmount";
    
    /**
     * 获取偏移量
     */
    public Integer getOffset() {
        return (page - 1) * size;
    }
    
    /**
     * 获取限制数量
     */
    public Integer getLimit() {
        return size;
    }
}
