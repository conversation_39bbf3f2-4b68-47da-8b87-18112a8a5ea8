package com.example.multidatasource.crew.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 船员基础信息DTO
 */
@Data
@Schema(description = "船员基础信息")
public class SeafarerBaseInfoDTO {

    @Schema(description = "船舶列表")
    private List<Map<String, Object>> vesselList;

    @Schema(description = "职务列表")
    private List<Map<String, Object>> crtDutyList;

    @Schema(description = "证书等级列表")
    private List<Map<String, Object>> crtLevelList;
}
