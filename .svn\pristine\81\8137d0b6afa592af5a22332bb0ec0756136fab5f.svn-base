package com.example.multidatasource.common.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 数据源上下文持有者
 * 使用ThreadLocal来保存当前线程的数据源标识
 */
public class DataSourceContextHolder {
    
    private static final Logger logger = LoggerFactory.getLogger(DataSourceContextHolder.class);
    
    private static final ThreadLocal<String> CONTEXT_HOLDER = new ThreadLocal<>();
    
    /**
     * 数据源类型枚举
     */
    public enum DataSourceType {
        CREW("crew", "船管系统"),
        VOYAGE("voyage", "航次动态管理"),
        CARGO("cargo", "货物管理"),
        FINANCE("finance", "财务管理");
        
        private final String code;
        private final String description;
        
        DataSourceType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 设置数据源类型
     */
    public static void setDataSourceType(String dataSourceType) {
        logger.debug("Switching to data source: {}", dataSourceType);
        CONTEXT_HOLDER.set(dataSourceType);
    }
    
    /**
     * 设置数据源类型
     */
    public static void setDataSourceType(DataSourceType dataSourceType) {
        setDataSourceType(dataSourceType.getCode());
    }
    
    /**
     * 获取数据源类型
     */
    public static String getDataSourceType() {
        String dataSourceType = CONTEXT_HOLDER.get();
        if (dataSourceType == null) {
            // 默认返回crew数据源
            dataSourceType = DataSourceType.CREW.getCode();
            logger.debug("No data source specified, using default: {}", dataSourceType);
        }
        return dataSourceType;
    }
    
    /**
     * 清除数据源类型
     */
    public static void clearDataSourceType() {
        logger.debug("Clearing data source context");
        CONTEXT_HOLDER.remove();
    }
    
    /**
     * 验证数据源类型是否有效
     */
    public static boolean isValidDataSourceType(String dataSourceType) {
        if (dataSourceType == null) {
            return false;
        }
        
        for (DataSourceType type : DataSourceType.values()) {
            if (type.getCode().equals(dataSourceType)) {
                return true;
            }
        }
        return false;
    }
}
