package com.example.multidatasource.model;

import lombok.Data;

/**
 * 数据源配置模型
 */
@Data
public class DataSourceConfig {
    
    /**
     * 数据源名称
     */
    private String name;
    
    /**
     * 数据库驱动类名
     */
    private String driverClassName;
    
    /**
     * 数据库连接URL
     */
    private String url;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 数据库类型 (mysql, postgresql, oracle, sqlserver)
     */
    private String type;
    
    /**
     * 最大连接池大小
     */
    private Integer maximumPoolSize = 10;
    
    /**
     * 最小空闲连接数
     */
    private Integer minimumIdle = 5;
    
    /**
     * 连接超时时间(毫秒)
     */
    private Long connectionTimeout = 30000L;
    
    /**
     * 空闲超时时间(毫秒)
     */
    private Long idleTimeout = 600000L;
    
    /**
     * 连接最大生命周期(毫秒)
     */
    private Long maxLifetime = 1800000L;
}
