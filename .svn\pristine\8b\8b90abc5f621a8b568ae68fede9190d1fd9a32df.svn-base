# 船员经验统计功能说明

## 功能概述

在船员匹配接口`/match-single-shift-change`和`/match-all-candidates-v2`中，为候选人和在船船员添加了经验统计字段，用于参考展示。

## 新增字段

### 一、候选人个人统计字段（3个）

#### 1. 入司时间（年）
- **字段名**: `companyYears`
- **数据来源**: `crew_seafarer_info.enter_company_date`
- **计算公式**: `ROUND(DATEDIFF(CURDATE(), enter_company_date) / 360.0, 2)`
- **说明**: 从入司日期到当前日期的年数（每月30天，保留2位小数）

#### 2. 任该职务年限（年）
- **字段名**: `dutyYears`
- **数据来源**: `crew_seafarer_service_qualification_info`表
- **计算公式**: `ROUND(相同duty_id的所有服务记录的在船天数总和 / 360.0, 2)`
- **说明**: 船员在该职务上的总服务年数

#### 3. 任该类型船舶时间（年）
- **字段名**: `vesselTypeYears`
- **数据来源**: `crew_seafarer_service_qualification_info` + `common_vessel`表
- **计算公式**: `ROUND(相同vessel_type_flag的所有服务记录的在船天数总和 / 360.0, 2)`
- **说明**: 船员在该类型船舶上的总服务年数

### 二、职务组合统计字段（3个）

针对4个职务组合，计算在船船员+候选船员的合计经验：

#### 职务组合定义
1. **老轨&大管轮组合**: 轮机长 + 大管轮
2. **船长&大副组合**: 船长 + 大副  
3. **二管轮&三管轮组合**: 二管轮 + 三管轮
4. **二副&三副组合**: 二副 + 三副

#### 统计字段
1. **职务组合入司时间合计（年）**: `groupCompanyYears`
2. **职务组合任该职务年限合计（年）**: `groupDutyYears`
3. **职务组合任该类型船舶时间合计（年）**: `groupVesselTypeYears`

#### 计算逻辑
- **候选人经验**: 候选人在该职务上的历史总经验
- **搭档经验**: 在船搭档在其职务上的历史总经验
- **组合统计** = 候选人历史经验 + 搭档历史经验
- **时间单位**: 年（每月30天，保留2位小数）

### 三、搭档信息字段（2个）

#### 1. 搭档职务名称
- **字段名**: `partnerDutyName`
- **数据来源**: 职务搭档关系映射
- **说明**: 与候选人形成组合的搭档职务名称

#### 2. 搭档船员姓名
- **字段名**: `partnerSeafarerName`
- **数据来源**: `crew_seafarer_info`表中在船搭档信息
- **格式**: "姓名1, 姓名2（人数）" 或 状态描述
- **说明**: 当前在船的搭档船员姓名和人数，便于溯源和判断

## 技术实现

### 实现方案
采用**混合方案**：
- **个人统计**: 在SQL查询中通过LEFT JOIN子查询实现
- **职务组合统计**: 在应用层通过搭档查询实现

### 优势
- **性能优化**: 个人统计在SQL层完成，避免N+1查询
- **逻辑清晰**: 搭档关系在应用层处理，便于维护
- **结构简洁**: 避免复杂的SQL嵌套查询

### 核心SQL逻辑

#### 候选人个人统计（getAvailableCrews）
```sql
-- 个人统计字段（转换为年，保留2位小数）
ROUND(IFNULL(DATEDIFF(CURDATE(), a.enter_company_date), 0) / 360.0, 2) AS companyYears,
ROUND(IFNULL(duty_stats.dutyDays, 0) / 360.0, 2) AS dutyYears,
ROUND(IFNULL(vessel_type_stats.vesselTypeDays, 0) / 360.0, 2) AS vesselTypeYears,

-- 职务组合统计字段（转换为年，保留2位小数）
ROUND((IFNULL(DATEDIFF(CURDATE(), a.enter_company_date), 0) + IFNULL(partner_stats.partnerCompanyDays, 0)) / 360.0, 2) AS groupCompanyYears,
ROUND((IFNULL(duty_stats.dutyDays, 0) + IFNULL(partner_stats.partnerDutyDays, 0)) / 360.0, 2) AS groupDutyYears,
ROUND((IFNULL(vessel_type_stats.vesselTypeDays, 0) + IFNULL(partner_stats.partnerVesselTypeDays, 0)) / 360.0, 2) AS groupVesselTypeYears

-- 任该职务年限统计
LEFT JOIN (
    SELECT
        seafarer_id,
        SUM(DATEDIFF(IFNULL(down_board_date, CURDATE()), on_board_date) + 1) AS dutyDays
    FROM crew_seafarer_service_qualification_info
    WHERE delete_flag = '0' AND duty_id = #{applyDutyId}
    GROUP BY seafarer_id
) duty_stats ON a.seafarer_id = duty_stats.seafarer_id

-- 任该类型船舶时间统计
LEFT JOIN (
    SELECT
        q.seafarer_id,
        SUM(DATEDIFF(IFNULL(q.down_board_date, CURDATE()), q.on_board_date) + 1) AS vesselTypeDays
    FROM crew_seafarer_service_qualification_info q
    JOIN common_vessel v ON q.vessel_id = v.vessel_id
    WHERE q.delete_flag = '0' AND v.vessel_type_flag = #{targetVesselTypeFlag}
    GROUP BY q.seafarer_id
) vessel_type_stats ON a.seafarer_id = vessel_type_stats.seafarer_id

```

#### 搭档统计查询（getPartnerStats）
```sql
SELECT
    SUM(DATEDIFF(CURDATE(), partner.enter_company_date)) AS partnerCompanyDays,
    SUM(IFNULL(duty_exp.dutyDays, 0)) AS partnerDutyDays,
    SUM(IFNULL(vessel_exp.vesselTypeDays, 0)) AS partnerVesselTypeDays
FROM crew_seafarer_info partner

-- 搭档职务经验统计
LEFT JOIN (
    SELECT
        seafarer_id,
        SUM(DATEDIFF(IFNULL(down_board_date, CURDATE()), on_board_date) + 1) AS dutyDays
    FROM crew_seafarer_service_qualification_info
    WHERE delete_flag = '0'
    GROUP BY seafarer_id
) duty_exp ON partner.seafarer_id = duty_exp.seafarer_id

-- 搭档船舶类型经验统计
LEFT JOIN (
    SELECT
        q.seafarer_id,
        SUM(DATEDIFF(IFNULL(q.down_board_date, CURDATE()), q.on_board_date) + 1) AS vesselTypeDays
    FROM crew_seafarer_service_qualification_info q
    JOIN common_vessel v ON q.vessel_id = v.vessel_id
    WHERE q.delete_flag = '0' AND v.vessel_type_flag = #{targetVesselTypeFlag}
    GROUP BY q.seafarer_id
) vessel_exp ON partner.seafarer_id = vessel_exp.seafarer_id

WHERE partner.delete_flag = '0'
AND partner.status_key = 'ONBOARD'
AND partner.apply_duty_name = #{partnerDutyName}
```

#### 应用层职务组合计算
```java
// 职务搭档关系映射
Map<String, String> DUTY_PARTNER_MAP = {
    "轮机长" -> "大管轮", "大管轮" -> "轮机长",
    "船长" -> "大副", "大副" -> "船长",
    "二管轮" -> "三管轮", "三管轮" -> "二管轮",
    "二副" -> "三副", "三副" -> "二副"
};

// 组合统计计算
groupCompanyYears = (candidateCompanyDays + partnerCompanyDays) / 360.0;
groupDutyYears = (candidateDutyDays + partnerDutyDays) / 360.0;
groupVesselTypeYears = (candidateVesselTypeDays + partnerVesselTypeDays) / 360.0;
```

#### 职务组合统计（getOnBoardCrews）
```sql
-- 职务组合统计子查询
LEFT JOIN (
    SELECT 
        vessel_id,
        apply_duty_name,
        SUM(DATEDIFF(CURDATE(), enter_company_date)) AS groupCompanyDays,
        SUM(IFNULL(duty_exp.dutyDays, 0)) AS groupDutyDays,
        SUM(IFNULL(vessel_type_exp.vesselTypeDays, 0)) AS groupVesselTypeDays
    FROM crew_seafarer_info crew
    WHERE crew.delete_flag = '0' 
    AND crew.status_key = 'ONBOARD'
    AND crew.vessel_id = a.vessel_id
    AND (
        -- 职务组合匹配逻辑
        (crew.apply_duty_name IN ('轮机长', '大管轮') AND a.apply_duty_name IN ('轮机长', '大管轮'))
        OR (crew.apply_duty_name IN ('船长', '大副') AND a.apply_duty_name IN ('船长', '大副'))
        OR (crew.apply_duty_name IN ('二管轮', '三管轮') AND a.apply_duty_name IN ('二管轮', '三管轮'))
        OR (crew.apply_duty_name IN ('二副', '三副') AND a.apply_duty_name IN ('二副', '三副'))
    )
    GROUP BY vessel_id, apply_duty_name
) group_stats ON a.vessel_id = group_stats.vessel_id AND a.apply_duty_name = group_stats.apply_duty_name
```

## 数据流向

```
请求参数 → getOnBoardCrews (职务组合统计) → getAvailableCrews (个人统计) → 返回结果
    ↓                    ↓                           ↓
获取目标船舶类型    计算组合经验统计           计算个人经验统计
```

## 接口返回示例

### 在船船员信息（OnBoardSeafarerDTO）
```json
{
  "seafarerId": "12345",
  "seafarerName": "张三",
  "applyDutyName": "轮机长"
  // 注：在船船员不显示统计字段
}
```

### 候选船员信息（CandidateSeafarerDTO）
```json
{
  "seafarerId": "67890",
  "seafarerName": "李四",
  "applyDutyName": "大管轮",
  "companyYears": 2.53,              // 入司时间（年）
  "dutyYears": 1.67,                 // 任该职务年限（年）
  "vesselTypeYears": 1.11,           // 任该类型船舶时间（年）
  "groupCompanyYears": 7.61,         // 职务组合入司时间合计（年）
  "groupDutyYears": 4.94,            // 职务组合任该职务年限合计（年）
  "groupVesselTypeYears": 3.33,      // 职务组合任该类型船舶时间合计（年）
  "partnerDutyName": "轮机长",        // 搭档职务名称
  "partnerSeafarerName": "张三, 王五（2人）"  // 搭档船员姓名和人数
}
```

### 搭档信息状态说明
- **正常情况**: "张三, 王五（2人）" - 显示搭档姓名和总人数
- **无搭档**: "暂无在船搭档" - 当前没有对应职务的在船船员
- **查询失败**: "查询搭档信息失败" - 系统异常时的提示
- **无搭档关系**: "无搭档职务" - 该职务不在4个组合范围内

## 部署状态

- ✅ 编译通过
- ✅ SQL逻辑完整
- ✅ DTO字段映射完成
- ✅ 跨数据源查询正常
- 🔄 待测试验证

## 注意事项

1. **性能考虑**: 统计查询可能影响性能，建议监控查询时间
2. **数据准确性**: 依赖`crew_seafarer_service_qualification_info`表数据的完整性
3. **船舶类型匹配**: 需要确保`common_vessel.vessel_type_flag`字段数据准确
4. **职务组合逻辑**: 基于职务名称字符串匹配，需要确保职务名称标准化
