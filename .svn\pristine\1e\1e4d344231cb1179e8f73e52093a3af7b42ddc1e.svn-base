# 数据库配置说明

## ✅ 当前配置状态

**项目已配置为使用明文配置，无需设置环境变量！**

所有配置文件（application-dev.yml、application-test.yml、application-prod.yml）都已改为明文配置，可以直接使用。

## 🔧 环境变量说明（备用）

如果将来需要使用环境变量，项目支持Spring Boot的环境变量占位符语法：`${VARIABLE_NAME:default_value}`

- `VARIABLE_NAME`：环境变量名称
- `default_value`：如果环境变量不存在时使用的默认值

## 📋 数据库相关环境变量

### 通用数据库配置
```bash
# 数据库服务器配置
export DB_HOST=localhost                    # 数据库主机地址
export DB_PORT=3306                        # 数据库端口

# 船管系统数据库
export DB_CREW_USERNAME=root               # 船管数据库用户名
export DB_CREW_PASSWORD=123456             # 船管数据库密码

# 航次管理数据库
export DB_VOYAGE_USERNAME=root             # 航次数据库用户名
export DB_VOYAGE_PASSWORD=123456           # 航次数据库密码

# 货物管理数据库
export DB_CARGO_USERNAME=root              # 货物数据库用户名
export DB_CARGO_PASSWORD=123456            # 货物数据库密码

# 财务管理数据库
export DB_FINANCE_USERNAME=root            # 财务数据库用户名
export DB_FINANCE_PASSWORD=123456          # 财务数据库密码
```

### JWT安全配置
```bash
# JWT配置
export JWT_SECRET=myProductionSecretKey123456789012345678901234567890
export JWT_EXPIRATION=604800                 # 令牌过期时间（秒）
```

## 🌍 不同环境的配置方式

### 开发环境（application-dev.yml）
开发环境通常直接使用明文配置，无需设置环境变量：
```yaml
datasources:
  crew:
    username: root                          # 直接使用明文
    password: 123456                        # 直接使用明文
```

### 测试环境（application-test.yml）
测试环境使用环境变量，提供默认值：
```yaml
datasources:
  crew:
    username: ${DB_CREW_USERNAME:test_user}  # 环境变量或默认值
    password: ${DB_CREW_PASSWORD:test_password}
```

### 生产环境（application-prod.yml）
生产环境必须使用环境变量，不提供默认密码：
```yaml
datasources:
  crew:
    username: ${DB_CREW_USERNAME:crew_user}  # 环境变量或默认用户名
    password: ${DB_CREW_PASSWORD:}           # 环境变量，无默认值（安全）
```

## 🚀 设置环境变量的方法

### 方法1：系统环境变量（推荐生产环境）

#### Linux/Mac
```bash
# 临时设置（当前会话）
export DB_CREW_USERNAME=myuser
export DB_CREW_PASSWORD=mypassword

# 永久设置（添加到 ~/.bashrc 或 ~/.zshrc）
echo 'export DB_CREW_USERNAME=myuser' >> ~/.bashrc
echo 'export DB_CREW_PASSWORD=mypassword' >> ~/.bashrc
source ~/.bashrc
```

#### Windows
```cmd
# 临时设置（当前会话）
set DB_CREW_USERNAME=myuser
set DB_CREW_PASSWORD=mypassword

# 永久设置（系统环境变量）
setx DB_CREW_USERNAME myuser
setx DB_CREW_PASSWORD mypassword
```

### 方法2：IDE配置

#### IntelliJ IDEA
1. 打开 Run/Debug Configurations
2. 选择 Application
3. 在 Environment variables 中添加：
   ```
   DB_CREW_USERNAME=root
   DB_CREW_PASSWORD=123456
   ```

#### VS Code
在 `.vscode/launch.json` 中添加：
```json
{
  "configurations": [
    {
      "env": {
        "DB_CREW_USERNAME": "root",
        "DB_CREW_PASSWORD": "123456"
      }
    }
  ]
}
```

### 方法3：Docker环境变量

#### docker-compose.yml
```yaml
services:
  app:
    environment:
      - DB_CREW_USERNAME=root
      - DB_CREW_PASSWORD=123456
      - JWT_SECRET=mySecretKey
```

#### Dockerfile
```dockerfile
ENV DB_CREW_USERNAME=root
ENV DB_CREW_PASSWORD=123456
```

### 方法4：Spring Boot启动参数
```bash
# 方式1：命令行参数
java -jar app.jar --spring.datasource.username=root --spring.datasource.password=123456

# 方式2：JVM系统属性
java -DDB_CREW_USERNAME=root -DDB_CREW_PASSWORD=123456 -jar app.jar

# 方式3：环境变量
DB_CREW_USERNAME=root DB_CREW_PASSWORD=123456 java -jar app.jar
```

## 🔒 安全最佳实践

### 1. 密码安全
- ✅ 生产环境必须使用环境变量
- ✅ 不要在代码中硬编码密码
- ✅ 使用强密码
- ✅ 定期更换密码

### 2. 环境变量管理
- ✅ 使用密钥管理服务（如 AWS Secrets Manager、Azure Key Vault）
- ✅ 限制环境变量的访问权限
- ✅ 不要在日志中输出敏感信息
- ✅ 使用加密存储敏感配置

### 3. 配置验证
```bash
# 验证环境变量是否设置
echo $DB_CREW_USERNAME
echo $DB_CREW_PASSWORD

# 检查应用启动日志
tail -f logs/application.log | grep -i "datasource\|connection"
```

## 📝 配置模板

### 开发环境配置模板
```bash
# 开发环境环境变量（可选）
export DB_HOST=localhost
export DB_PORT=3306
export DB_CREW_USERNAME=root
export DB_CREW_PASSWORD=123456
export JWT_SECRET=devSecretKey123456789012345678901234567890
```

### 生产环境配置模板
```bash
# 生产环境环境变量（必需）
export DB_HOST=prod-db-server.example.com
export DB_PORT=3306
export DB_CREW_USERNAME=prod_crew_user
export DB_CREW_PASSWORD=your_secure_password_here
export DB_VOYAGE_USERNAME=prod_voyage_user
export DB_VOYAGE_PASSWORD=your_secure_password_here
export JWT_SECRET=your_production_jwt_secret_key_here
export JWT_EXPIRATION=604800
```

## 🎯 快速配置

如果您想直接使用明文配置而不使用环境变量，可以：

1. **修改配置文件**：将 `${DB_CREW_USERNAME:crew_user}` 直接改为 `root`
2. **设置环境变量**：按照上述方法设置对应的环境变量
3. **使用默认值**：如果环境变量未设置，系统会使用冒号后的默认值

推荐在开发环境使用明文配置，在生产环境使用环境变量配置。
