package com.example.multidatasource.controller;

import com.example.multidatasource.model.SqlRequest;
import com.example.multidatasource.model.SqlResponse;
import com.example.multidatasource.service.SqlExecutorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * SQL执行控制器
 */
@Slf4j
@RestController
@RequestMapping("/sql")
@Tag(name = "SQL执行器", description = "多数据源SQL执行API")
@Validated
public class SqlExecutorController {
    
    @Autowired
    private SqlExecutorService sqlExecutorService;
    
    @PostMapping("/execute")
    @Operation(summary = "执行SQL语句", description = "在指定数据源上执行SQL语句")
    public ResponseEntity<SqlResponse> executeSql(
            @Valid @RequestBody SqlRequest request) {
        
        log.info("收到SQL执行请求 - 数据源: {}, SQL: {}", 
            request.getDataSourceName(), request.getSql());
        
        SqlResponse response = sqlExecutorService.executeQuery(request);
        
        if (Boolean.TRUE.equals(response.getSuccess())) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    @PostMapping("/query")
    @Operation(summary = "执行查询语句", description = "执行SELECT查询语句")
    public ResponseEntity<SqlResponse> executeQuery(
            @Parameter(description = "数据源名称") @RequestParam String dataSourceName,
            @Parameter(description = "SQL查询语句") @RequestParam String sql,
            @Parameter(description = "页码(从1开始)") @RequestParam(required = false) Integer page,
            @Parameter(description = "每页大小") @RequestParam(required = false) Integer size,
            @Parameter(description = "是否返回总数") @RequestParam(required = false, defaultValue = "false") Boolean needCount) {
        
        SqlRequest request = new SqlRequest();
        request.setDataSourceName(dataSourceName);
        request.setSql(sql);
        request.setQueryType("SELECT");
        request.setPage(page);
        request.setSize(size);
        request.setNeedCount(needCount);
        
        return executeSql(request);
    }
    
    @PostMapping("/update")
    @Operation(summary = "执行更新语句", description = "执行INSERT/UPDATE/DELETE语句")
    public ResponseEntity<SqlResponse> executeUpdate(
            @Parameter(description = "数据源名称") @RequestParam String dataSourceName,
            @Parameter(description = "SQL更新语句") @RequestParam String sql) {
        
        SqlRequest request = new SqlRequest();
        request.setDataSourceName(dataSourceName);
        request.setSql(sql);
        
        // 根据SQL语句判断类型
        String sqlUpper = sql.trim().toUpperCase();
        if (sqlUpper.startsWith("INSERT")) {
            request.setQueryType("INSERT");
        } else if (sqlUpper.startsWith("UPDATE")) {
            request.setQueryType("UPDATE");
        } else if (sqlUpper.startsWith("DELETE")) {
            request.setQueryType("DELETE");
        } else {
            request.setQueryType("UPDATE");
        }
        
        return executeSql(request);
    }
}
