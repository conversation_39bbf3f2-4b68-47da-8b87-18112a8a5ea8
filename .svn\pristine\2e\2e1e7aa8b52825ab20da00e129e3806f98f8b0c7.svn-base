package com.example.multidatasource.voyage.controller;

import com.example.multidatasource.model.ApiResponse;
import com.example.multidatasource.common.dto.PageResult;
import com.example.multidatasource.voyage.dto.MaritimeCrawlRequestDTO;
import com.example.multidatasource.voyage.dto.MaritimeCrawlResultDTO;
import com.example.multidatasource.voyage.dto.MaritimeQueryDTO;
import com.example.multidatasource.voyage.entity.MaritimeBureauConfig;
import com.example.multidatasource.voyage.entity.MaritimeInfo;
import com.example.multidatasource.voyage.service.MaritimeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 海事信息管理Controller
 */
@Slf4j
@RestController
@RequestMapping("/voyage/maritime")
@Tag(name = "海事信息管理", description = "海事信息爬取、查询和配置管理相关API")
public class MaritimeController {

    @Autowired
    private MaritimeService maritimeService;

    @PostMapping("/crawl")
    @Operation(summary = "爬取海事信息", 
               description = "根据指定条件爬取海事局的航行警告和通告信息")
    public ApiResponse<MaritimeCrawlResultDTO> crawlMaritimeInfo(
            @Valid @RequestBody MaritimeCrawlRequestDTO request) {
        try {
            MaritimeCrawlResultDTO result = maritimeService.crawlMaritimeInfo(request);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("爬取海事信息失败", e);
            return ApiResponse.error("爬取海事信息失败: " + e.getMessage());
        }
    }

    @GetMapping("/list")
    @Operation(summary = "分页查询海事信息", 
               description = "根据条件分页查询海事信息列表")
    public ApiResponse<PageResult<MaritimeInfo>> queryMaritimeInfo(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "海事局名称（可选）") @RequestParam(required = false) String bureauName,
            @Parameter(description = "信息类型（可选）：ALARM-航行警告，NOTICE-航行通告") @RequestParam(required = false) String infoType,
            @Parameter(description = "开始日期（可选）", example = "2025-08-01") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束日期（可选）", example = "2025-08-13") @RequestParam(required = false) String endDate,
            @Parameter(description = "关键词搜索（可选）") @RequestParam(required = false) String keyword) {
        try {
            MaritimeQueryDTO query = new MaritimeQueryDTO();
            query.setPage(page);
            query.setSize(size);
            query.setBureauName(bureauName);
            query.setInfoType(infoType);
            query.setKeyword(keyword);
            
            // 处理日期参数
            if (startDate != null && !startDate.trim().isEmpty()) {
                query.setStartDate(java.time.LocalDate.parse(startDate));
            }
            if (endDate != null && !endDate.trim().isEmpty()) {
                query.setEndDate(java.time.LocalDate.parse(endDate));
            }
            
            PageResult<MaritimeInfo> result = maritimeService.queryMaritimeInfo(query);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("查询海事信息失败", e);
            return ApiResponse.error("查询海事信息失败: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取海事信息详情", 
               description = "根据ID查询海事信息详细内容")
    public ApiResponse<MaritimeInfo> getMaritimeInfoById(
            @Parameter(description = "海事信息ID") @PathVariable Long id) {
        try {
            MaritimeInfo result = maritimeService.getMaritimeInfoById(id);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("查询海事信息详情失败，ID: {}", id, e);
            return ApiResponse.error("查询海事信息详情失败: " + e.getMessage());
        }
    }

    @GetMapping("/bureau/list")
    @Operation(summary = "查询所有海事局配置", 
               description = "获取所有海事局的配置信息")
    public ApiResponse<List<MaritimeBureauConfig>> getAllBureauConfigs() {
        try {
            List<MaritimeBureauConfig> result = maritimeService.getAllBureauConfigs();
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("查询海事局配置失败", e);
            return ApiResponse.error("查询海事局配置失败: " + e.getMessage());
        }
    }

    @GetMapping("/bureau/{id}")
    @Operation(summary = "获取海事局配置详情", 
               description = "根据ID查询海事局配置详细信息")
    public ApiResponse<MaritimeBureauConfig> getBureauConfigById(
            @Parameter(description = "海事局配置ID") @PathVariable Long id) {
        try {
            MaritimeBureauConfig result = maritimeService.getBureauConfigById(id);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("查询海事局配置详情失败，ID: {}", id, e);
            return ApiResponse.error("查询海事局配置详情失败: " + e.getMessage());
        }
    }

    @PostMapping("/bureau")
    @Operation(summary = "新增海事局配置", 
               description = "创建新的海事局配置")
    public ApiResponse<MaritimeBureauConfig> saveBureauConfig(
            @Valid @RequestBody MaritimeBureauConfig config) {
        try {
            MaritimeBureauConfig result = maritimeService.saveBureauConfig(config);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("保存海事局配置失败", e);
            return ApiResponse.error("保存海事局配置失败: " + e.getMessage());
        }
    }

    @PutMapping("/bureau/{id}")
    @Operation(summary = "更新海事局配置", 
               description = "根据ID更新海事局配置信息")
    public ApiResponse<MaritimeBureauConfig> updateBureauConfig(
            @Parameter(description = "海事局配置ID") @PathVariable Long id,
            @Valid @RequestBody MaritimeBureauConfig config) {
        try {
            MaritimeBureauConfig result = maritimeService.updateBureauConfig(id, config);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("更新海事局配置失败，ID: {}", id, e);
            return ApiResponse.error("更新海事局配置失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/bureau/{id}")
    @Operation(summary = "删除海事局配置", 
               description = "根据ID删除海事局配置")
    public ApiResponse<Void> deleteBureauConfig(
            @Parameter(description = "海事局配置ID") @PathVariable Long id) {
        try {
            maritimeService.deleteBureauConfig(id);
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("删除海事局配置失败，ID: {}", id, e);
            return ApiResponse.error("删除海事局配置失败: " + e.getMessage());
        }
    }
}
