<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper">

    <!-- 结果映射：航次油耗汇总DTO -->
    <resultMap id="VoyageConsumptionSummaryMap" type="com.example.multidatasource.crew.dto.VoyageConsumptionSummaryDTO">
        <result column="vessel_id" property="vesselId"/>
        <result column="vessel_name" property="vesselName"/>
        <result column="fill_date" property="fillDate"/>
        <result column="consum_id" property="consumId"/>
        <result column="mmsi_code" property="mmsiCode"/>
        <collection property="detailList" ofType="com.example.multidatasource.crew.dto.VoyageConsumptionSummaryDTO$OilConsumptionDetailDTO">
            <result column="oil_id" property="oilId"/>
            <result column="oil_name_cn" property="oilNameCn"/>
            <result column="oil_name_en" property="oilNameEn"/>
            <result column="oil_mark" property="oilMark"/>
            <result column="oil_type_id" property="oilTypeId"/>
            <result column="fuel_type_id" property="fuelTypeId"/>
            <result column="oil_unit_id" property="oilUnitId"/>
            <result column="oil_unit_name" property="oilUnitName"/>
            <result column="last_voyage_inventory" property="lastVoyageInventory"/>
            <result column="this_voyage_add" property="thisVoyageAdd"/>
            <result column="this_voyage_consum" property="thisVoyageConsum"/>
            <result column="this_voyage_inventory" property="thisVoyageInventory"/>
        </collection>
    </resultMap>



    <!-- 获取每个船舶最新的航次油耗记录（MySQL 5.7兼容版本） -->
    <select id="getLatestVoyageConsumptionByVessel" resultMap="VoyageConsumptionSummaryMap">
        SELECT
            latest.vessel_id,
            latest.vessel_name,
            latest.fill_date,
            latest.consum_id,
            latest.mmsi_code,
            detail.oil_id,
            detail.oil_name_cn,
            detail.oil_name_en,
            detail.oil_mark,
            detail.oil_type_id,
            detail.fuel_type_id,
            detail.oil_unit_id,
            detail.oil_unit_name,
            detail.last_voyage_inventory,
            detail.this_voyage_add,
            detail.this_voyage_consum,
            detail.this_voyage_inventory
        FROM (
            SELECT
                t1.vessel_id,
                t1.vessel_name,
                t1.fill_date,
                t1.consum_id,
                sh.mmsi_code
            FROM oil_voyage_consumption_info t1
            INNER JOIN common_vessel sh ON t1.vessel_id = sh.vessel_id
            INNER JOIN (
                SELECT
                    ci.vessel_id,
                    MAX(ci.fill_date) AS max_fill_date
                FROM oil_voyage_consumption_info ci
                INNER JOIN common_vessel vs ON vs.vessel_id = ci.vessel_id
                WHERE ci.delete_flag = '0' AND ci.status_flag != '99'
                <if test="vesselId != null and vesselId != ''">
                    AND ci.vessel_id = #{vesselId}
                </if>
                <if test="vesselName != null and vesselName != ''">
                    AND ci.vessel_name LIKE CONCAT('%', #{vesselName}, '%')
                </if>
                <if test="mmsiCode != null and mmsiCode != ''">
                    AND vs.mmsi_code LIKE CONCAT('%', #{mmsiCode}, '%')
                </if>
                <if test="startDate != null and startDate != ''">
                    AND ci.fill_date &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate != ''">
                    AND ci.fill_date &lt;= #{endDate}
                </if>
                GROUP BY ci.vessel_id
            ) t2 ON t1.vessel_id = t2.vessel_id AND t1.fill_date = t2.max_fill_date
            WHERE t1.delete_flag = '0' AND t1.status_flag != '99'
            <if test="vesselId != null and vesselId != ''">
                AND t1.vessel_id = #{vesselId}
            </if>
            <if test="vesselName != null and vesselName != ''">
                AND t1.vessel_name LIKE CONCAT('%', #{vesselName}, '%')
            </if>
            <if test="mmsiCode != null and mmsiCode != ''">
                AND sh.mmsi_code LIKE CONCAT('%', #{mmsiCode}, '%')
            </if>
            <if test="startDate != null and startDate != ''">
                AND t1.fill_date &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND t1.fill_date &lt;= #{endDate}
            </if>
        ) latest
        INNER JOIN oil_voyage_consumption_detail detail ON latest.consum_id = detail.consum_id
        WHERE detail.delete_flag = '0'
        AND (detail.oil_name_cn = '轻油' OR detail.oil_name_cn = '重油')
        ORDER BY latest.vessel_id, detail.oil_name_cn
    </select>



</mapper>
