<component name="libraryTable">
  <library name="Maven: com.microsoft.sqlserver:mssql-jdbc:10.2.3.jre8">
    <CLASSES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/com/microsoft/sqlserver/mssql-jdbc/10.2.3.jre8/mssql-jdbc-10.2.3.jre8.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/com/microsoft/sqlserver/mssql-jdbc/10.2.3.jre8/mssql-jdbc-10.2.3.jre8-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://F:/learing/apache-maven-3.6.1/mavenRepository/com/microsoft/sqlserver/mssql-jdbc/10.2.3.jre8/mssql-jdbc-10.2.3.jre8-sources.jar!/" />
    </SOURCES>
  </library>
</component>