<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.multidatasource.voyage.mapper.ShipEngineMapper">

    <!-- 查询所有非国际船舶的主机/辅机信息 -->
    <select id="getAllShipEngineInfo" resultType="com.example.multidatasource.voyage.dto.ShipEngineInfo">
        SELECT 
            mmsi,
            main_engine_model AS mainEngineModel,
            main_engine_manufacturer AS mainEngineManufacturer,
            auxiliary_engine_model AS auxiliaryEngineModel,
            auxiliary_engine_manufacturer AS auxiliaryEngineManufacturer
        FROM t_sh_ship 
        WHERE business_model != 3  -- 排除国际船舶
        AND mmsi IS NOT NULL       -- MMSI码不为空
        AND mmsi != ''             -- MMSI码不为空字符串
        ORDER BY mmsi              -- 按MMSI码排序，便于调试和查看
    </select>

</mapper>
