package com.example.multidatasource.crew.service.impl;

import com.example.multidatasource.common.config.DataSourceContextHolder;
import com.example.multidatasource.common.annotation.DataSource;
import com.example.multidatasource.crew.dto.VoyageConsumptionSummaryDTO;
import com.example.multidatasource.crew.mapper.OilVoyageConsumptionMapper;
import com.example.multidatasource.crew.service.OilVoyageConsumptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 航次油耗信息服务实现类
 */
@Slf4j
@Service
@DataSource(DataSourceContextHolder.DataSourceType.CREW)
public class OilVoyageConsumptionServiceImpl implements OilVoyageConsumptionService {

    @Autowired
    private OilVoyageConsumptionMapper oilVoyageConsumptionMapper;

    @Override
    public List<VoyageConsumptionSummaryDTO> getLatestVoyageConsumptionByVessel(
            String vesselId, String vesselName, String mmsiCode, String startDate, String endDate) {
        log.info("获取每个船舶最新的航次油耗记录（包含轻油和重油明细）- vesselId: {}, vesselName: {}, startDate: {}, endDate: {}",
                vesselId, vesselName, startDate, endDate);
        try {
            List<VoyageConsumptionSummaryDTO> result = oilVoyageConsumptionMapper.getLatestVoyageConsumptionByVessel(
                    vesselId, vesselName, mmsiCode, startDate, endDate);
            log.info("成功获取{}条船舶最新航次油耗记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取船舶最新航次油耗记录失败", e);
            throw new RuntimeException("获取船舶最新航次油耗记录失败: " + e.getMessage());
        }
    }
}
