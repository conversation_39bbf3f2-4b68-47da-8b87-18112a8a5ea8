<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.multidatasource.crew.mapper.CrewMapper">

    <!-- 结果映射 -->
    <resultMap id="CrewInfoResultMap" type="com.example.multidatasource.crew.entity.CrewInfo">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="position" property="position"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="status" property="status"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <!-- 查询所有船员信息（分页） -->
    <select id="selectCrewList" resultMap="CrewInfoResultMap">
        SELECT * FROM crew_seafarer_info 
        ORDER BY created_time DESC 
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据ID查询船员信息 -->
    <select id="selectCrewById" resultMap="CrewInfoResultMap">
        SELECT * FROM crew_seafarer_info WHERE seafarer_id = #{id}
    </select>

    <!-- 插入船员信息 -->
    <insert id="insertCrew" parameterType="com.example.multidatasource.crew.entity.CrewInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO crew_seafarer_info (name, position, phone, email, status, created_time, updated_time)
        VALUES (#{name}, #{position}, #{phone}, #{email}, #{status}, NOW(), NOW())
    </insert>

    <!-- 更新船员信息 -->
    <update id="updateCrew" parameterType="com.example.multidatasource.crew.entity.CrewInfo">
        UPDATE crew_seafarer_info 
        SET name = #{name}, 
            position = #{position}, 
            phone = #{phone}, 
            email = #{email}, 
            status = #{status}, 
            updated_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 删除船员信息 -->
    <delete id="deleteCrewById">
        DELETE FROM crew_seafarer_info WHERE id = #{id}
    </delete>

    <!-- 根据状态查询船员列表 -->
    <select id="selectCrewByStatus" resultMap="CrewInfoResultMap">
        SELECT * FROM crew_seafarer_info WHERE status = #{status} ORDER BY created_time DESC
    </select>

    <!-- 根据职位查询船员列表 -->
    <select id="selectCrewByPosition" resultMap="CrewInfoResultMap">
        SELECT * FROM crew_seafarer_info WHERE position = #{position} ORDER BY created_time DESC
    </select>

    <!-- 统计船员总数 -->
    <select id="countCrew" resultType="int">
        SELECT COUNT(*) FROM crew_seafarer_info
    </select>

    <!-- 根据姓名模糊查询船员 -->
    <select id="selectCrewByName" resultMap="CrewInfoResultMap">
        SELECT * FROM crew_seafarer_info 
        WHERE name LIKE CONCAT('%', #{name}, '%')
        ORDER BY created_time DESC
    </select>

    <!-- 根据多个条件查询船员 -->
    <select id="selectCrewByConditions" resultMap="CrewInfoResultMap">
        SELECT * FROM crew_seafarer_info 
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="position != null and position != ''">
                AND position = #{position}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="phone != null and phone != ''">
                AND phone LIKE CONCAT('%', #{phone}, '%')
            </if>
        </where>
        ORDER BY created_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 统计符合条件的船员总数 -->
    <select id="countCrewByConditions" resultType="int">
        SELECT COUNT(*) FROM crew_seafarer_info 
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="position != null and position != ''">
                AND position = #{position}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="phone != null and phone != ''">
                AND phone LIKE CONCAT('%', #{phone}, '%')
            </if>
        </where>
    </select>

    <!-- 获取所有职位列表 -->
    <select id="selectAllPositions" resultType="string">
        SELECT DISTINCT position FROM crew_seafarer_info WHERE position IS NOT NULL ORDER BY position
    </select>

    <!-- 批量更新船员状态 -->
    <update id="batchUpdateCrewStatus">
        UPDATE crew_seafarer_info SET status = #{status}, updated_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 执行自定义SQL查询 -->
    <select id="executeCustomQuery" resultType="map">
        ${sql}
    </select>

    <!-- 执行自定义SQL更新 -->
    <update id="executeCustomUpdate">
        ${sql}
    </update>

</mapper>
