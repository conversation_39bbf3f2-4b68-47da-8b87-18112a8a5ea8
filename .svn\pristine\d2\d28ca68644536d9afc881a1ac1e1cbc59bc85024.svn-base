<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b2cd344a-2f19-44b4-9adb-0fccee262d2f" name="Changes" comment="添加排期候选人导出功能">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/crew/service/impl/SeafarerScheduleServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/multidatasource/crew/service/impl/SeafarerScheduleServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/mapper/voyage/VoyageMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/mapper/voyage/VoyageMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="F:\learing\apache-maven-3.6.1\mavenRepository" />
        <option name="mavenHome" value="F:/learing/apache-maven-3.6.1/apache-maven-3.6.1" />
        <option name="useMavenConfig" value="true" />
        <option name="userSettingsFile" value="F:\learing\apache-maven-3.6.1\apache-maven-3.6.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="workspaceImportEnabled" value="true" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="ProjectId" id="31E3kCFBE8Oj1g5Q1DeLX6QRJbu" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="false" />
    <option name="showLibraryContents" value="true" />
    <option name="showScratchesAndConsoles" value="false" />
    <option name="showVisibilityIcons" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/augmentSpace&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Global Libraries&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;reference.settingsdialog.IDE.editor.colors.General&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;96e4b2703bc3d3c2b2f618cf5e956881&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="MultiDataSourceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="multi-datasource-api" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.multidatasource.MultiDataSourceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\augmentSpace" />
          <option name="myCopyRoot" value="D:\augmentSpace" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\augmentSpace" />
          <option name="myCopyRoot" value="D:\augmentSpace" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b2cd344a-2f19-44b4-9adb-0fccee262d2f" name="Changes" comment="" />
      <created>1755075550203</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755075550203</updated>
      <workItem from="1755075553439" duration="2606000" />
      <workItem from="1755078220646" duration="13568000" />
      <workItem from="1755565966519" duration="3335000" />
      <workItem from="1755575507732" duration="13000" />
      <workItem from="1755595331344" duration="4081000" />
      <workItem from="1755748806387" duration="20487000" />
      <workItem from="1756081448089" duration="3333000" />
      <workItem from="1756087795510" duration="2431000" />
      <workItem from="1756091171756" duration="7891000" />
      <workItem from="1756112173304" duration="3996000" />
    </task>
    <task id="LOCAL-00001" summary="海事局定时任务完成后！">
      <created>1755131150123</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1755131150123</updated>
    </task>
    <task id="LOCAL-00002" summary="海事局定时任务完成后！">
      <created>1755220390902</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1755220390902</updated>
    </task>
    <task id="LOCAL-00003" summary="海事局定时任务完成后！">
      <created>1755221352705</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1755221352705</updated>
    </task>
    <task id="LOCAL-00004" summary="海事局定时任务完成后！">
      <created>1755222912291</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1755222912291</updated>
    </task>
    <task id="LOCAL-00005" summary="海事局定时任务完成后！">
      <created>1755509250788</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1755509250788</updated>
    </task>
    <task id="LOCAL-00006" summary="海事局定时任务完成后！">
      <created>1755761431218</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1755761431218</updated>
    </task>
    <task id="LOCAL-00007" summary="修复时间因为格式刷问题出错问题">
      <created>1755762589705</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1755762589705</updated>
    </task>
    <task id="LOCAL-00008" summary="将所有获选人都添加福建省计算比例，去除以此作为筛选条件">
      <created>1755765114846</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1755765114846</updated>
    </task>
    <task id="LOCAL-00009" summary="优化评语后提交">
      <created>1755834911536</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1755834911536</updated>
    </task>
    <task id="LOCAL-00010" summary="将所有获选人都添加福建省计算比例，去除以此作为筛选条件">
      <created>1755844775527</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1755844775527</updated>
    </task>
    <task id="LOCAL-00011" summary="获选人添加曾经任职船舶信息">
      <created>1756088241985</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1756088241985</updated>
    </task>
    <task id="LOCAL-00012" summary="添加排期候选人导出功能">
      <created>1756092066114</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1756092066114</updated>
    </task>
    <option name="localTasksCounter" value="13" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="海事局定时任务完成后！" />
    <MESSAGE value="修复时间因为格式刷问题出错问题" />
    <MESSAGE value="优化评语后提交" />
    <MESSAGE value="将所有获选人都添加福建省计算比例，去除以此作为筛选条件" />
    <MESSAGE value="获选人添加曾经任职船舶信息" />
    <MESSAGE value="添加排期候选人导出功能" />
    <option name="LAST_COMMIT_MESSAGE" value="添加排期候选人导出功能" />
  </component>
</project>