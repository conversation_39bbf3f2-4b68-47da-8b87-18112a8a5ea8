package com.example.multidatasource.voyage.dto;

import lombok.Data;

/**
 * 船舶引擎信息DTO
 * 
 * 用于存储船舶的主机和辅机相关信息，支持跨数据源查询和缓存
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class ShipEngineInfo {
    
    /**
     * 船舶MMSI码（唯一标识）
     */
    private String mmsi;
    
    /**
     * 主机型号
     */
    private String mainEngineModel;
    
    /**
     * 主机制造厂商
     */
    private String mainEngineManufacturer;
    
    /**
     * 辅机型号
     */
    private String auxiliaryEngineModel;
    
    /**
     * 辅机制造厂商
     */
    private String auxiliaryEngineManufacturer;
}
